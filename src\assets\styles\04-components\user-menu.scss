// Dropdown toggler
.dropdown__trigger {
  @apply rounded-full overflow-hidden w-10 h-10 font-medium focus:ring-offset-transparent cursor-pointer;

  &.filter{
    @apply rounded-none w-auto h-auto overflow-visible;
  }

  /* تحسينات للثيم الغامق */
  .gaming-theme & {
    @apply border border-purple-500/30 bg-gray-800/50;
    transition: all 0.3s ease;

    &:hover {
      @apply border-purple-400/50 bg-gray-700/60;
      box-shadow: 0 0 10px rgba(147, 51, 234, 0.3);
    }
  }
}

.dropdown__menu {
  @apply origin-top-right duration-200 transition-all scale-y-90 opacity-0 -translate-y-4 invisible z-50 w-80 lg:w-60 rounded-t-md lg:rounded-t-none rounded-b-md shadow-default bg-white lg:border-t lg:border-gray-300/30;
  outline: none;
  position: fixed !important;
  top: 100% !important;
  right: 0 !important;
  left: auto !important;
  max-height: 400px;
  overflow-y: auto;

  [dir="rtl"] & {
    right: auto !important;
    left: 0 !important;
  }

  /* تحسينات للثيم الغامق */
  .gaming-theme & {
    @apply bg-gray-800 border-purple-500 backdrop-blur-md;
    background-color: rgba(31, 41, 55, 0.95);
    border-color: rgba(147, 51, 234, 0.3);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5), 0 0 20px rgba(147, 51, 234, 0.2);

    .menu-item {
      @apply text-gray-200 hover:text-white;
      transition: all 0.3s ease;

      &:hover {
        background: linear-gradient(90deg, rgba(147, 51, 234, 0.2) 0%, rgba(79, 70, 229, 0.2) 100%);
      }
    }
  }
}

.dropdown-toggler {
  @apply inline-flex items-center lg:h-full w-10 mx-0 text-gray-500;
  position: relative !important;

  &.cat-filter{
    @apply static w-auto;

    .dropdown__trigger {
      @apply rounded-none w-auto h-auto overflow-visible;
    }
  }

  &:before {
    content: "";
    background: rgba(113, 113, 122, 0.75);
    @apply fixed w-screen h-screen left-0 top-0 opacity-0 pointer-events-none invisible duration-300 z-40;

    @media (max-width: 1024px) {
      background: rgba(0, 0, 0, 0.6);
      backdrop-filter: blur(4px);
    }
  }

  &.is-opened {
    .dropdown__menu {
      @apply opacity-100 visible translate-y-0 scale-100;
    }

    &:before {
      @apply opacity-100 visible pointer-events-auto;
    }
  }

  /* تحسينات للثيم الغامق */
  .gaming-theme & {
    @apply text-gray-300;

    &:hover {
      @apply text-white;
    }
  }

  @media (max-width: 1024px) {
    .dropdown__menu {
      position: fixed !important;
      left: 0 !important;
      right: 0 !important;
      bottom: 0 !important;
      top: auto !important;
      width: 100% !important;
      z-index: 9999 !important;
      max-height: 80vh !important;
      overflow-y: auto !important;
      border-radius: 20px 20px 0 0 !important;
      transform: translateY(100%) !important;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
      opacity: 1 !important;

      /* إضافة handle للسحب */
      &::before {
        content: '';
        position: absolute;
        top: 12px;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 4px;
        background: rgba(0, 0, 0, 0.3);
        border-radius: 2px;
        z-index: 1;
      }

      .menu-item{
        @apply rtl:pl-2.5 ltr:pr-2.5 py-4 text-lg;

        &:first-child {
          margin-top: 20px; /* مساحة للـ handle */
        }
      }

      /* تحسينات للثيم الغامق على الموبايل */
      .gaming-theme & {
        @apply bg-gray-900 border-t border-purple-500;
        background-color: rgba(17, 24, 39, 0.98);
        border-color: rgba(147, 51, 234, 0.3);
        backdrop-filter: blur(20px);

        &::before {
          background: rgba(147, 51, 234, 0.6);
          box-shadow: 0 0 8px rgba(147, 51, 234, 0.4);
        }

        .menu-item {
          @apply text-gray-200 border-b border-gray-700;
          border-color: rgba(75, 85, 99, 0.5);

          &:hover, &:active {
            @apply text-white;
            background-color: rgba(147, 51, 234, 0.2);
          }

          &:last-child {
            @apply border-b-0;
          }
        }
      }
    }

    &.is-opened {
      .dropdown__menu {
        @apply opacity-100;
        transform: translateY(0) !important;

        @media (max-width: 1024px) {
          transform: translateY(0) !important;
          opacity: 1 !important;
        }
      }

      &:before {
        @apply opacity-100 visible pointer-events-auto;
        background: rgba(0, 0, 0, 0.8);
      }
    }
  }
}

/* إضافة CSS خاص للموبايل لضمان ظهور القائمة من الأسفل */
@media (max-width: 1024px) {
  /* استهداف عنصر salla-user-menu مباشرة */
  salla-user-menu .dropdown__menu,
  .dropdown.user-menu .dropdown__menu {
    position: fixed !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    top: auto !important;
    width: 100vw !important;
    margin: 0 !important;
    border-radius: 20px 20px 0 0 !important;
    transform: translateY(100%) !important;
    transition: transform 0.3s ease-in-out !important;
    z-index: 10000 !important;
    max-height: 80vh !important;
    overflow-y: auto !important;
  }

  /* الحالة المفتوحة */
  salla-user-menu .dropdown-toggler.is-opened .dropdown__menu,
  .dropdown.user-menu.is-opened .dropdown__menu {
    transform: translateY(0) !important;
  }

  /* إضافة تأثير الخلفية */
  salla-user-menu .dropdown-toggler.is-opened:before {
    background: rgba(0, 0, 0, 0.8) !important;
  }

  /* تأكيد إضافي للتموضع */
  salla-user-menu {
    .dropdown-toggler {
      position: relative !important;

      .dropdown__menu {
        position: fixed !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        top: auto !important;
        width: 100vw !important;
        margin: 0 !important;
        border-radius: 20px 20px 0 0 !important;
        transform: translateY(100%) !important;
        transition: transform 0.3s ease-in-out !important;
        z-index: 10000 !important;
        max-height: 80vh !important;
        overflow-y: auto !important;

        /* إضافة كلاس للموبايل */
        &.mobile-bottom-menu {
          position: fixed !important;
          bottom: 0 !important;
          left: 0 !important;
          right: 0 !important;
          top: auto !important;
          width: 100vw !important;
          transform: translateY(100%) !important;
        }

        &.mobile-opened {
          transform: translateY(0) !important;
        }
      }

      &.is-opened .dropdown__menu {
        transform: translateY(0) !important;
      }
    }
  }
}

/* CSS إضافي لضمان عمل القائمة على الموبايل */
@media (max-width: 1024px) {
  /* استهداف جميع الاحتمالات */
  salla-user-menu .dropdown__menu,
  salla-user-menu .dropdown-toggler .dropdown__menu,
  .user-menu .dropdown__menu,
  [class*="user"] .dropdown__menu {
    position: fixed !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    top: auto !important;
    width: 100vw !important;
    height: auto !important;
    max-height: 80vh !important;
    margin: 0 !important;
    padding: 0 !important;
    border-radius: 20px 20px 0 0 !important;
    transform: translateY(100%) !important;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    z-index: 10000 !important;
    overflow-y: auto !important;
    box-shadow: 0 -10px 25px rgba(0, 0, 0, 0.3) !important;
  }

  /* الحالة المفتوحة */
  salla-user-menu .dropdown-toggler.is-opened .dropdown__menu,
  salla-user-menu.is-opened .dropdown__menu,
  .user-menu.is-opened .dropdown__menu,
  [class*="user"].is-opened .dropdown__menu {
    transform: translateY(0) !important;
    opacity: 1 !important;
    visibility: visible !important;
  }
}