// Dropdown toggler
.dropdown__trigger {
  @apply rounded-full overflow-hidden w-10 h-10 font-medium focus:ring-offset-transparent cursor-pointer;

  &.filter{
    @apply rounded-none w-auto h-auto overflow-visible;
  }

  /* تحسينات للثيم الغامق */
  .gaming-theme & {
    @apply border border-purple-500/30 bg-gray-800/50;
    transition: all 0.3s ease;

    &:hover {
      @apply border-purple-400/50 bg-gray-700/60;
      box-shadow: 0 0 10px rgba(147, 51, 234, 0.3);
    }
  }
}

.dropdown__menu {
  @apply origin-top-right duration-200 transition-all scale-y-90 absolute opacity-0 -translate-y-4 invisible rtl:left-0 ltr:right-0 z-50 w-80 lg:w-60 rounded-t-md lg:rounded-t-none rounded-b-md shadow-default bg-white top-full lg:border-t lg:border-gray-300/30;
  outline: none;

  /* تحسينات للثيم الغامق */
  .gaming-theme & {
    @apply bg-gray-800/95 border-purple-500/30 backdrop-blur-md;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5), 0 0 20px rgba(147, 51, 234, 0.2);

    .menu-item {
      @apply text-gray-200 hover:text-white hover:bg-purple-600/20;
      transition: all 0.3s ease;

      &:hover {
        background: linear-gradient(90deg, rgba(147, 51, 234, 0.1) 0%, rgba(79, 70, 229, 0.1) 100%);
      }
    }
  }
}

.dropdown-toggler {
  @apply inline-flex items-center lg:h-full w-10 mx-0 text-gray-500 relative;

  &.cat-filter{
    @apply static w-auto;

    .dropdown__trigger {
      @apply rounded-none w-auto h-auto overflow-visible;
    }
  }

  &:before {
    content: "";
    background: rgba(113, 113, 122, 0.75);
    @apply fixed w-screen h-screen left-0 top-0 opacity-0 pointer-events-none invisible duration-300 z-40;
  }

  &.is-opened {
    .dropdown__menu {
      @apply opacity-100 visible translate-y-0 scale-100;
    }

    &:before {
      @apply opacity-100 visible pointer-events-auto;
    }
  }

  /* تحسينات للثيم الغامق */
  .gaming-theme & {
    @apply text-gray-300;

    &:hover {
      @apply text-white;
    }
  }

  @media (max-width: 1024px) {
    .dropdown__menu {
      left: 0 !important;
      @apply fixed bottom-0 top-auto w-full opacity-0 translate-y-10 origin-center duration-300 rounded-b-none z-50;

      .menu-item{
        @apply rtl:pl-2.5 ltr:pr-2.5 py-4 text-lg;
      }

      /* تحسينات للثيم الغامق على الموبايل */
      .gaming-theme & {
        @apply bg-gray-900/98 border-t border-purple-500/30;
        backdrop-filter: blur(20px);

        .menu-item {
          @apply text-gray-200 border-b border-gray-700/50;

          &:hover, &:active {
            @apply bg-purple-600/20 text-white;
          }

          &:last-child {
            @apply border-b-0;
          }
        }
      }
    }

    &.is-opened {
      .dropdown__menu {
        @apply opacity-100 translate-y-0;
      }

      &:before {
        @apply opacity-100 visible pointer-events-auto;
        background: rgba(0, 0, 0, 0.8);
      }
    }
  }
}