
// Main Menu
.main-menu > li > a[href*="offer"]{
  @apply text-red-800
}

@media only screen and (min-width: 1024px) {
  .main-menu {
    @apply hidden lg:flex flex-wrap items-center mx-6 pt-8 pb-0;

    .fixed-pinned & {
      padding-top: 0;
      padding-bottom: 0;
    }

    li{
      > a{
        @apply flex justify-between items-center transition duration-300 p-3 text-sm hover:text-primary hover:no-underline;
      }

      &.root-level{
        @apply inline-block;

        > a{
          @apply font-bold pt-0 pb-8;
        }
      }
    }

    /* تحسينات للثيم الغامق */
    .gaming-theme & {
      li > a {
        @apply text-gray-300 hover:text-white;
        transition: all 0.3s ease;
        position: relative;

        &:hover {
          text-shadow: 0 0 8px rgba(255, 255, 255, 0.5);

          &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 80%;
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(147, 51, 234, 0.8), transparent);
            border-radius: 1px;
          }
        }
      }

      li.root-level > a {
        &:hover::after {
          bottom: 8px;
        }
      }
    }

    > .has-children:hover > a{
      color: var(--color-primary);
    }

    .has-children{
      li a:hover,
      .has-children:hover > a{
        color: var(--color-primary);
        @apply bg-gray-200/20;
      }

      > a:after{
        font-family: 'sallaicons';
        content: "\e970";
        @apply inline-block transition-transform duration-300 self-end mx-0.5 text-lg opacity-50 leading-4;
      }

      &.root-level {
        > a:after{
          content: "\e96e";
        }

        &:hover > a:after{
          opacity: 1;
          transform: scaleY(-1);
        }
      }
    }

    .sub-menu {
      @apply z-50 transition opacity-0 invisible absolute bg-white -translate-y-3 shadow-default rounded-b-md border-t border-gray-300/30;

      .sub-menu {
        top: -1px;
        right: 100%;

        [dir="ltr"] & {
          left: 100%;
          right: auto;
        }
      }

      /* تحسينات للثيم الغامق */
      .gaming-theme & {
        @apply bg-gray-800 border-purple-500;
        background-color: rgba(31, 41, 55, 0.95);
        border-color: rgba(147, 51, 234, 0.3);
        backdrop-filter: blur(10px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5), 0 0 20px rgba(147, 51, 234, 0.2);

        li a {
          @apply text-gray-200 hover:text-white;
          transition: all 0.3s ease;

          &:hover {
            background: linear-gradient(90deg, rgba(147, 51, 234, 0.2) 0%, rgba(79, 70, 229, 0.2) 100%);
            text-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
          }
        }
      }

      .s-product-card-entry{
        @apply border border-gray-100;
      }

      .btn {
        padding: 8px 10px 10px;
      }

      li.mega-menu .container {
        ul{
          @apply p-0 m-0 border-none;
        }
  
        > div{
          @apply hidden;
        }
      }
    }

    .change-menu-dir .sub-menu  .sub-menu {
      @apply rtl:left-full rtl:right-auto ltr:right-full ltr:left-auto;
    }

    li:hover {
      > .sub-menu {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
      }
    }
  }

  .main-menu .sub-menu ul > li:not(:first-child) > .sub-menu{
    border-top-right-radius: 0.375rem;
    border-top-left-radius: 0.375rem;
    border: none;
  }
}
// Mobile Slide Menu
@media only screen and (max-width: 1024px) {
  .filters-opened{
    .close-filters{
      display: block !important;
    }
  } 
  .menu-opened{
    .btn--close-sm.close-mobile-menu{
      display: block !important;
    }
  }
  .mobile-menu {
      display: none;
      @apply lg:hidden overflow-hidden;

      /* تحسينات للثيم الغامق */
      .gaming-theme & {
        @apply bg-gray-900;
        background-color: rgba(17, 24, 39, 0.98);
        backdrop-filter: blur(20px);
        border-right: 1px solid rgba(147, 51, 234, 0.3);

        [dir="ltr"] & {
          border-left: 1px solid rgba(147, 51, 234, 0.3);
          border-right: none;
        }
      }
  }
  .mm-ocd__content{
    overflow-y: auto;
  }
  .mm-ocd-opened {
    .mobile-menu {
      display: block;
    }

    @media (max-width: 480px) {
      .btn--close-sm {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
        z-index: 99999999;
      }
    }
  }

  .mm-spn.mm-spn--light {
    color: #000;
    background: #ffffff;
  }

  .mm-spn {
    ul.main-menu li:before{
      @apply w-2 h-2;
    }

    &.mm-spn--navbar{
      &:after {
        @apply transition-all duration-300 rtl:pr-12 ltr:pl-12 text-start opacity-90 font-bold;
      }

      &.mm-spn--main{
        &:after{
          @apply rtl:pr-3 ltr:pl-3;
        }
      }
    }

    /* تحسينات للثيم الغامق */
    .gaming-theme & {
      @apply bg-gray-900 text-white;
      background-color: rgba(17, 24, 39, 0.98);

      &.mm-spn--navbar:after {
        @apply text-white;
        text-shadow: 0 0 8px rgba(147, 51, 234, 0.5);
      }
    }

    &.mm-spn--navbar:before {
      [dir="rtl"] & {
        transform: rotate(135deg) translateY(77%);
        right: var(--mm-spn-item-indent);
        left: auto;
      }
    }

    li {
      a,
      > span {
		padding: 18px;
		@apply flex items-center gap-4 transition-all duration-300;
	  }

	  img{
		@apply w-12 h-12 object-cover bg-[#f5f7f9] pointer-events-none rounded-lg;
	  }

      a span{
        padding: 0;
      }

      /* تحسينات للثيم الغامق */
      .gaming-theme & {
        a, > span {
          @apply text-gray-200 border-b border-gray-700;
          border-color: rgba(75, 85, 99, 0.5);

          &:hover, &:active {
            @apply text-white;
            background-color: rgba(147, 51, 234, 0.2);
            text-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
          }
        }

        img {
          @apply bg-gray-800 border border-purple-500;
          background-color: rgba(31, 41, 55, 0.6);
          border-color: rgba(147, 51, 234, 0.2);
          box-shadow: 0 0 10px rgba(147, 51, 234, 0.2);
        }

        &:last-child a, &:last-child > span {
          @apply border-b-0;
        }
      }

      &:before {
        [dir="rtl"] & {
          width: 6px;
          height: 6px;
          top: 50%;
          left: 25px;
          left: calc(var(--mm-spn-item-height) / 2);
          right: auto;
          border-bottom: 1px solid;
          border-left: 1px solid;
          border-right: none;
          border-top: none;
        }
      }

      &:after {
        width: 100%;
        border-color: var(--infinte-color);
      }
    }
  }
}