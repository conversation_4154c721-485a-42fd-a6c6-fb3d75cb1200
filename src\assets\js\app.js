import MobileMenu from 'mmenu-light';
import Swal from 'sweetalert2';
import Anime from './partials/anime';
import initTootTip from './partials/tooltip';
import AppHelpers from "./app-helpers";
import CategorySearch from './partials/category-search';
import './main.js';
// import './particles-background.js'; // تم تعطيل الخلفية المتحركة

class App extends AppHelpers {
  constructor() {
    super();
    window.app = this;
  }

  loadTheApp() {
    this.commonThings();
    this.initiateNotifier();
    this.initiateMobileMenu();
    if (header_is_sticky) {
      this.initiateStickyMenu();
    }
    this.initAddToCart();
    this.initiateAdAlert();
    this.initiateDropdowns();
    this.initiateModals();
    this.initiateCollapse();
    this.initAttachWishlistListeners();
    this.changeMenuDirection();
    this.initCustomComponents();
    initTootTip();
    this.loadModalImgOnclick();

    salla.comment.event.onAdded(() => window.location.reload());

    this.status = 'ready';
    document.dispatchEvent(new CustomEvent('theme::ready'));
    this.log('Theme Loaded 🎉');
  }

  /**
   * تهيئة المكونات المخصصة
   */
  initCustomComponents() {
    this.log('تحميل المكونات المخصصة...');
    
    // التأكد من أن العنصر المخصص للبحث مسجل
    if (!customElements.get('category-search')) {
      this.log('تسجيل مكون البحث يدوياً');
      customElements.define('category-search', CategorySearch);
    }
    
    this.log('اكتمل تحميل المكونات المخصصة');
  }

  log(message) {
    salla.log(`ThemeApp(Raed)::${message}`);
    return this;
  }

    // fix Menu Direction at the third level >> The menu at the third level was popping off the page
    changeMenuDirection(){
      app.all('.root-level.has-children',item=>{
        if(item.classList.contains('change-menu-dir')) return;
        app.on('mouseover',item,()=>{
          let submenu = item.querySelector('.sub-menu .sub-menu');
          if(submenu){
            let rect = submenu.getBoundingClientRect();
            (rect.left < 10 || rect.right > window.innerWidth - 10) && app.addClass(item,'change-menu-dir')
          }      
        })
      })
    }

  loadModalImgOnclick(){
    document.querySelectorAll('.load-img-onclick').forEach(link => {
      link.addEventListener('click', (event) => {
        event.preventDefault();
        let modal = document.querySelector('#' + link.dataset.modalId),
          img = modal.querySelector('img'),
          imgSrc = img.dataset.src;
        modal.open();

        if (img.classList.contains('loaded')) return;

        img.src = imgSrc;
        img.classList.add('loaded');
      })
    })
  }

  commonThings() {
    this.cleanContentArticles('.content-entry');
  }

  cleanContentArticles(elementsSelector) {
    let articleElements = document.querySelectorAll(elementsSelector);

    if (articleElements.length) {
      articleElements.forEach(article => {
        article.innerHTML = article.innerHTML.replace(/\&nbsp;/g, ' ')
      })
    }
  }

isElementLoaded(selector){
  return new Promise((resolve=>{
    const interval=setInterval(()=>{
    if(document.querySelector(selector)){
      clearInterval(interval)
      return resolve(document.querySelector(selector))
    }
   },160)
}))

  
  };

  copyToClipboard(event) {
    event.preventDefault();
    let aux = document.createElement("input"),
    btn = event.currentTarget;
    aux.setAttribute("value", btn.dataset.content);
    document.body.appendChild(aux);
    aux.select();
    document.execCommand("copy");
    document.body.removeChild(aux);
    this.toggleElementClassIf(btn, 'copied', 'code-to-copy', () => true);
    setTimeout(() => {
      this.toggleElementClassIf(btn, 'code-to-copy', 'copied', () => true)
    }, 1000);
  }

  initiateNotifier() {
    salla.notify.setNotifier(function (message, type, data) {
      if (typeof message == 'object') {
        return Swal.fire(message).then(type);
      }

      return Swal.mixin({
        toast: true,
        position: salla.config.get('theme.is_rtl') ? 'top-start' : 'top-end',
        showConfirmButton: false,
        timer: 2000,
        didOpen: (toast) => {
          toast.addEventListener('mouseenter', Swal.stopTimer)
          toast.addEventListener('mouseleave', Swal.resumeTimer)
        }
      }).fire({
        icon: type,
        title: message,
        showCloseButton: true,
        timerProgressBar: true
      })
    });
  }


  initiateMobileMenu() {
    this.isElementLoaded('#mobile-menu').then((menu) => {
      const mobileMenu = new MobileMenu(menu, "(max-width: 1024px)", "( slidingSubmenus: false)");

      salla.lang.onLoaded(() => {
        mobileMenu.navigation({ title: salla.lang.get('blocks.header.main_menu') });
      });

      const drawer = mobileMenu.offcanvas({
        position: salla.config.get('theme.is_rtl') ? "right" : 'left'
      });

      // فتح القائمة
      this.onClick("a[href='#mobile-menu']", event => {
        event.preventDefault();
        document.body.classList.add('menu-opened');

        // إغلاق أي dropdowns مفتوحة
        document.querySelectorAll('.dropdown-toggler.is-opened').forEach(dropdown => {
          dropdown.classList.remove('is-opened');
        });
        document.body.classList.remove('dropdown--is-opened');

        drawer.open();
      });

      // إغلاق القائمة
      this.onClick(".close-mobile-menu", event => {
        event.preventDefault();
        document.body.classList.remove('menu-opened');
        drawer.close();
      });

      // إغلاق القائمة عند النقر على الخلفية
      document.addEventListener('click', (event) => {
        if (event.target.classList.contains('mm-ocd__backdrop')) {
          document.body.classList.remove('menu-opened');
        }
      });

      // إغلاق القائمة بالـ escape key
      document.addEventListener('keydown', (event) => {
        if (event.key === 'Escape' && document.body.classList.contains('menu-opened')) {
          document.body.classList.remove('menu-opened');
          drawer.close();
        }
      });
    });
  }
 initAttachWishlistListeners() {
    let isListenerAttached = false;
  
    function toggleFavoriteIcon(id, isAdded = true) {
      document.querySelectorAll('.s-product-card-wishlist-btn[data-id="' + id + '"]').forEach(btn => {
        app.toggleElementClassIf(btn, 's-product-card-wishlist-added', 'not-added', () => isAdded);
        app.toggleElementClassIf(btn, 'pulse-anime', 'un-favorited', () => isAdded);
      });
    }
  
    if (!isListenerAttached) {
      salla.wishlist.event.onAdded((event, id) => toggleFavoriteIcon(id));
      salla.wishlist.event.onRemoved((event, id) => toggleFavoriteIcon(id, false));
      isListenerAttached = true; // Mark the listener as attached
    }
  }

  initiateStickyMenu() {
    let header = this.element('#mainnav'),
      height = this.element('#mainnav .inner')?.clientHeight;
    //when it's landing page, there is no header
    if (!header) {
      return;
    }

    window.addEventListener('load', () => setTimeout(() => this.setHeaderHeight(), 500))
    window.addEventListener('resize', () => this.setHeaderHeight())

    window.addEventListener('scroll', () => {
      window.scrollY >= header.offsetTop + height ? header.classList.add('fixed-pinned', 'animated') : header.classList.remove('fixed-pinned');
      window.scrollY >= 200 ? header.classList.add('fixed-header') : header.classList.remove('fixed-header', 'animated');
    }, { passive: true });
  }

  setHeaderHeight() {
    let height = this.element('#mainnav .inner').clientHeight,
      header = this.element('#mainnav');
    header.style.height = height + 'px';
  }

  /**
   * Because salla caches the response, it's important to keep the alert disabled if the visitor closed it.
   * by store the status of the ad in local storage `salla.storage.set(...)`
   */
  initiateAdAlert() {
    let ad = this.element(".salla-advertisement");

    if (!ad) {
      return;
    }

    if (!salla.storage.get('statusAd-' + ad.dataset.id)) {
      ad.classList.remove('hidden');
    }

    this.onClick('.ad-close', function (event) {
      event.preventDefault();
      salla.storage.set('statusAd-' + ad.dataset.id, 'dismissed');

      anime({
        targets: '.salla-advertisement',
        opacity: [1, 0],
        duration: 300,
        height: [ad.clientHeight, 0],
        easing: 'easeInOutQuad',
      });
    });
  }

  initiateDropdowns() {
    // إزالة المستمعين القدامى لتجنب التكرار
    document.removeEventListener('click', this.handleDropdownClick);

    this.handleDropdownClick = (event) => {
      const btn = event.target.closest('.dropdown__trigger');
      if (btn) {
        event.preventDefault();
        event.stopPropagation();

        // إغلاق جميع الـ dropdowns الأخرى
        document.querySelectorAll('.dropdown-toggler.is-opened').forEach(dropdown => {
          if (dropdown !== btn.parentElement) {
            dropdown.classList.remove('is-opened');
          }
        });

        const dropdownToggler = btn.parentElement;
        const dropdownMenu = dropdownToggler.querySelector('.dropdown__menu');

        // تبديل حالة الـ dropdown الحالي
        const isOpening = !dropdownToggler.classList.contains('is-opened');
        dropdownToggler.classList.toggle('is-opened');

        if (isOpening && dropdownMenu) {
          // حساب الموقع الصحيح للـ dropdown
          this.positionDropdown(btn, dropdownMenu);

          // إضافة touch events للموبايل
          if (window.innerWidth <= 1024) {
            dropdownMenu.classList.add('sliding-up');
            this.addMobileSwipeToClose(dropdownMenu, dropdownToggler);

            // إزالة الكلاس بعد انتهاء الانيميشن
            setTimeout(() => {
              dropdownMenu.classList.remove('sliding-up');
            }, 300);
          }
        }

        document.body.classList.toggle('dropdown--is-opened',
          document.querySelector('.dropdown-toggler.is-opened') !== null);
      } else {
        // النقر خارج الـ dropdown
        const clickedInsideMenu = event.target.closest('.dropdown__menu');
        const clickedCloseBtn = event.target.classList.contains('dropdown__close');

        if (!clickedInsideMenu || clickedCloseBtn) {
          document.querySelectorAll('.dropdown-toggler.is-opened').forEach(dropdown => {
            dropdown.classList.remove('is-opened');
          });
          document.body.classList.remove('dropdown--is-opened');
        }
      }
    };

    document.addEventListener('click', this.handleDropdownClick);

    // إضافة مستمع للـ escape key
    document.addEventListener('keydown', (event) => {
      if (event.key === 'Escape') {
        document.querySelectorAll('.dropdown-toggler.is-opened').forEach(dropdown => {
          dropdown.classList.remove('is-opened');
        });
        document.body.classList.remove('dropdown--is-opened');
      }
    });

    // إعادة حساب موقع الـ dropdown عند تغيير حجم الشاشة
    window.addEventListener('resize', () => {
      const openDropdown = document.querySelector('.dropdown-toggler.is-opened');
      if (openDropdown) {
        const trigger = openDropdown.querySelector('.dropdown__trigger');
        const menu = openDropdown.querySelector('.dropdown__menu');
        if (trigger && menu) {
          this.positionDropdown(trigger, menu);
        }
      }
    });
  }

  positionDropdown(trigger, menu) {
    if (!trigger || !menu) return;

    const viewportWidth = window.innerWidth;

    // للموبايل - استخدم الموقع الافتراضي (bottom sheet)
    if (viewportWidth <= 1024) {
      menu.style.position = 'fixed';
      menu.style.top = 'auto';
      menu.style.bottom = '0';
      menu.style.left = '0';
      menu.style.right = '0';
      menu.style.width = '100%';
      menu.style.transform = 'translateY(0)';
      menu.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
      return;
    }

    // للديسكتوب
    const triggerRect = trigger.getBoundingClientRect();
    const menuRect = menu.getBoundingClientRect();
    const viewportHeight = window.innerHeight;

    // حساب الموقع الأفضل
    let top = triggerRect.bottom + 8; // 8px مسافة من الـ trigger
    let left = triggerRect.right - menuRect.width; // محاذاة من اليمين

    // للـ RTL
    if (document.dir === 'rtl' || document.documentElement.dir === 'rtl') {
      left = triggerRect.left;
    }

    // التأكد من أن الـ dropdown لا يخرج من الشاشة
    if (top + menuRect.height > viewportHeight - 20) {
      top = triggerRect.top - menuRect.height - 8; // عرض فوق الـ trigger
    }

    if (left < 10) {
      left = 10; // مسافة من حافة الشاشة
    } else if (left + menuRect.width > viewportWidth - 10) {
      left = viewportWidth - menuRect.width - 10;
    }

    // تطبيق الموقع
    menu.style.position = 'fixed';
    menu.style.top = `${top}px`;
    menu.style.left = `${left}px`;
    menu.style.right = 'auto';
    menu.style.bottom = 'auto';
    menu.style.width = 'auto';
    menu.style.transform = 'none';
  }

  addMobileSwipeToClose(menu, toggler) {
    let startY = 0;
    let currentY = 0;
    let isDragging = false;

    const handleTouchStart = (e) => {
      startY = e.touches[0].clientY;
      isDragging = true;
      menu.style.transition = 'none';
    };

    const handleTouchMove = (e) => {
      if (!isDragging) return;

      currentY = e.touches[0].clientY;
      const deltaY = currentY - startY;

      // السماح بالسحب لأسفل فقط
      if (deltaY > 0) {
        const translateY = Math.min(deltaY, menu.offsetHeight);
        menu.style.transform = `translateY(${translateY}px)`;
      }
    };

    const handleTouchEnd = () => {
      if (!isDragging) return;

      isDragging = false;
      menu.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';

      const deltaY = currentY - startY;

      // إذا تم السحب أكثر من 100px، أغلق القائمة
      if (deltaY > 100) {
        menu.classList.add('sliding-down');
        toggler.classList.remove('is-opened');
        document.body.classList.remove('dropdown--is-opened');
        menu.style.transform = 'translateY(100%)';

        // إزالة الكلاس بعد انتهاء الانيميشن
        setTimeout(() => {
          menu.classList.remove('sliding-down');
        }, 300);
      } else {
        // إرجاع القائمة لموضعها
        menu.style.transform = 'translateY(0)';
      }

      // إزالة event listeners
      menu.removeEventListener('touchstart', handleTouchStart);
      menu.removeEventListener('touchmove', handleTouchMove);
      menu.removeEventListener('touchend', handleTouchEnd);
    };

    // إضافة event listeners
    menu.addEventListener('touchstart', handleTouchStart, { passive: true });
    menu.addEventListener('touchmove', handleTouchMove, { passive: true });
    menu.addEventListener('touchend', handleTouchEnd, { passive: true });
  }

  initiateModals() {
    this.onClick('[data-modal-trigger]', e => {
      let id = '#' + e.target.dataset.modalTrigger;
      this.removeClass(id, 'hidden');
      setTimeout(() => this.toggleModal(id, true)); //small amont of time to running toggle After adding hidden
    });
    salla.event.document.onClick("[data-close-modal]", e => this.toggleModal('#' + e.target.dataset.closeModal, false));
  }

  toggleModal(id, isOpen) {
    this.toggleClassIf(`${id} .s-salla-modal-overlay`, 'ease-out duration-300 opacity-100', 'opacity-0', () => isOpen)
      .toggleClassIf(`${id} .s-salla-modal-body`,
        'ease-out duration-300 opacity-100 translate-y-0 sm:scale-100', //add these classes
        'opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95', //remove these classes
        () => isOpen)
      .toggleElementClassIf(document.body, 'modal-is-open', 'modal-is-closed', () => isOpen);
    if (!isOpen) {
      setTimeout(() => this.addClass(id, 'hidden'), 350);
    }
  }

  initiateCollapse() {
    document.querySelectorAll('.btn--collapse')
      .forEach((trigger) => {
        const content = document.querySelector('#' + trigger.dataset.show);
        const state = { isOpen: false }

        const onOpen = () => anime({
          targets: content,
          duration: 225,
          height: content.scrollHeight,
          opacity: [0, 1],
          easing: 'easeOutQuart',
        });

        const onClose = () => anime({
          targets: content,
          duration: 225,
          height: 0,
          opacity: [1, 0],
          easing: 'easeOutQuart',
        })

        const toggleState = (isOpen) => {
          state.isOpen = !isOpen
          this.toggleElementClassIf([content, trigger], 'is-closed', 'is-opened', () => isOpen);
        }

        trigger.addEventListener('click', () => {
          const { isOpen } = state
          toggleState(isOpen)
          isOpen ? onClose() : onOpen();
        })
      });
  }


  /**
   * Workaround for seeking to simplify & clean, There are three ways to use this method:
   * 1- direct call: `this.anime('.my-selector')` - will use default values
   * 2- direct call with overriding defaults: `this.anime('.my-selector', {duration:3000})`
   * 3- return object to play it letter: `this.anime('.my-selector', false).duration(3000).play()` - will not play animation unless calling play method.
   * @param {string|HTMLElement} selector
   * @param {object|undefined|null|null} options - in case there is need to set attributes one by one set it `false`;
   * @return {Anime|*}
   */
  anime(selector, options = null) {
    let anime = new Anime(selector, options);
    return options === false ? anime : anime.play();
  }

  /**
   * These actions are responsible for pressing "add to cart" button,
   * they can be from any page, especially when mega-menu is enabled
   */
  initAddToCart() {
    salla.cart.event.onUpdated(summary => {
      document.querySelectorAll('[data-cart-total]').forEach(el => el.innerHTML = salla.money(summary.total));
      document.querySelectorAll('[data-cart-count]').forEach(el => el.innerText = salla.helpers.number(summary.count));
    });

    salla.cart.event.onItemAdded((response, prodId) => {
      app.element('salla-cart-summary').animateToCart(app.element(`#product-${prodId} img`));
    });
  }
}

salla.onReady(() => (new App).loadTheApp());

document.addEventListener('DOMContentLoaded', function() {
  console.log('تم تحميل تطبيق المتجر بنجاح');
});
