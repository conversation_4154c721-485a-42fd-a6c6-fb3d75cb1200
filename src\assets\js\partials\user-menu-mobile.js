/**
 * User Menu Mobile Enhancement
 * يضمن ظهور قائمة المستخدم من الأسفل في الموبايل
 */

class UserMenuMobile {
    constructor() {
        this.init();
    }

    init() {
        // انتظار تحميل Salla
        if (typeof salla !== 'undefined') {
            this.setupUserMenu();
        } else {
            document.addEventListener('DOMContentLoaded', () => {
                this.setupUserMenu();
            });
        }
    }

    setupUserMenu() {
        // البحث عن عنصر salla-user-menu
        const userMenus = document.querySelectorAll('salla-user-menu');
        
        userMenus.forEach(userMenu => {
            this.enhanceUserMenu(userMenu);
        });

        // مراقبة إضافة عناصر جديدة
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === 1) { // Element node
                        if (node.tagName === 'SALLA-USER-MENU') {
                            this.enhanceUserMenu(node);
                        }
                        // البحث داخل العنصر المضاف
                        const userMenus = node.querySelectorAll && node.querySelectorAll('salla-user-menu');
                        if (userMenus) {
                            userMenus.forEach(userMenu => {
                                this.enhanceUserMenu(userMenu);
                            });
                        }
                    }
                });
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    enhanceUserMenu(userMenu) {
        // التحقق من أن العنصر لم يتم تحسينه من قبل
        if (userMenu.hasAttribute('data-mobile-enhanced')) {
            return;
        }

        // وضع علامة أن العنصر تم تحسينه
        userMenu.setAttribute('data-mobile-enhanced', 'true');

        // انتظار تحميل المحتوى الداخلي
        setTimeout(() => {
            this.setupDropdownBehavior(userMenu);
        }, 100);
    }

    setupDropdownBehavior(userMenu) {
        const dropdownToggler = userMenu.querySelector('.dropdown-toggler');
        const dropdownMenu = userMenu.querySelector('.dropdown__menu');

        if (!dropdownToggler || !dropdownMenu) {
            return;
        }

        // إضافة كلاس للموبايل
        if (window.innerWidth <= 1024) {
            dropdownMenu.classList.add('mobile-bottom-menu');
        }

        // مراقبة تغيير حجم الشاشة
        window.addEventListener('resize', () => {
            if (window.innerWidth <= 1024) {
                dropdownMenu.classList.add('mobile-bottom-menu');
            } else {
                dropdownMenu.classList.remove('mobile-bottom-menu');
            }
        });

        // تحسين سلوك الفتح والإغلاق
        const trigger = userMenu.querySelector('.dropdown__trigger');
        if (trigger) {
            trigger.addEventListener('click', (e) => {
                if (window.innerWidth <= 1024) {
                    e.preventDefault();
                    this.toggleMobileMenu(dropdownToggler, dropdownMenu);
                }
            });
        }

        // إغلاق القائمة عند النقر على الخلفية
        document.addEventListener('click', (e) => {
            if (window.innerWidth <= 1024 && 
                dropdownToggler.classList.contains('is-opened') &&
                !userMenu.contains(e.target)) {
                this.closeMobileMenu(dropdownToggler, dropdownMenu);
            }
        });

        // إغلاق القائمة عند الضغط على ESC
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && 
                window.innerWidth <= 1024 && 
                dropdownToggler.classList.contains('is-opened')) {
                this.closeMobileMenu(dropdownToggler, dropdownMenu);
            }
        });
    }

    toggleMobileMenu(toggler, menu) {
        if (toggler.classList.contains('is-opened')) {
            this.closeMobileMenu(toggler, menu);
        } else {
            this.openMobileMenu(toggler, menu);
        }
    }

    openMobileMenu(toggler, menu) {
        toggler.classList.add('is-opened');
        menu.classList.add('mobile-opened');
        document.body.style.overflow = 'hidden';
    }

    closeMobileMenu(toggler, menu) {
        toggler.classList.remove('is-opened');
        menu.classList.remove('mobile-opened');
        document.body.style.overflow = '';
    }
}

// تشغيل التحسين
new UserMenuMobile();
