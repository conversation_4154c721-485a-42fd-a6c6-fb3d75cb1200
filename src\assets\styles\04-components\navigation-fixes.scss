/* Navigation Fixes for Gaming Theme */

/* إصلاحات عامة للـ overflow لمنع قطع الـ dropdowns */
header, .header, .main-header, .navbar, .nav, .navigation,
.main-nav-container, .container, .inner, .wrapper {
  overflow: visible !important;
}

/* إصلاحات عامة للنافيجيشن */
.gaming-theme {
  /* إصلاح مشاكل z-index */
  .main-nav-container {
    position: relative;
    z-index: 100;
    
    &.fixed-pinned .inner {
      z-index: 999 !important;
    }
  }
  
  /* إصلاح مشاكل الـ dropdown */
  .dropdown-toggler {
    position: relative !important;
    z-index: 101;
    overflow: visible !important;

    &.is-opened {
      z-index: 102;

      .dropdown__menu {
        z-index: 9999 !important;
        position: fixed !important;
        transform: none !important;
      }

      &:before {
        z-index: 9998 !important;
      }
    }
  }
  
  /* إصلاح مشاكل الـ sub-menu */
  .main-menu .sub-menu {
    z-index: 104 !important;
    
    .sub-menu {
      z-index: 105 !important;
    }
  }
  
  /* إصلاح مشاكل الموبايل */
  @media (max-width: 1024px) {
    .mobile-menu {
      z-index: 1000 !important;
    }
    
    .mm-ocd__backdrop {
      z-index: 999 !important;
      background: rgba(0, 0, 0, 0.8) !important;
    }
    
    .dropdown__menu {
      z-index: 1001 !important;
    }
    
    .btn--close-sm.close-mobile-menu {
      z-index: 1002 !important;
      position: fixed;
      top: 20px;
      right: 20px;
      background: rgba(147, 51, 234, 0.9);
      color: white;
      border: none;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: none;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
      
      [dir="rtl"] & {
        left: 20px;
        right: auto;
      }
      
      &:hover {
        background: rgba(147, 51, 234, 1);
        transform: scale(1.1);
      }
    }
  }
  
  /* تحسينات إضافية للـ user menu */
  salla-user-menu {
    position: relative;
    z-index: 102;

    .dropdown__trigger {
      border: 2px solid rgba(147, 51, 234, 0.3);
      background-color: rgba(31, 41, 55, 0.8);
      transition: all 0.3s ease;

      &:hover {
        border-color: rgba(147, 51, 234, 0.6);
        background-color: rgba(31, 41, 55, 1);
        box-shadow: 0 0 15px rgba(147, 51, 234, 0.4);
      }

      img {
        border-radius: 50%;
      }
    }
    
    .dropdown__menu {
      margin-top: 8px;
      border: 1px solid rgba(147, 51, 234, 0.3);
      
      .menu-item {
        padding: 12px 16px;
        border-bottom: 1px solid rgba(75, 85, 99, 0.3);
        
        &:last-child {
          border-bottom: none;
        }
        
        &:hover {
          background: linear-gradient(90deg, 
            rgba(147, 51, 234, 0.1) 0%, 
            rgba(79, 70, 229, 0.1) 100%);
          transform: translateX(4px);
          
          [dir="rtl"] & {
            transform: translateX(-4px);
          }
        }
        
        i {
          margin-right: 8px;
          color: rgba(147, 51, 234, 0.8);
          
          [dir="rtl"] & {
            margin-left: 8px;
            margin-right: 0;
          }
        }
      }
    }
  }
  
  /* تحسينات للـ cart summary */
  salla-cart-summary {
    position: relative;
    z-index: 102;

    .header-btn__icon {
      background-color: rgba(31, 41, 55, 0.8);
      border: 2px solid rgba(147, 51, 234, 0.3);

      &:hover {
        background-color: rgba(31, 41, 55, 1);
        border-color: rgba(147, 51, 234, 0.6);
        transform: translateY(-2px);
      }
    }
    
    .s-cart-summary-count {
      background: linear-gradient(135deg, #9333ea, #7c3aed);
      border: 1px solid rgba(147, 51, 234, 0.5);
      box-shadow: 0 0 10px rgba(147, 51, 234, 0.6);
      font-weight: bold;
      font-size: 0.75rem;
    }
  }
  
  /* تحسينات للـ mobile menu button */
  .mburger {
    color: rgba(147, 51, 234, 0.8) !important;
    font-size: 1.5rem !important;
    transition: all 0.3s ease;
    
    &:hover {
      color: rgba(147, 51, 234, 1) !important;
      transform: scale(1.1);
    }
  }
  
  /* تحسينات للـ navbar brand */
  .navbar-brand {
    position: relative;
    z-index: 102;
    
    img {
      transition: all 0.3s ease;
      filter: drop-shadow(0 0 8px rgba(147, 51, 234, 0.3));
      
      &:hover {
        transform: scale(1.05);
        filter: drop-shadow(0 0 12px rgba(147, 51, 234, 0.5));
      }
    }
  }
  
  /* إصلاح مشاكل التداخل */
  .header-search {
    position: relative;
    z-index: 99;
    
    .s-search-results {
      z-index: 100 !important;
    }
  }
  
  /* تحسينات للـ responsive */
  @media (max-width: 768px) {
    .main-nav-container .inner .container {
      padding-left: 10px;
      padding-right: 10px;
    }
    
    .navbar-brand img {
      max-width: 120px;
    }
    
    .header-btn__icon {
      width: 36px;
      height: 36px;
      font-size: 1rem;
    }
  }
  
  /* تحسينات للـ accessibility */
  .dropdown__trigger:focus,
  .header-btn:focus,
  .mburger:focus {
    outline: 2px solid rgba(147, 51, 234, 0.6);
    outline-offset: 2px;
  }
  
  /* تحسينات للـ animations */
  .dropdown__menu,
  .sub-menu {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .dropdown-toggler.is-opened .dropdown__menu {
    animation: dropdownFadeIn 0.3s ease-out;
  }
  
  @keyframes dropdownFadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }
}

/* إصلاحات للثيم العادي */
body:not(.gaming-theme) {
  .dropdown__menu {
    z-index: 50;
  }
  
  .main-menu .sub-menu {
    z-index: 51;
  }
  
  .mobile-menu {
    z-index: 100;
  }
}
