/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
/******/ (() => { // webpackBootstrap
/******/ 	var __webpack_modules__ = ({

/***/ "./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/arrayLikeToArray.js":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/arrayLikeToArray.js ***!
  \**************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ _arrayLikeToArray)\n/* harmony export */ });\nfunction _arrayLikeToArray(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/arrayLikeToArray.js?");

/***/ }),

/***/ "./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/arrayWithoutHoles.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/arrayWithoutHoles.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ _arrayWithoutHoles)\n/* harmony export */ });\n/* harmony import */ var _arrayLikeToArray_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./arrayLikeToArray.js */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/arrayLikeToArray.js\");\n\nfunction _arrayWithoutHoles(r) {\n  if (Array.isArray(r)) return (0,_arrayLikeToArray_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(r);\n}\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/arrayWithoutHoles.js?");

/***/ }),

/***/ "./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ _assertThisInitialized)\n/* harmony export */ });\nfunction _assertThisInitialized(e) {\n  if (void 0 === e) throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  return e;\n}\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js?");

/***/ }),

/***/ "./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js ***!
  \**************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ _asyncToGenerator)\n/* harmony export */ });\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) {\n  try {\n    var i = n[a](c),\n      u = i.value;\n  } catch (n) {\n    return void e(n);\n  }\n  i.done ? t(u) : Promise.resolve(u).then(r, o);\n}\nfunction _asyncToGenerator(n) {\n  return function () {\n    var t = this,\n      e = arguments;\n    return new Promise(function (r, o) {\n      var a = n.apply(t, e);\n      function _next(n) {\n        asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n);\n      }\n      function _throw(n) {\n        asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n);\n      }\n      _next(void 0);\n    });\n  };\n}\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js?");

/***/ }),

/***/ "./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/classCallCheck.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/classCallCheck.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ _classCallCheck)\n/* harmony export */ });\nfunction _classCallCheck(a, n) {\n  if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\");\n}\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/classCallCheck.js?");

/***/ }),

/***/ "./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/construct.js":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/construct.js ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ _construct)\n/* harmony export */ });\n/* harmony import */ var _isNativeReflectConstruct_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./isNativeReflectConstruct.js */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/isNativeReflectConstruct.js\");\n/* harmony import */ var _setPrototypeOf_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./setPrototypeOf.js */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js\");\n\n\nfunction _construct(t, e, r) {\n  if ((0,_isNativeReflectConstruct_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])()) return Reflect.construct.apply(null, arguments);\n  var o = [null];\n  o.push.apply(o, e);\n  var p = new (t.bind.apply(t, o))();\n  return r && (0,_setPrototypeOf_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(p, r.prototype), p;\n}\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/construct.js?");

/***/ }),

/***/ "./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/createClass.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/createClass.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ _createClass)\n/* harmony export */ });\n/* harmony import */ var _toPropertyKey_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./toPropertyKey.js */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/toPropertyKey.js\");\n\nfunction _defineProperties(e, r) {\n  for (var t = 0; t < r.length; t++) {\n    var o = r[t];\n    o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, (0,_toPropertyKey_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(o.key), o);\n  }\n}\nfunction _createClass(e, r, t) {\n  return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", {\n    writable: !1\n  }), e;\n}\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/createClass.js?");

/***/ }),

/***/ "./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/getPrototypeOf.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/getPrototypeOf.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ _getPrototypeOf)\n/* harmony export */ });\nfunction _getPrototypeOf(t) {\n  return _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function (t) {\n    return t.__proto__ || Object.getPrototypeOf(t);\n  }, _getPrototypeOf(t);\n}\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/getPrototypeOf.js?");

/***/ }),

/***/ "./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/inherits.js":
/*!******************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/inherits.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ _inherits)\n/* harmony export */ });\n/* harmony import */ var _setPrototypeOf_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./setPrototypeOf.js */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js\");\n\nfunction _inherits(t, e) {\n  if (\"function\" != typeof e && null !== e) throw new TypeError(\"Super expression must either be null or a function\");\n  t.prototype = Object.create(e && e.prototype, {\n    constructor: {\n      value: t,\n      writable: !0,\n      configurable: !0\n    }\n  }), Object.defineProperty(t, \"prototype\", {\n    writable: !1\n  }), e && (0,_setPrototypeOf_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(t, e);\n}\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/inherits.js?");

/***/ }),

/***/ "./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/isNativeFunction.js":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/isNativeFunction.js ***!
  \**************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ _isNativeFunction)\n/* harmony export */ });\nfunction _isNativeFunction(t) {\n  try {\n    return -1 !== Function.toString.call(t).indexOf(\"[native code]\");\n  } catch (n) {\n    return \"function\" == typeof t;\n  }\n}\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/isNativeFunction.js?");

/***/ }),

/***/ "./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/isNativeReflectConstruct.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/isNativeReflectConstruct.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ _isNativeReflectConstruct)\n/* harmony export */ });\nfunction _isNativeReflectConstruct() {\n  try {\n    var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n  } catch (t) {}\n  return (_isNativeReflectConstruct = function _isNativeReflectConstruct() {\n    return !!t;\n  })();\n}\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/isNativeReflectConstruct.js?");

/***/ }),

/***/ "./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/iterableToArray.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/iterableToArray.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ _iterableToArray)\n/* harmony export */ });\nfunction _iterableToArray(r) {\n  if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r);\n}\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/iterableToArray.js?");

/***/ }),

/***/ "./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/nonIterableSpread.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/nonIterableSpread.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ _nonIterableSpread)\n/* harmony export */ });\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/nonIterableSpread.js?");

/***/ }),

/***/ "./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/possibleConstructorReturn.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/possibleConstructorReturn.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ _possibleConstructorReturn)\n/* harmony export */ });\n/* harmony import */ var _typeof_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./typeof.js */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/typeof.js\");\n/* harmony import */ var _assertThisInitialized_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./assertThisInitialized.js */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js\");\n\n\nfunction _possibleConstructorReturn(t, e) {\n  if (e && (\"object\" == (0,_typeof_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(e) || \"function\" == typeof e)) return e;\n  if (void 0 !== e) throw new TypeError(\"Derived constructors may only return object or undefined\");\n  return (0,_assertThisInitialized_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(t);\n}\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/possibleConstructorReturn.js?");

/***/ }),

/***/ "./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ _setPrototypeOf)\n/* harmony export */ });\nfunction _setPrototypeOf(t, e) {\n  return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) {\n    return t.__proto__ = e, t;\n  }, _setPrototypeOf(t, e);\n}\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js?");

/***/ }),

/***/ "./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ _toConsumableArray)\n/* harmony export */ });\n/* harmony import */ var _arrayWithoutHoles_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./arrayWithoutHoles.js */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/arrayWithoutHoles.js\");\n/* harmony import */ var _iterableToArray_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./iterableToArray.js */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/iterableToArray.js\");\n/* harmony import */ var _unsupportedIterableToArray_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./unsupportedIterableToArray.js */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/unsupportedIterableToArray.js\");\n/* harmony import */ var _nonIterableSpread_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./nonIterableSpread.js */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/nonIterableSpread.js\");\n\n\n\n\nfunction _toConsumableArray(r) {\n  return (0,_arrayWithoutHoles_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(r) || (0,_iterableToArray_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(r) || (0,_unsupportedIterableToArray_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(r) || (0,_nonIterableSpread_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n}\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js?");

/***/ }),

/***/ "./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/toPrimitive.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/toPrimitive.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ toPrimitive)\n/* harmony export */ });\n/* harmony import */ var _typeof_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./typeof.js */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/typeof.js\");\n\nfunction toPrimitive(t, r) {\n  if (\"object\" != (0,_typeof_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != (0,_typeof_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/toPrimitive.js?");

/***/ }),

/***/ "./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/toPropertyKey.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/toPropertyKey.js ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ toPropertyKey)\n/* harmony export */ });\n/* harmony import */ var _typeof_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./typeof.js */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/typeof.js\");\n/* harmony import */ var _toPrimitive_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./toPrimitive.js */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/toPrimitive.js\");\n\n\nfunction toPropertyKey(t) {\n  var i = (0,_toPrimitive_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(t, \"string\");\n  return \"symbol\" == (0,_typeof_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(i) ? i : i + \"\";\n}\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/toPropertyKey.js?");

/***/ }),

/***/ "./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/typeof.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/typeof.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ _typeof)\n/* harmony export */ });\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/typeof.js?");

/***/ }),

/***/ "./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/unsupportedIterableToArray.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/unsupportedIterableToArray.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ _unsupportedIterableToArray)\n/* harmony export */ });\n/* harmony import */ var _arrayLikeToArray_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./arrayLikeToArray.js */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/arrayLikeToArray.js\");\n\nfunction _unsupportedIterableToArray(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return (0,_arrayLikeToArray_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? (0,_arrayLikeToArray_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(r, a) : void 0;\n  }\n}\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/unsupportedIterableToArray.js?");

/***/ }),

/***/ "./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/wrapNativeSuper.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/wrapNativeSuper.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ _wrapNativeSuper)\n/* harmony export */ });\n/* harmony import */ var _getPrototypeOf_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getPrototypeOf.js */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/getPrototypeOf.js\");\n/* harmony import */ var _setPrototypeOf_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./setPrototypeOf.js */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js\");\n/* harmony import */ var _isNativeFunction_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./isNativeFunction.js */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/isNativeFunction.js\");\n/* harmony import */ var _construct_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./construct.js */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/construct.js\");\n\n\n\n\nfunction _wrapNativeSuper(t) {\n  var r = \"function\" == typeof Map ? new Map() : void 0;\n  return _wrapNativeSuper = function _wrapNativeSuper(t) {\n    if (null === t || !(0,_isNativeFunction_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(t)) return t;\n    if (\"function\" != typeof t) throw new TypeError(\"Super expression must either be null or a function\");\n    if (void 0 !== r) {\n      if (r.has(t)) return r.get(t);\n      r.set(t, Wrapper);\n    }\n    function Wrapper() {\n      return (0,_construct_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(t, arguments, (0,_getPrototypeOf_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this).constructor);\n    }\n    return Wrapper.prototype = Object.create(t.prototype, {\n      constructor: {\n        value: Wrapper,\n        enumerable: !1,\n        writable: !0,\n        configurable: !0\n      }\n    }), (0,_setPrototypeOf_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(Wrapper, t);\n  }, _wrapNativeSuper(t);\n}\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/wrapNativeSuper.js?");

/***/ }),

/***/ "./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/regeneratorRuntime.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/regeneratorRuntime.js ***!
  \************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var _typeof = (__webpack_require__(/*! ./typeof.js */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/typeof.js\")[\"default\"]);\nfunction _regeneratorRuntime() {\n  \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */\n  module.exports = _regeneratorRuntime = function _regeneratorRuntime() {\n    return r;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n  var t,\n    r = {},\n    e = Object.prototype,\n    n = e.hasOwnProperty,\n    o = \"function\" == typeof Symbol ? Symbol : {},\n    i = o.iterator || \"@@iterator\",\n    a = o.asyncIterator || \"@@asyncIterator\",\n    u = o.toStringTag || \"@@toStringTag\";\n  function c(t, r, e, n) {\n    Object.defineProperty(t, r, {\n      value: e,\n      enumerable: !n,\n      configurable: !n,\n      writable: !n\n    });\n  }\n  try {\n    c({}, \"\");\n  } catch (t) {\n    c = function c(t, r, e) {\n      return t[r] = e;\n    };\n  }\n  function h(r, e, n, o) {\n    var i = e && e.prototype instanceof Generator ? e : Generator,\n      a = Object.create(i.prototype);\n    return c(a, \"_invoke\", function (r, e, n) {\n      var o = 1;\n      return function (i, a) {\n        if (3 === o) throw Error(\"Generator is already running\");\n        if (4 === o) {\n          if (\"throw\" === i) throw a;\n          return {\n            value: t,\n            done: !0\n          };\n        }\n        for (n.method = i, n.arg = a;;) {\n          var u = n.delegate;\n          if (u) {\n            var c = d(u, n);\n            if (c) {\n              if (c === f) continue;\n              return c;\n            }\n          }\n          if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) {\n            if (1 === o) throw o = 4, n.arg;\n            n.dispatchException(n.arg);\n          } else \"return\" === n.method && n.abrupt(\"return\", n.arg);\n          o = 3;\n          var h = s(r, e, n);\n          if (\"normal\" === h.type) {\n            if (o = n.done ? 4 : 2, h.arg === f) continue;\n            return {\n              value: h.arg,\n              done: n.done\n            };\n          }\n          \"throw\" === h.type && (o = 4, n.method = \"throw\", n.arg = h.arg);\n        }\n      };\n    }(r, n, new Context(o || [])), !0), a;\n  }\n  function s(t, r, e) {\n    try {\n      return {\n        type: \"normal\",\n        arg: t.call(r, e)\n      };\n    } catch (t) {\n      return {\n        type: \"throw\",\n        arg: t\n      };\n    }\n  }\n  r.wrap = h;\n  var f = {};\n  function Generator() {}\n  function GeneratorFunction() {}\n  function GeneratorFunctionPrototype() {}\n  var l = {};\n  c(l, i, function () {\n    return this;\n  });\n  var p = Object.getPrototypeOf,\n    y = p && p(p(x([])));\n  y && y !== e && n.call(y, i) && (l = y);\n  var v = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(l);\n  function g(t) {\n    [\"next\", \"throw\", \"return\"].forEach(function (r) {\n      c(t, r, function (t) {\n        return this._invoke(r, t);\n      });\n    });\n  }\n  function AsyncIterator(t, r) {\n    function e(o, i, a, u) {\n      var c = s(t[o], t, i);\n      if (\"throw\" !== c.type) {\n        var h = c.arg,\n          f = h.value;\n        return f && \"object\" == _typeof(f) && n.call(f, \"__await\") ? r.resolve(f.__await).then(function (t) {\n          e(\"next\", t, a, u);\n        }, function (t) {\n          e(\"throw\", t, a, u);\n        }) : r.resolve(f).then(function (t) {\n          h.value = t, a(h);\n        }, function (t) {\n          return e(\"throw\", t, a, u);\n        });\n      }\n      u(c.arg);\n    }\n    var o;\n    c(this, \"_invoke\", function (t, n) {\n      function i() {\n        return new r(function (r, o) {\n          e(t, n, r, o);\n        });\n      }\n      return o = o ? o.then(i, i) : i();\n    }, !0);\n  }\n  function d(r, e) {\n    var n = e.method,\n      o = r.i[n];\n    if (o === t) return e.delegate = null, \"throw\" === n && r.i[\"return\"] && (e.method = \"return\", e.arg = t, d(r, e), \"throw\" === e.method) || \"return\" !== n && (e.method = \"throw\", e.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), f;\n    var i = s(o, r.i, e.arg);\n    if (\"throw\" === i.type) return e.method = \"throw\", e.arg = i.arg, e.delegate = null, f;\n    var a = i.arg;\n    return a ? a.done ? (e[r.r] = a.value, e.next = r.n, \"return\" !== e.method && (e.method = \"next\", e.arg = t), e.delegate = null, f) : a : (e.method = \"throw\", e.arg = new TypeError(\"iterator result is not an object\"), e.delegate = null, f);\n  }\n  function w(t) {\n    this.tryEntries.push(t);\n  }\n  function m(r) {\n    var e = r[4] || {};\n    e.type = \"normal\", e.arg = t, r[4] = e;\n  }\n  function Context(t) {\n    this.tryEntries = [[-1]], t.forEach(w, this), this.reset(!0);\n  }\n  function x(r) {\n    if (null != r) {\n      var e = r[i];\n      if (e) return e.call(r);\n      if (\"function\" == typeof r.next) return r;\n      if (!isNaN(r.length)) {\n        var o = -1,\n          a = function e() {\n            for (; ++o < r.length;) if (n.call(r, o)) return e.value = r[o], e.done = !1, e;\n            return e.value = t, e.done = !0, e;\n          };\n        return a.next = a;\n      }\n    }\n    throw new TypeError(_typeof(r) + \" is not iterable\");\n  }\n  return GeneratorFunction.prototype = GeneratorFunctionPrototype, c(v, \"constructor\", GeneratorFunctionPrototype), c(GeneratorFunctionPrototype, \"constructor\", GeneratorFunction), c(GeneratorFunctionPrototype, u, GeneratorFunction.displayName = \"GeneratorFunction\"), r.isGeneratorFunction = function (t) {\n    var r = \"function\" == typeof t && t.constructor;\n    return !!r && (r === GeneratorFunction || \"GeneratorFunction\" === (r.displayName || r.name));\n  }, r.mark = function (t) {\n    return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, c(t, u, \"GeneratorFunction\")), t.prototype = Object.create(v), t;\n  }, r.awrap = function (t) {\n    return {\n      __await: t\n    };\n  }, g(AsyncIterator.prototype), c(AsyncIterator.prototype, a, function () {\n    return this;\n  }), r.AsyncIterator = AsyncIterator, r.async = function (t, e, n, o, i) {\n    void 0 === i && (i = Promise);\n    var a = new AsyncIterator(h(t, e, n, o), i);\n    return r.isGeneratorFunction(e) ? a : a.next().then(function (t) {\n      return t.done ? t.value : a.next();\n    });\n  }, g(v), c(v, u, \"Generator\"), c(v, i, function () {\n    return this;\n  }), c(v, \"toString\", function () {\n    return \"[object Generator]\";\n  }), r.keys = function (t) {\n    var r = Object(t),\n      e = [];\n    for (var n in r) e.unshift(n);\n    return function t() {\n      for (; e.length;) if ((n = e.pop()) in r) return t.value = n, t.done = !1, t;\n      return t.done = !0, t;\n    };\n  }, r.values = x, Context.prototype = {\n    constructor: Context,\n    reset: function reset(r) {\n      if (this.prev = this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(m), !r) for (var e in this) \"t\" === e.charAt(0) && n.call(this, e) && !isNaN(+e.slice(1)) && (this[e] = t);\n    },\n    stop: function stop() {\n      this.done = !0;\n      var t = this.tryEntries[0][4];\n      if (\"throw\" === t.type) throw t.arg;\n      return this.rval;\n    },\n    dispatchException: function dispatchException(r) {\n      if (this.done) throw r;\n      var e = this;\n      function n(t) {\n        a.type = \"throw\", a.arg = r, e.next = t;\n      }\n      for (var o = e.tryEntries.length - 1; o >= 0; --o) {\n        var i = this.tryEntries[o],\n          a = i[4],\n          u = this.prev,\n          c = i[1],\n          h = i[2];\n        if (-1 === i[0]) return n(\"end\"), !1;\n        if (!c && !h) throw Error(\"try statement without catch or finally\");\n        if (null != i[0] && i[0] <= u) {\n          if (u < c) return this.method = \"next\", this.arg = t, n(c), !0;\n          if (u < h) return n(h), !1;\n        }\n      }\n    },\n    abrupt: function abrupt(t, r) {\n      for (var e = this.tryEntries.length - 1; e >= 0; --e) {\n        var n = this.tryEntries[e];\n        if (n[0] > -1 && n[0] <= this.prev && this.prev < n[2]) {\n          var o = n;\n          break;\n        }\n      }\n      o && (\"break\" === t || \"continue\" === t) && o[0] <= r && r <= o[2] && (o = null);\n      var i = o ? o[4] : {};\n      return i.type = t, i.arg = r, o ? (this.method = \"next\", this.next = o[2], f) : this.complete(i);\n    },\n    complete: function complete(t, r) {\n      if (\"throw\" === t.type) throw t.arg;\n      return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && r && (this.next = r), f;\n    },\n    finish: function finish(t) {\n      for (var r = this.tryEntries.length - 1; r >= 0; --r) {\n        var e = this.tryEntries[r];\n        if (e[2] === t) return this.complete(e[4], e[3]), m(e), f;\n      }\n    },\n    \"catch\": function _catch(t) {\n      for (var r = this.tryEntries.length - 1; r >= 0; --r) {\n        var e = this.tryEntries[r];\n        if (e[0] === t) {\n          var n = e[4];\n          if (\"throw\" === n.type) {\n            var o = n.arg;\n            m(e);\n          }\n          return o;\n        }\n      }\n      throw Error(\"illegal catch attempt\");\n    },\n    delegateYield: function delegateYield(r, e, n) {\n      return this.delegate = {\n        i: x(r),\n        r: e,\n        n: n\n      }, \"next\" === this.method && (this.arg = t), f;\n    }\n  }, r;\n}\nmodule.exports = _regeneratorRuntime, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/regeneratorRuntime.js?");

/***/ }),

/***/ "./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/typeof.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/typeof.js ***!
  \************************************************************************************************/
/***/ ((module) => {

eval("function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return module.exports = _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports, _typeof(o);\n}\nmodule.exports = _typeof, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/typeof.js?");

/***/ }),

/***/ "./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/regenerator/index.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/regenerator/index.js ***!
  \***************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// TODO(Babel 8): Remove this file.\n\nvar runtime = __webpack_require__(/*! ../helpers/regeneratorRuntime */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/regeneratorRuntime.js\")();\nmodule.exports = runtime;\n\n// Copied from https://github.com/facebook/regenerator/blob/main/packages/runtime/runtime.js#L736=\ntry {\n  regeneratorRuntime = runtime;\n} catch (accidentalStrictMode) {\n  if (typeof globalThis === \"object\") {\n    globalThis.regeneratorRuntime = runtime;\n  } else {\n    Function(\"r\", \"regeneratorRuntime = r\")(runtime);\n  }\n}\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/regenerator/index.js?");

/***/ }),

/***/ "./node_modules/.pnpm/animejs@3.2.2/node_modules/animejs/lib/anime.es.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/animejs@3.2.2/node_modules/animejs/lib/anime.es.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/*\n * anime.js v3.2.2\n * (c) 2023 Julian Garnier\n * Released under the MIT license\n * animejs.com\n */\n\n// Defaults\n\nvar defaultInstanceSettings = {\n  update: null,\n  begin: null,\n  loopBegin: null,\n  changeBegin: null,\n  change: null,\n  changeComplete: null,\n  loopComplete: null,\n  complete: null,\n  loop: 1,\n  direction: 'normal',\n  autoplay: true,\n  timelineOffset: 0\n};\n\nvar defaultTweenSettings = {\n  duration: 1000,\n  delay: 0,\n  endDelay: 0,\n  easing: 'easeOutElastic(1, .5)',\n  round: 0\n};\n\nvar validTransforms = ['translateX', 'translateY', 'translateZ', 'rotate', 'rotateX', 'rotateY', 'rotateZ', 'scale', 'scaleX', 'scaleY', 'scaleZ', 'skew', 'skewX', 'skewY', 'perspective', 'matrix', 'matrix3d'];\n\n// Caching\n\nvar cache = {\n  CSS: {},\n  springs: {}\n};\n\n// Utils\n\nfunction minMax(val, min, max) {\n  return Math.min(Math.max(val, min), max);\n}\n\nfunction stringContains(str, text) {\n  return str.indexOf(text) > -1;\n}\n\nfunction applyArguments(func, args) {\n  return func.apply(null, args);\n}\n\nvar is = {\n  arr: function (a) { return Array.isArray(a); },\n  obj: function (a) { return stringContains(Object.prototype.toString.call(a), 'Object'); },\n  pth: function (a) { return is.obj(a) && a.hasOwnProperty('totalLength'); },\n  svg: function (a) { return a instanceof SVGElement; },\n  inp: function (a) { return a instanceof HTMLInputElement; },\n  dom: function (a) { return a.nodeType || is.svg(a); },\n  str: function (a) { return typeof a === 'string'; },\n  fnc: function (a) { return typeof a === 'function'; },\n  und: function (a) { return typeof a === 'undefined'; },\n  nil: function (a) { return is.und(a) || a === null; },\n  hex: function (a) { return /(^#[0-9A-F]{6}$)|(^#[0-9A-F]{3}$)/i.test(a); },\n  rgb: function (a) { return /^rgb/.test(a); },\n  hsl: function (a) { return /^hsl/.test(a); },\n  col: function (a) { return (is.hex(a) || is.rgb(a) || is.hsl(a)); },\n  key: function (a) { return !defaultInstanceSettings.hasOwnProperty(a) && !defaultTweenSettings.hasOwnProperty(a) && a !== 'targets' && a !== 'keyframes'; },\n};\n\n// Easings\n\nfunction parseEasingParameters(string) {\n  var match = /\\(([^)]+)\\)/.exec(string);\n  return match ? match[1].split(',').map(function (p) { return parseFloat(p); }) : [];\n}\n\n// Spring solver inspired by Webkit Copyright © 2016 Apple Inc. All rights reserved. https://webkit.org/demos/spring/spring.js\n\nfunction spring(string, duration) {\n\n  var params = parseEasingParameters(string);\n  var mass = minMax(is.und(params[0]) ? 1 : params[0], .1, 100);\n  var stiffness = minMax(is.und(params[1]) ? 100 : params[1], .1, 100);\n  var damping = minMax(is.und(params[2]) ? 10 : params[2], .1, 100);\n  var velocity =  minMax(is.und(params[3]) ? 0 : params[3], .1, 100);\n  var w0 = Math.sqrt(stiffness / mass);\n  var zeta = damping / (2 * Math.sqrt(stiffness * mass));\n  var wd = zeta < 1 ? w0 * Math.sqrt(1 - zeta * zeta) : 0;\n  var a = 1;\n  var b = zeta < 1 ? (zeta * w0 + -velocity) / wd : -velocity + w0;\n\n  function solver(t) {\n    var progress = duration ? (duration * t) / 1000 : t;\n    if (zeta < 1) {\n      progress = Math.exp(-progress * zeta * w0) * (a * Math.cos(wd * progress) + b * Math.sin(wd * progress));\n    } else {\n      progress = (a + b * progress) * Math.exp(-progress * w0);\n    }\n    if (t === 0 || t === 1) { return t; }\n    return 1 - progress;\n  }\n\n  function getDuration() {\n    var cached = cache.springs[string];\n    if (cached) { return cached; }\n    var frame = 1/6;\n    var elapsed = 0;\n    var rest = 0;\n    while(true) {\n      elapsed += frame;\n      if (solver(elapsed) === 1) {\n        rest++;\n        if (rest >= 16) { break; }\n      } else {\n        rest = 0;\n      }\n    }\n    var duration = elapsed * frame * 1000;\n    cache.springs[string] = duration;\n    return duration;\n  }\n\n  return duration ? solver : getDuration;\n\n}\n\n// Basic steps easing implementation https://developer.mozilla.org/fr/docs/Web/CSS/transition-timing-function\n\nfunction steps(steps) {\n  if ( steps === void 0 ) steps = 10;\n\n  return function (t) { return Math.ceil((minMax(t, 0.000001, 1)) * steps) * (1 / steps); };\n}\n\n// BezierEasing https://github.com/gre/bezier-easing\n\nvar bezier = (function () {\n\n  var kSplineTableSize = 11;\n  var kSampleStepSize = 1.0 / (kSplineTableSize - 1.0);\n\n  function A(aA1, aA2) { return 1.0 - 3.0 * aA2 + 3.0 * aA1 }\n  function B(aA1, aA2) { return 3.0 * aA2 - 6.0 * aA1 }\n  function C(aA1)      { return 3.0 * aA1 }\n\n  function calcBezier(aT, aA1, aA2) { return ((A(aA1, aA2) * aT + B(aA1, aA2)) * aT + C(aA1)) * aT }\n  function getSlope(aT, aA1, aA2) { return 3.0 * A(aA1, aA2) * aT * aT + 2.0 * B(aA1, aA2) * aT + C(aA1) }\n\n  function binarySubdivide(aX, aA, aB, mX1, mX2) {\n    var currentX, currentT, i = 0;\n    do {\n      currentT = aA + (aB - aA) / 2.0;\n      currentX = calcBezier(currentT, mX1, mX2) - aX;\n      if (currentX > 0.0) { aB = currentT; } else { aA = currentT; }\n    } while (Math.abs(currentX) > 0.0000001 && ++i < 10);\n    return currentT;\n  }\n\n  function newtonRaphsonIterate(aX, aGuessT, mX1, mX2) {\n    for (var i = 0; i < 4; ++i) {\n      var currentSlope = getSlope(aGuessT, mX1, mX2);\n      if (currentSlope === 0.0) { return aGuessT; }\n      var currentX = calcBezier(aGuessT, mX1, mX2) - aX;\n      aGuessT -= currentX / currentSlope;\n    }\n    return aGuessT;\n  }\n\n  function bezier(mX1, mY1, mX2, mY2) {\n\n    if (!(0 <= mX1 && mX1 <= 1 && 0 <= mX2 && mX2 <= 1)) { return; }\n    var sampleValues = new Float32Array(kSplineTableSize);\n\n    if (mX1 !== mY1 || mX2 !== mY2) {\n      for (var i = 0; i < kSplineTableSize; ++i) {\n        sampleValues[i] = calcBezier(i * kSampleStepSize, mX1, mX2);\n      }\n    }\n\n    function getTForX(aX) {\n\n      var intervalStart = 0;\n      var currentSample = 1;\n      var lastSample = kSplineTableSize - 1;\n\n      for (; currentSample !== lastSample && sampleValues[currentSample] <= aX; ++currentSample) {\n        intervalStart += kSampleStepSize;\n      }\n\n      --currentSample;\n\n      var dist = (aX - sampleValues[currentSample]) / (sampleValues[currentSample + 1] - sampleValues[currentSample]);\n      var guessForT = intervalStart + dist * kSampleStepSize;\n      var initialSlope = getSlope(guessForT, mX1, mX2);\n\n      if (initialSlope >= 0.001) {\n        return newtonRaphsonIterate(aX, guessForT, mX1, mX2);\n      } else if (initialSlope === 0.0) {\n        return guessForT;\n      } else {\n        return binarySubdivide(aX, intervalStart, intervalStart + kSampleStepSize, mX1, mX2);\n      }\n\n    }\n\n    return function (x) {\n      if (mX1 === mY1 && mX2 === mY2) { return x; }\n      if (x === 0 || x === 1) { return x; }\n      return calcBezier(getTForX(x), mY1, mY2);\n    }\n\n  }\n\n  return bezier;\n\n})();\n\nvar penner = (function () {\n\n  // Based on jQuery UI's implemenation of easing equations from Robert Penner (http://www.robertpenner.com/easing)\n\n  var eases = { linear: function () { return function (t) { return t; }; } };\n\n  var functionEasings = {\n    Sine: function () { return function (t) { return 1 - Math.cos(t * Math.PI / 2); }; },\n    Expo: function () { return function (t) { return t ? Math.pow(2, 10 * t - 10) : 0; }; },\n    Circ: function () { return function (t) { return 1 - Math.sqrt(1 - t * t); }; },\n    Back: function () { return function (t) { return t * t * (3 * t - 2); }; },\n    Bounce: function () { return function (t) {\n      var pow2, b = 4;\n      while (t < (( pow2 = Math.pow(2, --b)) - 1) / 11) {}\n      return 1 / Math.pow(4, 3 - b) - 7.5625 * Math.pow(( pow2 * 3 - 2 ) / 22 - t, 2)\n    }; },\n    Elastic: function (amplitude, period) {\n      if ( amplitude === void 0 ) amplitude = 1;\n      if ( period === void 0 ) period = .5;\n\n      var a = minMax(amplitude, 1, 10);\n      var p = minMax(period, .1, 2);\n      return function (t) {\n        return (t === 0 || t === 1) ? t : \n          -a * Math.pow(2, 10 * (t - 1)) * Math.sin((((t - 1) - (p / (Math.PI * 2) * Math.asin(1 / a))) * (Math.PI * 2)) / p);\n      }\n    }\n  };\n\n  var baseEasings = ['Quad', 'Cubic', 'Quart', 'Quint'];\n\n  baseEasings.forEach(function (name, i) {\n    functionEasings[name] = function () { return function (t) { return Math.pow(t, i + 2); }; };\n  });\n\n  Object.keys(functionEasings).forEach(function (name) {\n    var easeIn = functionEasings[name];\n    eases['easeIn' + name] = easeIn;\n    eases['easeOut' + name] = function (a, b) { return function (t) { return 1 - easeIn(a, b)(1 - t); }; };\n    eases['easeInOut' + name] = function (a, b) { return function (t) { return t < 0.5 ? easeIn(a, b)(t * 2) / 2 : \n      1 - easeIn(a, b)(t * -2 + 2) / 2; }; };\n    eases['easeOutIn' + name] = function (a, b) { return function (t) { return t < 0.5 ? (1 - easeIn(a, b)(1 - t * 2)) / 2 : \n      (easeIn(a, b)(t * 2 - 1) + 1) / 2; }; };\n  });\n\n  return eases;\n\n})();\n\nfunction parseEasings(easing, duration) {\n  if (is.fnc(easing)) { return easing; }\n  var name = easing.split('(')[0];\n  var ease = penner[name];\n  var args = parseEasingParameters(easing);\n  switch (name) {\n    case 'spring' : return spring(easing, duration);\n    case 'cubicBezier' : return applyArguments(bezier, args);\n    case 'steps' : return applyArguments(steps, args);\n    default : return applyArguments(ease, args);\n  }\n}\n\n// Strings\n\nfunction selectString(str) {\n  try {\n    var nodes = document.querySelectorAll(str);\n    return nodes;\n  } catch(e) {\n    return;\n  }\n}\n\n// Arrays\n\nfunction filterArray(arr, callback) {\n  var len = arr.length;\n  var thisArg = arguments.length >= 2 ? arguments[1] : void 0;\n  var result = [];\n  for (var i = 0; i < len; i++) {\n    if (i in arr) {\n      var val = arr[i];\n      if (callback.call(thisArg, val, i, arr)) {\n        result.push(val);\n      }\n    }\n  }\n  return result;\n}\n\nfunction flattenArray(arr) {\n  return arr.reduce(function (a, b) { return a.concat(is.arr(b) ? flattenArray(b) : b); }, []);\n}\n\nfunction toArray(o) {\n  if (is.arr(o)) { return o; }\n  if (is.str(o)) { o = selectString(o) || o; }\n  if (o instanceof NodeList || o instanceof HTMLCollection) { return [].slice.call(o); }\n  return [o];\n}\n\nfunction arrayContains(arr, val) {\n  return arr.some(function (a) { return a === val; });\n}\n\n// Objects\n\nfunction cloneObject(o) {\n  var clone = {};\n  for (var p in o) { clone[p] = o[p]; }\n  return clone;\n}\n\nfunction replaceObjectProps(o1, o2) {\n  var o = cloneObject(o1);\n  for (var p in o1) { o[p] = o2.hasOwnProperty(p) ? o2[p] : o1[p]; }\n  return o;\n}\n\nfunction mergeObjects(o1, o2) {\n  var o = cloneObject(o1);\n  for (var p in o2) { o[p] = is.und(o1[p]) ? o2[p] : o1[p]; }\n  return o;\n}\n\n// Colors\n\nfunction rgbToRgba(rgbValue) {\n  var rgb = /rgb\\((\\d+,\\s*[\\d]+,\\s*[\\d]+)\\)/g.exec(rgbValue);\n  return rgb ? (\"rgba(\" + (rgb[1]) + \",1)\") : rgbValue;\n}\n\nfunction hexToRgba(hexValue) {\n  var rgx = /^#?([a-f\\d])([a-f\\d])([a-f\\d])$/i;\n  var hex = hexValue.replace(rgx, function (m, r, g, b) { return r + r + g + g + b + b; } );\n  var rgb = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex);\n  var r = parseInt(rgb[1], 16);\n  var g = parseInt(rgb[2], 16);\n  var b = parseInt(rgb[3], 16);\n  return (\"rgba(\" + r + \",\" + g + \",\" + b + \",1)\");\n}\n\nfunction hslToRgba(hslValue) {\n  var hsl = /hsl\\((\\d+),\\s*([\\d.]+)%,\\s*([\\d.]+)%\\)/g.exec(hslValue) || /hsla\\((\\d+),\\s*([\\d.]+)%,\\s*([\\d.]+)%,\\s*([\\d.]+)\\)/g.exec(hslValue);\n  var h = parseInt(hsl[1], 10) / 360;\n  var s = parseInt(hsl[2], 10) / 100;\n  var l = parseInt(hsl[3], 10) / 100;\n  var a = hsl[4] || 1;\n  function hue2rgb(p, q, t) {\n    if (t < 0) { t += 1; }\n    if (t > 1) { t -= 1; }\n    if (t < 1/6) { return p + (q - p) * 6 * t; }\n    if (t < 1/2) { return q; }\n    if (t < 2/3) { return p + (q - p) * (2/3 - t) * 6; }\n    return p;\n  }\n  var r, g, b;\n  if (s == 0) {\n    r = g = b = l;\n  } else {\n    var q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n    var p = 2 * l - q;\n    r = hue2rgb(p, q, h + 1/3);\n    g = hue2rgb(p, q, h);\n    b = hue2rgb(p, q, h - 1/3);\n  }\n  return (\"rgba(\" + (r * 255) + \",\" + (g * 255) + \",\" + (b * 255) + \",\" + a + \")\");\n}\n\nfunction colorToRgb(val) {\n  if (is.rgb(val)) { return rgbToRgba(val); }\n  if (is.hex(val)) { return hexToRgba(val); }\n  if (is.hsl(val)) { return hslToRgba(val); }\n}\n\n// Units\n\nfunction getUnit(val) {\n  var split = /[+-]?\\d*\\.?\\d+(?:\\.\\d+)?(?:[eE][+-]?\\d+)?(%|px|pt|em|rem|in|cm|mm|ex|ch|pc|vw|vh|vmin|vmax|deg|rad|turn)?$/.exec(val);\n  if (split) { return split[1]; }\n}\n\nfunction getTransformUnit(propName) {\n  if (stringContains(propName, 'translate') || propName === 'perspective') { return 'px'; }\n  if (stringContains(propName, 'rotate') || stringContains(propName, 'skew')) { return 'deg'; }\n}\n\n// Values\n\nfunction getFunctionValue(val, animatable) {\n  if (!is.fnc(val)) { return val; }\n  return val(animatable.target, animatable.id, animatable.total);\n}\n\nfunction getAttribute(el, prop) {\n  return el.getAttribute(prop);\n}\n\nfunction convertPxToUnit(el, value, unit) {\n  var valueUnit = getUnit(value);\n  if (arrayContains([unit, 'deg', 'rad', 'turn'], valueUnit)) { return value; }\n  var cached = cache.CSS[value + unit];\n  if (!is.und(cached)) { return cached; }\n  var baseline = 100;\n  var tempEl = document.createElement(el.tagName);\n  var parentEl = (el.parentNode && (el.parentNode !== document)) ? el.parentNode : document.body;\n  parentEl.appendChild(tempEl);\n  tempEl.style.position = 'absolute';\n  tempEl.style.width = baseline + unit;\n  var factor = baseline / tempEl.offsetWidth;\n  parentEl.removeChild(tempEl);\n  var convertedUnit = factor * parseFloat(value);\n  cache.CSS[value + unit] = convertedUnit;\n  return convertedUnit;\n}\n\nfunction getCSSValue(el, prop, unit) {\n  if (prop in el.style) {\n    var uppercasePropName = prop.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();\n    var value = el.style[prop] || getComputedStyle(el).getPropertyValue(uppercasePropName) || '0';\n    return unit ? convertPxToUnit(el, value, unit) : value;\n  }\n}\n\nfunction getAnimationType(el, prop) {\n  if (is.dom(el) && !is.inp(el) && (!is.nil(getAttribute(el, prop)) || (is.svg(el) && el[prop]))) { return 'attribute'; }\n  if (is.dom(el) && arrayContains(validTransforms, prop)) { return 'transform'; }\n  if (is.dom(el) && (prop !== 'transform' && getCSSValue(el, prop))) { return 'css'; }\n  if (el[prop] != null) { return 'object'; }\n}\n\nfunction getElementTransforms(el) {\n  if (!is.dom(el)) { return; }\n  var str = el.style.transform || '';\n  var reg  = /(\\w+)\\(([^)]*)\\)/g;\n  var transforms = new Map();\n  var m; while (m = reg.exec(str)) { transforms.set(m[1], m[2]); }\n  return transforms;\n}\n\nfunction getTransformValue(el, propName, animatable, unit) {\n  var defaultVal = stringContains(propName, 'scale') ? 1 : 0 + getTransformUnit(propName);\n  var value = getElementTransforms(el).get(propName) || defaultVal;\n  if (animatable) {\n    animatable.transforms.list.set(propName, value);\n    animatable.transforms['last'] = propName;\n  }\n  return unit ? convertPxToUnit(el, value, unit) : value;\n}\n\nfunction getOriginalTargetValue(target, propName, unit, animatable) {\n  switch (getAnimationType(target, propName)) {\n    case 'transform': return getTransformValue(target, propName, animatable, unit);\n    case 'css': return getCSSValue(target, propName, unit);\n    case 'attribute': return getAttribute(target, propName);\n    default: return target[propName] || 0;\n  }\n}\n\nfunction getRelativeValue(to, from) {\n  var operator = /^(\\*=|\\+=|-=)/.exec(to);\n  if (!operator) { return to; }\n  var u = getUnit(to) || 0;\n  var x = parseFloat(from);\n  var y = parseFloat(to.replace(operator[0], ''));\n  switch (operator[0][0]) {\n    case '+': return x + y + u;\n    case '-': return x - y + u;\n    case '*': return x * y + u;\n  }\n}\n\nfunction validateValue(val, unit) {\n  if (is.col(val)) { return colorToRgb(val); }\n  if (/\\s/g.test(val)) { return val; }\n  var originalUnit = getUnit(val);\n  var unitLess = originalUnit ? val.substr(0, val.length - originalUnit.length) : val;\n  if (unit) { return unitLess + unit; }\n  return unitLess;\n}\n\n// getTotalLength() equivalent for circle, rect, polyline, polygon and line shapes\n// adapted from https://gist.github.com/SebLambla/3e0550c496c236709744\n\nfunction getDistance(p1, p2) {\n  return Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2));\n}\n\nfunction getCircleLength(el) {\n  return Math.PI * 2 * getAttribute(el, 'r');\n}\n\nfunction getRectLength(el) {\n  return (getAttribute(el, 'width') * 2) + (getAttribute(el, 'height') * 2);\n}\n\nfunction getLineLength(el) {\n  return getDistance(\n    {x: getAttribute(el, 'x1'), y: getAttribute(el, 'y1')}, \n    {x: getAttribute(el, 'x2'), y: getAttribute(el, 'y2')}\n  );\n}\n\nfunction getPolylineLength(el) {\n  var points = el.points;\n  var totalLength = 0;\n  var previousPos;\n  for (var i = 0 ; i < points.numberOfItems; i++) {\n    var currentPos = points.getItem(i);\n    if (i > 0) { totalLength += getDistance(previousPos, currentPos); }\n    previousPos = currentPos;\n  }\n  return totalLength;\n}\n\nfunction getPolygonLength(el) {\n  var points = el.points;\n  return getPolylineLength(el) + getDistance(points.getItem(points.numberOfItems - 1), points.getItem(0));\n}\n\n// Path animation\n\nfunction getTotalLength(el) {\n  if (el.getTotalLength) { return el.getTotalLength(); }\n  switch(el.tagName.toLowerCase()) {\n    case 'circle': return getCircleLength(el);\n    case 'rect': return getRectLength(el);\n    case 'line': return getLineLength(el);\n    case 'polyline': return getPolylineLength(el);\n    case 'polygon': return getPolygonLength(el);\n  }\n}\n\nfunction setDashoffset(el) {\n  var pathLength = getTotalLength(el);\n  el.setAttribute('stroke-dasharray', pathLength);\n  return pathLength;\n}\n\n// Motion path\n\nfunction getParentSvgEl(el) {\n  var parentEl = el.parentNode;\n  while (is.svg(parentEl)) {\n    if (!is.svg(parentEl.parentNode)) { break; }\n    parentEl = parentEl.parentNode;\n  }\n  return parentEl;\n}\n\nfunction getParentSvg(pathEl, svgData) {\n  var svg = svgData || {};\n  var parentSvgEl = svg.el || getParentSvgEl(pathEl);\n  var rect = parentSvgEl.getBoundingClientRect();\n  var viewBoxAttr = getAttribute(parentSvgEl, 'viewBox');\n  var width = rect.width;\n  var height = rect.height;\n  var viewBox = svg.viewBox || (viewBoxAttr ? viewBoxAttr.split(' ') : [0, 0, width, height]);\n  return {\n    el: parentSvgEl,\n    viewBox: viewBox,\n    x: viewBox[0] / 1,\n    y: viewBox[1] / 1,\n    w: width,\n    h: height,\n    vW: viewBox[2],\n    vH: viewBox[3]\n  }\n}\n\nfunction getPath(path, percent) {\n  var pathEl = is.str(path) ? selectString(path)[0] : path;\n  var p = percent || 100;\n  return function(property) {\n    return {\n      property: property,\n      el: pathEl,\n      svg: getParentSvg(pathEl),\n      totalLength: getTotalLength(pathEl) * (p / 100)\n    }\n  }\n}\n\nfunction getPathProgress(path, progress, isPathTargetInsideSVG) {\n  function point(offset) {\n    if ( offset === void 0 ) offset = 0;\n\n    var l = progress + offset >= 1 ? progress + offset : 0;\n    return path.el.getPointAtLength(l);\n  }\n  var svg = getParentSvg(path.el, path.svg);\n  var p = point();\n  var p0 = point(-1);\n  var p1 = point(+1);\n  var scaleX = isPathTargetInsideSVG ? 1 : svg.w / svg.vW;\n  var scaleY = isPathTargetInsideSVG ? 1 : svg.h / svg.vH;\n  switch (path.property) {\n    case 'x': return (p.x - svg.x) * scaleX;\n    case 'y': return (p.y - svg.y) * scaleY;\n    case 'angle': return Math.atan2(p1.y - p0.y, p1.x - p0.x) * 180 / Math.PI;\n  }\n}\n\n// Decompose value\n\nfunction decomposeValue(val, unit) {\n  // const rgx = /-?\\d*\\.?\\d+/g; // handles basic numbers\n  // const rgx = /[+-]?\\d+(?:\\.\\d+)?(?:[eE][+-]?\\d+)?/g; // handles exponents notation\n  var rgx = /[+-]?\\d*\\.?\\d+(?:\\.\\d+)?(?:[eE][+-]?\\d+)?/g; // handles exponents notation\n  var value = validateValue((is.pth(val) ? val.totalLength : val), unit) + '';\n  return {\n    original: value,\n    numbers: value.match(rgx) ? value.match(rgx).map(Number) : [0],\n    strings: (is.str(val) || unit) ? value.split(rgx) : []\n  }\n}\n\n// Animatables\n\nfunction parseTargets(targets) {\n  var targetsArray = targets ? (flattenArray(is.arr(targets) ? targets.map(toArray) : toArray(targets))) : [];\n  return filterArray(targetsArray, function (item, pos, self) { return self.indexOf(item) === pos; });\n}\n\nfunction getAnimatables(targets) {\n  var parsed = parseTargets(targets);\n  return parsed.map(function (t, i) {\n    return {target: t, id: i, total: parsed.length, transforms: { list: getElementTransforms(t) } };\n  });\n}\n\n// Properties\n\nfunction normalizePropertyTweens(prop, tweenSettings) {\n  var settings = cloneObject(tweenSettings);\n  // Override duration if easing is a spring\n  if (/^spring/.test(settings.easing)) { settings.duration = spring(settings.easing); }\n  if (is.arr(prop)) {\n    var l = prop.length;\n    var isFromTo = (l === 2 && !is.obj(prop[0]));\n    if (!isFromTo) {\n      // Duration divided by the number of tweens\n      if (!is.fnc(tweenSettings.duration)) { settings.duration = tweenSettings.duration / l; }\n    } else {\n      // Transform [from, to] values shorthand to a valid tween value\n      prop = {value: prop};\n    }\n  }\n  var propArray = is.arr(prop) ? prop : [prop];\n  return propArray.map(function (v, i) {\n    var obj = (is.obj(v) && !is.pth(v)) ? v : {value: v};\n    // Default delay value should only be applied to the first tween\n    if (is.und(obj.delay)) { obj.delay = !i ? tweenSettings.delay : 0; }\n    // Default endDelay value should only be applied to the last tween\n    if (is.und(obj.endDelay)) { obj.endDelay = i === propArray.length - 1 ? tweenSettings.endDelay : 0; }\n    return obj;\n  }).map(function (k) { return mergeObjects(k, settings); });\n}\n\n\nfunction flattenKeyframes(keyframes) {\n  var propertyNames = filterArray(flattenArray(keyframes.map(function (key) { return Object.keys(key); })), function (p) { return is.key(p); })\n  .reduce(function (a,b) { if (a.indexOf(b) < 0) { a.push(b); } return a; }, []);\n  var properties = {};\n  var loop = function ( i ) {\n    var propName = propertyNames[i];\n    properties[propName] = keyframes.map(function (key) {\n      var newKey = {};\n      for (var p in key) {\n        if (is.key(p)) {\n          if (p == propName) { newKey.value = key[p]; }\n        } else {\n          newKey[p] = key[p];\n        }\n      }\n      return newKey;\n    });\n  };\n\n  for (var i = 0; i < propertyNames.length; i++) loop( i );\n  return properties;\n}\n\nfunction getProperties(tweenSettings, params) {\n  var properties = [];\n  var keyframes = params.keyframes;\n  if (keyframes) { params = mergeObjects(flattenKeyframes(keyframes), params); }\n  for (var p in params) {\n    if (is.key(p)) {\n      properties.push({\n        name: p,\n        tweens: normalizePropertyTweens(params[p], tweenSettings)\n      });\n    }\n  }\n  return properties;\n}\n\n// Tweens\n\nfunction normalizeTweenValues(tween, animatable) {\n  var t = {};\n  for (var p in tween) {\n    var value = getFunctionValue(tween[p], animatable);\n    if (is.arr(value)) {\n      value = value.map(function (v) { return getFunctionValue(v, animatable); });\n      if (value.length === 1) { value = value[0]; }\n    }\n    t[p] = value;\n  }\n  t.duration = parseFloat(t.duration);\n  t.delay = parseFloat(t.delay);\n  return t;\n}\n\nfunction normalizeTweens(prop, animatable) {\n  var previousTween;\n  return prop.tweens.map(function (t) {\n    var tween = normalizeTweenValues(t, animatable);\n    var tweenValue = tween.value;\n    var to = is.arr(tweenValue) ? tweenValue[1] : tweenValue;\n    var toUnit = getUnit(to);\n    var originalValue = getOriginalTargetValue(animatable.target, prop.name, toUnit, animatable);\n    var previousValue = previousTween ? previousTween.to.original : originalValue;\n    var from = is.arr(tweenValue) ? tweenValue[0] : previousValue;\n    var fromUnit = getUnit(from) || getUnit(originalValue);\n    var unit = toUnit || fromUnit;\n    if (is.und(to)) { to = previousValue; }\n    tween.from = decomposeValue(from, unit);\n    tween.to = decomposeValue(getRelativeValue(to, from), unit);\n    tween.start = previousTween ? previousTween.end : 0;\n    tween.end = tween.start + tween.delay + tween.duration + tween.endDelay;\n    tween.easing = parseEasings(tween.easing, tween.duration);\n    tween.isPath = is.pth(tweenValue);\n    tween.isPathTargetInsideSVG = tween.isPath && is.svg(animatable.target);\n    tween.isColor = is.col(tween.from.original);\n    if (tween.isColor) { tween.round = 1; }\n    previousTween = tween;\n    return tween;\n  });\n}\n\n// Tween progress\n\nvar setProgressValue = {\n  css: function (t, p, v) { return t.style[p] = v; },\n  attribute: function (t, p, v) { return t.setAttribute(p, v); },\n  object: function (t, p, v) { return t[p] = v; },\n  transform: function (t, p, v, transforms, manual) {\n    transforms.list.set(p, v);\n    if (p === transforms.last || manual) {\n      var str = '';\n      transforms.list.forEach(function (value, prop) { str += prop + \"(\" + value + \") \"; });\n      t.style.transform = str;\n    }\n  }\n};\n\n// Set Value helper\n\nfunction setTargetsValue(targets, properties) {\n  var animatables = getAnimatables(targets);\n  animatables.forEach(function (animatable) {\n    for (var property in properties) {\n      var value = getFunctionValue(properties[property], animatable);\n      var target = animatable.target;\n      var valueUnit = getUnit(value);\n      var originalValue = getOriginalTargetValue(target, property, valueUnit, animatable);\n      var unit = valueUnit || getUnit(originalValue);\n      var to = getRelativeValue(validateValue(value, unit), originalValue);\n      var animType = getAnimationType(target, property);\n      setProgressValue[animType](target, property, to, animatable.transforms, true);\n    }\n  });\n}\n\n// Animations\n\nfunction createAnimation(animatable, prop) {\n  var animType = getAnimationType(animatable.target, prop.name);\n  if (animType) {\n    var tweens = normalizeTweens(prop, animatable);\n    var lastTween = tweens[tweens.length - 1];\n    return {\n      type: animType,\n      property: prop.name,\n      animatable: animatable,\n      tweens: tweens,\n      duration: lastTween.end,\n      delay: tweens[0].delay,\n      endDelay: lastTween.endDelay\n    }\n  }\n}\n\nfunction getAnimations(animatables, properties) {\n  return filterArray(flattenArray(animatables.map(function (animatable) {\n    return properties.map(function (prop) {\n      return createAnimation(animatable, prop);\n    });\n  })), function (a) { return !is.und(a); });\n}\n\n// Create Instance\n\nfunction getInstanceTimings(animations, tweenSettings) {\n  var animLength = animations.length;\n  var getTlOffset = function (anim) { return anim.timelineOffset ? anim.timelineOffset : 0; };\n  var timings = {};\n  timings.duration = animLength ? Math.max.apply(Math, animations.map(function (anim) { return getTlOffset(anim) + anim.duration; })) : tweenSettings.duration;\n  timings.delay = animLength ? Math.min.apply(Math, animations.map(function (anim) { return getTlOffset(anim) + anim.delay; })) : tweenSettings.delay;\n  timings.endDelay = animLength ? timings.duration - Math.max.apply(Math, animations.map(function (anim) { return getTlOffset(anim) + anim.duration - anim.endDelay; })) : tweenSettings.endDelay;\n  return timings;\n}\n\nvar instanceID = 0;\n\nfunction createNewInstance(params) {\n  var instanceSettings = replaceObjectProps(defaultInstanceSettings, params);\n  var tweenSettings = replaceObjectProps(defaultTweenSettings, params);\n  var properties = getProperties(tweenSettings, params);\n  var animatables = getAnimatables(params.targets);\n  var animations = getAnimations(animatables, properties);\n  var timings = getInstanceTimings(animations, tweenSettings);\n  var id = instanceID;\n  instanceID++;\n  return mergeObjects(instanceSettings, {\n    id: id,\n    children: [],\n    animatables: animatables,\n    animations: animations,\n    duration: timings.duration,\n    delay: timings.delay,\n    endDelay: timings.endDelay\n  });\n}\n\n// Core\n\nvar activeInstances = [];\n\nvar engine = (function () {\n  var raf;\n\n  function play() {\n    if (!raf && (!isDocumentHidden() || !anime.suspendWhenDocumentHidden) && activeInstances.length > 0) {\n      raf = requestAnimationFrame(step);\n    }\n  }\n  function step(t) {\n    // memo on algorithm issue:\n    // dangerous iteration over mutable `activeInstances`\n    // (that collection may be updated from within callbacks of `tick`-ed animation instances)\n    var activeInstancesLength = activeInstances.length;\n    var i = 0;\n    while (i < activeInstancesLength) {\n      var activeInstance = activeInstances[i];\n      if (!activeInstance.paused) {\n        activeInstance.tick(t);\n        i++;\n      } else {\n        activeInstances.splice(i, 1);\n        activeInstancesLength--;\n      }\n    }\n    raf = i > 0 ? requestAnimationFrame(step) : undefined;\n  }\n\n  function handleVisibilityChange() {\n    if (!anime.suspendWhenDocumentHidden) { return; }\n\n    if (isDocumentHidden()) {\n      // suspend ticks\n      raf = cancelAnimationFrame(raf);\n    } else { // is back to active tab\n      // first adjust animations to consider the time that ticks were suspended\n      activeInstances.forEach(\n        function (instance) { return instance ._onDocumentVisibility(); }\n      );\n      engine();\n    }\n  }\n  if (typeof document !== 'undefined') {\n    document.addEventListener('visibilitychange', handleVisibilityChange);\n  }\n\n  return play;\n})();\n\nfunction isDocumentHidden() {\n  return !!document && document.hidden;\n}\n\n// Public Instance\n\nfunction anime(params) {\n  if ( params === void 0 ) params = {};\n\n\n  var startTime = 0, lastTime = 0, now = 0;\n  var children, childrenLength = 0;\n  var resolve = null;\n\n  function makePromise(instance) {\n    var promise = window.Promise && new Promise(function (_resolve) { return resolve = _resolve; });\n    instance.finished = promise;\n    return promise;\n  }\n\n  var instance = createNewInstance(params);\n  var promise = makePromise(instance);\n\n  function toggleInstanceDirection() {\n    var direction = instance.direction;\n    if (direction !== 'alternate') {\n      instance.direction = direction !== 'normal' ? 'normal' : 'reverse';\n    }\n    instance.reversed = !instance.reversed;\n    children.forEach(function (child) { return child.reversed = instance.reversed; });\n  }\n\n  function adjustTime(time) {\n    return instance.reversed ? instance.duration - time : time;\n  }\n\n  function resetTime() {\n    startTime = 0;\n    lastTime = adjustTime(instance.currentTime) * (1 / anime.speed);\n  }\n\n  function seekChild(time, child) {\n    if (child) { child.seek(time - child.timelineOffset); }\n  }\n\n  function syncInstanceChildren(time) {\n    if (!instance.reversePlayback) {\n      for (var i = 0; i < childrenLength; i++) { seekChild(time, children[i]); }\n    } else {\n      for (var i$1 = childrenLength; i$1--;) { seekChild(time, children[i$1]); }\n    }\n  }\n\n  function setAnimationsProgress(insTime) {\n    var i = 0;\n    var animations = instance.animations;\n    var animationsLength = animations.length;\n    while (i < animationsLength) {\n      var anim = animations[i];\n      var animatable = anim.animatable;\n      var tweens = anim.tweens;\n      var tweenLength = tweens.length - 1;\n      var tween = tweens[tweenLength];\n      // Only check for keyframes if there is more than one tween\n      if (tweenLength) { tween = filterArray(tweens, function (t) { return (insTime < t.end); })[0] || tween; }\n      var elapsed = minMax(insTime - tween.start - tween.delay, 0, tween.duration) / tween.duration;\n      var eased = isNaN(elapsed) ? 1 : tween.easing(elapsed);\n      var strings = tween.to.strings;\n      var round = tween.round;\n      var numbers = [];\n      var toNumbersLength = tween.to.numbers.length;\n      var progress = (void 0);\n      for (var n = 0; n < toNumbersLength; n++) {\n        var value = (void 0);\n        var toNumber = tween.to.numbers[n];\n        var fromNumber = tween.from.numbers[n] || 0;\n        if (!tween.isPath) {\n          value = fromNumber + (eased * (toNumber - fromNumber));\n        } else {\n          value = getPathProgress(tween.value, eased * toNumber, tween.isPathTargetInsideSVG);\n        }\n        if (round) {\n          if (!(tween.isColor && n > 2)) {\n            value = Math.round(value * round) / round;\n          }\n        }\n        numbers.push(value);\n      }\n      // Manual Array.reduce for better performances\n      var stringsLength = strings.length;\n      if (!stringsLength) {\n        progress = numbers[0];\n      } else {\n        progress = strings[0];\n        for (var s = 0; s < stringsLength; s++) {\n          var a = strings[s];\n          var b = strings[s + 1];\n          var n$1 = numbers[s];\n          if (!isNaN(n$1)) {\n            if (!b) {\n              progress += n$1 + ' ';\n            } else {\n              progress += n$1 + b;\n            }\n          }\n        }\n      }\n      setProgressValue[anim.type](animatable.target, anim.property, progress, animatable.transforms);\n      anim.currentValue = progress;\n      i++;\n    }\n  }\n\n  function setCallback(cb) {\n    if (instance[cb] && !instance.passThrough) { instance[cb](instance); }\n  }\n\n  function countIteration() {\n    if (instance.remaining && instance.remaining !== true) {\n      instance.remaining--;\n    }\n  }\n\n  function setInstanceProgress(engineTime) {\n    var insDuration = instance.duration;\n    var insDelay = instance.delay;\n    var insEndDelay = insDuration - instance.endDelay;\n    var insTime = adjustTime(engineTime);\n    instance.progress = minMax((insTime / insDuration) * 100, 0, 100);\n    instance.reversePlayback = insTime < instance.currentTime;\n    if (children) { syncInstanceChildren(insTime); }\n    if (!instance.began && instance.currentTime > 0) {\n      instance.began = true;\n      setCallback('begin');\n    }\n    if (!instance.loopBegan && instance.currentTime > 0) {\n      instance.loopBegan = true;\n      setCallback('loopBegin');\n    }\n    if (insTime <= insDelay && instance.currentTime !== 0) {\n      setAnimationsProgress(0);\n    }\n    if ((insTime >= insEndDelay && instance.currentTime !== insDuration) || !insDuration) {\n      setAnimationsProgress(insDuration);\n    }\n    if (insTime > insDelay && insTime < insEndDelay) {\n      if (!instance.changeBegan) {\n        instance.changeBegan = true;\n        instance.changeCompleted = false;\n        setCallback('changeBegin');\n      }\n      setCallback('change');\n      setAnimationsProgress(insTime);\n    } else {\n      if (instance.changeBegan) {\n        instance.changeCompleted = true;\n        instance.changeBegan = false;\n        setCallback('changeComplete');\n      }\n    }\n    instance.currentTime = minMax(insTime, 0, insDuration);\n    if (instance.began) { setCallback('update'); }\n    if (engineTime >= insDuration) {\n      lastTime = 0;\n      countIteration();\n      if (!instance.remaining) {\n        instance.paused = true;\n        if (!instance.completed) {\n          instance.completed = true;\n          setCallback('loopComplete');\n          setCallback('complete');\n          if (!instance.passThrough && 'Promise' in window) {\n            resolve();\n            promise = makePromise(instance);\n          }\n        }\n      } else {\n        startTime = now;\n        setCallback('loopComplete');\n        instance.loopBegan = false;\n        if (instance.direction === 'alternate') {\n          toggleInstanceDirection();\n        }\n      }\n    }\n  }\n\n  instance.reset = function() {\n    var direction = instance.direction;\n    instance.passThrough = false;\n    instance.currentTime = 0;\n    instance.progress = 0;\n    instance.paused = true;\n    instance.began = false;\n    instance.loopBegan = false;\n    instance.changeBegan = false;\n    instance.completed = false;\n    instance.changeCompleted = false;\n    instance.reversePlayback = false;\n    instance.reversed = direction === 'reverse';\n    instance.remaining = instance.loop;\n    children = instance.children;\n    childrenLength = children.length;\n    for (var i = childrenLength; i--;) { instance.children[i].reset(); }\n    if (instance.reversed && instance.loop !== true || (direction === 'alternate' && instance.loop === 1)) { instance.remaining++; }\n    setAnimationsProgress(instance.reversed ? instance.duration : 0);\n  };\n\n  // internal method (for engine) to adjust animation timings before restoring engine ticks (rAF)\n  instance._onDocumentVisibility = resetTime;\n\n  // Set Value helper\n\n  instance.set = function(targets, properties) {\n    setTargetsValue(targets, properties);\n    return instance;\n  };\n\n  instance.tick = function(t) {\n    now = t;\n    if (!startTime) { startTime = now; }\n    setInstanceProgress((now + (lastTime - startTime)) * anime.speed);\n  };\n\n  instance.seek = function(time) {\n    setInstanceProgress(adjustTime(time));\n  };\n\n  instance.pause = function() {\n    instance.paused = true;\n    resetTime();\n  };\n\n  instance.play = function() {\n    if (!instance.paused) { return; }\n    if (instance.completed) { instance.reset(); }\n    instance.paused = false;\n    activeInstances.push(instance);\n    resetTime();\n    engine();\n  };\n\n  instance.reverse = function() {\n    toggleInstanceDirection();\n    instance.completed = instance.reversed ? false : true;\n    resetTime();\n  };\n\n  instance.restart = function() {\n    instance.reset();\n    instance.play();\n  };\n\n  instance.remove = function(targets) {\n    var targetsArray = parseTargets(targets);\n    removeTargetsFromInstance(targetsArray, instance);\n  };\n\n  instance.reset();\n\n  if (instance.autoplay) { instance.play(); }\n\n  return instance;\n\n}\n\n// Remove targets from animation\n\nfunction removeTargetsFromAnimations(targetsArray, animations) {\n  for (var a = animations.length; a--;) {\n    if (arrayContains(targetsArray, animations[a].animatable.target)) {\n      animations.splice(a, 1);\n    }\n  }\n}\n\nfunction removeTargetsFromInstance(targetsArray, instance) {\n  var animations = instance.animations;\n  var children = instance.children;\n  removeTargetsFromAnimations(targetsArray, animations);\n  for (var c = children.length; c--;) {\n    var child = children[c];\n    var childAnimations = child.animations;\n    removeTargetsFromAnimations(targetsArray, childAnimations);\n    if (!childAnimations.length && !child.children.length) { children.splice(c, 1); }\n  }\n  if (!animations.length && !children.length) { instance.pause(); }\n}\n\nfunction removeTargetsFromActiveInstances(targets) {\n  var targetsArray = parseTargets(targets);\n  for (var i = activeInstances.length; i--;) {\n    var instance = activeInstances[i];\n    removeTargetsFromInstance(targetsArray, instance);\n  }\n}\n\n// Stagger helpers\n\nfunction stagger(val, params) {\n  if ( params === void 0 ) params = {};\n\n  var direction = params.direction || 'normal';\n  var easing = params.easing ? parseEasings(params.easing) : null;\n  var grid = params.grid;\n  var axis = params.axis;\n  var fromIndex = params.from || 0;\n  var fromFirst = fromIndex === 'first';\n  var fromCenter = fromIndex === 'center';\n  var fromLast = fromIndex === 'last';\n  var isRange = is.arr(val);\n  var val1 = isRange ? parseFloat(val[0]) : parseFloat(val);\n  var val2 = isRange ? parseFloat(val[1]) : 0;\n  var unit = getUnit(isRange ? val[1] : val) || 0;\n  var start = params.start || 0 + (isRange ? val1 : 0);\n  var values = [];\n  var maxValue = 0;\n  return function (el, i, t) {\n    if (fromFirst) { fromIndex = 0; }\n    if (fromCenter) { fromIndex = (t - 1) / 2; }\n    if (fromLast) { fromIndex = t - 1; }\n    if (!values.length) {\n      for (var index = 0; index < t; index++) {\n        if (!grid) {\n          values.push(Math.abs(fromIndex - index));\n        } else {\n          var fromX = !fromCenter ? fromIndex%grid[0] : (grid[0]-1)/2;\n          var fromY = !fromCenter ? Math.floor(fromIndex/grid[0]) : (grid[1]-1)/2;\n          var toX = index%grid[0];\n          var toY = Math.floor(index/grid[0]);\n          var distanceX = fromX - toX;\n          var distanceY = fromY - toY;\n          var value = Math.sqrt(distanceX * distanceX + distanceY * distanceY);\n          if (axis === 'x') { value = -distanceX; }\n          if (axis === 'y') { value = -distanceY; }\n          values.push(value);\n        }\n        maxValue = Math.max.apply(Math, values);\n      }\n      if (easing) { values = values.map(function (val) { return easing(val / maxValue) * maxValue; }); }\n      if (direction === 'reverse') { values = values.map(function (val) { return axis ? (val < 0) ? val * -1 : -val : Math.abs(maxValue - val); }); }\n    }\n    var spacing = isRange ? (val2 - val1) / maxValue : val1;\n    return start + (spacing * (Math.round(values[i] * 100) / 100)) + unit;\n  }\n}\n\n// Timeline\n\nfunction timeline(params) {\n  if ( params === void 0 ) params = {};\n\n  var tl = anime(params);\n  tl.duration = 0;\n  tl.add = function(instanceParams, timelineOffset) {\n    var tlIndex = activeInstances.indexOf(tl);\n    var children = tl.children;\n    if (tlIndex > -1) { activeInstances.splice(tlIndex, 1); }\n    function passThrough(ins) { ins.passThrough = true; }\n    for (var i = 0; i < children.length; i++) { passThrough(children[i]); }\n    var insParams = mergeObjects(instanceParams, replaceObjectProps(defaultTweenSettings, params));\n    insParams.targets = insParams.targets || params.targets;\n    var tlDuration = tl.duration;\n    insParams.autoplay = false;\n    insParams.direction = tl.direction;\n    insParams.timelineOffset = is.und(timelineOffset) ? tlDuration : getRelativeValue(timelineOffset, tlDuration);\n    passThrough(tl);\n    tl.seek(insParams.timelineOffset);\n    var ins = anime(insParams);\n    passThrough(ins);\n    children.push(ins);\n    var timings = getInstanceTimings(children, params);\n    tl.delay = timings.delay;\n    tl.endDelay = timings.endDelay;\n    tl.duration = timings.duration;\n    tl.seek(0);\n    tl.reset();\n    if (tl.autoplay) { tl.play(); }\n    return tl;\n  };\n  return tl;\n}\n\nanime.version = '3.2.1';\nanime.speed = 1;\n// TODO:#review: naming, documentation\nanime.suspendWhenDocumentHidden = true;\nanime.running = activeInstances;\nanime.remove = removeTargetsFromActiveInstances;\nanime.get = getOriginalTargetValue;\nanime.set = setTargetsValue;\nanime.convertPx = convertPxToUnit;\nanime.path = getPath;\nanime.setDashoffset = setDashoffset;\nanime.stagger = stagger;\nanime.timeline = timeline;\nanime.easing = parseEasings;\nanime.penner = penner;\nanime.random = function (min, max) { return Math.floor(Math.random() * (max - min + 1)) + min; };\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (anime);\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/animejs@3.2.2/node_modules/animejs/lib/anime.es.js?");

/***/ }),

/***/ "./node_modules/.pnpm/mmenu-light@3.2.2/node_modules/mmenu-light/esm/core/index.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/mmenu-light@3.2.2/node_modules/mmenu-light/esm/core/index.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _modules_match_media_toggler_index__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../modules/match-media-toggler/index */ \"./node_modules/.pnpm/mmenu-light@3.2.2/node_modules/mmenu-light/esm/modules/match-media-toggler/index.js\");\n/* harmony import */ var _modules_sliding_panels_navigation_index__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../modules/sliding-panels-navigation/index */ \"./node_modules/.pnpm/mmenu-light@3.2.2/node_modules/mmenu-light/esm/modules/sliding-panels-navigation/index.js\");\n/* harmony import */ var _modules_offcanvas_drawer_index__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../modules/offcanvas-drawer/index */ \"./node_modules/.pnpm/mmenu-light@3.2.2/node_modules/mmenu-light/esm/modules/offcanvas-drawer/index.js\");\n\n\n\n/**\n * Class for a lightweight mobile menu.\n */\nvar MmenuLight = /** @class */ (function () {\n    /**\n     * Create a lightweight mobile menu.\n     *\n     * @param {HTMLElement} menu                HTML element for the menu.\n     * @param {string}      [mediaQuery='all']  Media queury to match for the menu.\n     */\n    function MmenuLight(menu, mediaQuery) {\n        if (mediaQuery === void 0) { mediaQuery = 'all'; }\n        //  Store the menu node.\n        this.menu = menu;\n        //  Create the toggler instance.\n        this.toggler = new _modules_match_media_toggler_index__WEBPACK_IMPORTED_MODULE_0__[\"default\"](mediaQuery);\n    }\n    /**\n     * Add navigation for the menu.\n     *\n     * @param {object} options Options for the navigation.\n     */\n    MmenuLight.prototype.navigation = function (options) {\n        var _this = this;\n        //  Only needs to be done ones.\n        if (!this.navigator) {\n            options = options || {};\n            var _a = options.title, title = _a === void 0 ? 'Menu' : _a, _b = options.selectedClass, selectedClass = _b === void 0 ? 'Selected' : _b, _c = options.slidingSubmenus, slidingSubmenus = _c === void 0 ? true : _c, _d = options.theme, theme = _d === void 0 ? 'light' : _d;\n            this.navigator = new _modules_sliding_panels_navigation_index__WEBPACK_IMPORTED_MODULE_1__[\"default\"](this.menu, title, selectedClass, slidingSubmenus, theme);\n            //  En-/disable\n            this.toggler.add(function () { return _this.menu.classList.add(_this.navigator.prefix); }, function () { return _this.menu.classList.remove(_this.navigator.prefix); });\n        }\n        return this.navigator;\n    };\n    /**\n     * Add off-canvas behavior to the menu.\n     *\n     * @param {object} options Options for the off-canvas drawer.\n     */\n    MmenuLight.prototype.offcanvas = function (options) {\n        var _this = this;\n        //  Only needs to be done ones.\n        if (!this.drawer) {\n            options = options || {};\n            var _a = options.position, position = _a === void 0 ? 'left' : _a;\n            this.drawer = new _modules_offcanvas_drawer_index__WEBPACK_IMPORTED_MODULE_2__[\"default\"](null, position);\n            /** Original location in the DOM for the menu. */\n            var orgLocation_1 = document.createComment('original menu location');\n            this.menu.after(orgLocation_1);\n            //  En-/disable\n            this.toggler.add(function () {\n                // Move the menu to the drawer.\n                _this.drawer.content.append(_this.menu);\n            }, function () {\n                // Close the drawer.\n                _this.drawer.close();\n                // Move the menu to the original position.\n                orgLocation_1.after(_this.menu);\n            });\n        }\n        return this.drawer;\n    };\n    return MmenuLight;\n}());\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MmenuLight);\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/mmenu-light@3.2.2/node_modules/mmenu-light/esm/core/index.js?");

/***/ }),

/***/ "./node_modules/.pnpm/mmenu-light@3.2.2/node_modules/mmenu-light/esm/modules/helpers.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/.pnpm/mmenu-light@3.2.2/node_modules/mmenu-light/esm/modules/helpers.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $: () => (/* binding */ $),\n/* harmony export */   r: () => (/* binding */ r)\n/* harmony export */ });\n/**\n * Convert a list to an array.\n *\n * @param \t{NodeList|HTMLCollection} list \tThe list or collection to convert into an array.\n * @return\t{array}\t\t\t\t\t\t\tThe array.\n */\nvar r = function (list) {\n    return Array.prototype.slice.call(list);\n};\n/**\n * Find elements in the given context.\n *\n * @param \t{string}\t\tselector\t\t\tThe query selector to search for.\n * @param \t{HTMLElement}\t[context=document]\tThe context to search in.\n * @return\t{HTMLElement[]}\t\t\t\t\t\tThe found list of elements.\n */\nvar $ = function (selector, context) {\n    return r((context || document).querySelectorAll(selector));\n};\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/mmenu-light@3.2.2/node_modules/mmenu-light/esm/modules/helpers.js?");

/***/ }),

/***/ "./node_modules/.pnpm/mmenu-light@3.2.2/node_modules/mmenu-light/esm/modules/match-media-toggler/index.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/mmenu-light@3.2.2/node_modules/mmenu-light/esm/modules/match-media-toggler/index.js ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/**\n * Class for a match media toggler.\n */\nvar MmToggler = /** @class */ (function () {\n    /**\n     * Create the match media.\n     *\n     * @param {string} mediaquery Media query to use.\n     */\n    function MmToggler(mediaquery) {\n        var _this = this;\n        this.listener = function (evnt) {\n            (evnt.matches ? _this.matchFns : _this.unmatchFns).forEach(function (listener) {\n                listener();\n            });\n        };\n        this.toggler = window.matchMedia(mediaquery);\n        this.toggler.addListener(this.listener);\n        this.matchFns = [];\n        this.unmatchFns = [];\n    }\n    /**\n     * Add a function to the list,\n     * also fires the added function.\n     *\n     * @param {Function} match      Function to fire when the media query matches.\n     * @param {Function} unmatch    Function to fire when the media query does not match.\n     */\n    MmToggler.prototype.add = function (match, unmatch) {\n        this.matchFns.push(match);\n        this.unmatchFns.push(unmatch);\n        (this.toggler.matches ? match : unmatch)();\n    };\n    return MmToggler;\n}());\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MmToggler);\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/mmenu-light@3.2.2/node_modules/mmenu-light/esm/modules/match-media-toggler/index.js?");

/***/ }),

/***/ "./node_modules/.pnpm/mmenu-light@3.2.2/node_modules/mmenu-light/esm/modules/offcanvas-drawer/index.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/mmenu-light@3.2.2/node_modules/mmenu-light/esm/modules/offcanvas-drawer/index.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nvar prefix = 'mm-ocd';\n/**\n * Class for off-canvas behavior.\n */\nvar MmOffCanvasDrawer = /** @class */ (function () {\n    /**\n     * Class for off-canvas drawer.\n     *\n     * @param {HTMLElement} [node]          The element to put in the drawer.\n     * @param {String}      [position=left] The position of the drawer, can be \"left\" or \"right\".\n     */\n    function MmOffCanvasDrawer(node, position) {\n        var _this = this;\n        if (node === void 0) { node = null; }\n        //  Create the wrapper.\n        this.wrapper = document.createElement('div');\n        this.wrapper.classList.add(\"\" + prefix);\n        this.wrapper.classList.add(prefix + \"--\" + position);\n        //  Create the drawer.\n        this.content = document.createElement('div');\n        this.content.classList.add(prefix + \"__content\");\n        this.wrapper.append(this.content);\n        //  Create the backdrop.\n        this.backdrop = document.createElement('div');\n        this.backdrop.classList.add(prefix + \"__backdrop\");\n        this.wrapper.append(this.backdrop);\n        //  Add the nodes to the <body>.\n        document.body.append(this.wrapper);\n        if (node) {\n            this.content.append(node);\n        }\n        //  Click the backdrop.\n        var close = function (evnt) {\n            _this.close();\n            evnt.stopImmediatePropagation();\n        };\n        this.backdrop.addEventListener('touchstart', close, { passive: true });\n        this.backdrop.addEventListener('mousedown', close, { passive: true });\n    }\n    Object.defineProperty(MmOffCanvasDrawer.prototype, \"prefix\", {\n        /** Prefix for the class. */\n        get: function () {\n            return prefix;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * Open the drawer.\n     */\n    MmOffCanvasDrawer.prototype.open = function () {\n        this.wrapper.classList.add(prefix + \"--open\");\n        document.body.classList.add(prefix + \"-opened\");\n    };\n    /**\n     * Close the drawer.\n     */\n    MmOffCanvasDrawer.prototype.close = function () {\n        this.wrapper.classList.remove(prefix + \"--open\");\n        document.body.classList.remove(prefix + \"-opened\");\n    };\n    return MmOffCanvasDrawer;\n}());\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MmOffCanvasDrawer);\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/mmenu-light@3.2.2/node_modules/mmenu-light/esm/modules/offcanvas-drawer/index.js?");

/***/ }),

/***/ "./node_modules/.pnpm/mmenu-light@3.2.2/node_modules/mmenu-light/esm/modules/sliding-panels-navigation/index.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/mmenu-light@3.2.2/node_modules/mmenu-light/esm/modules/sliding-panels-navigation/index.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _helpers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../helpers */ \"./node_modules/.pnpm/mmenu-light@3.2.2/node_modules/mmenu-light/esm/modules/helpers.js\");\n\nvar prefix = 'mm-spn';\n/**\n * Class for navigating in a mobile menu.\n */\nvar MmSlidingPanelsNavigation = /** @class */ (function () {\n    /**\n     * Class for navigating in a mobile menu.\n     *\n     * @param {HTMLElement} node            HTMLElement for the menu.\n     * @param {string}      title           The title for the menu.\n     * @param {string}      selectedClass   The class for selected listitems.\n     * @param {boolean}     slidingSubmenus Whether or not to use sliding submenus.\n     * @param {string}      theme           The color scheme for the menu.\n     */\n    function MmSlidingPanelsNavigation(node, title, selectedClass, slidingSubmenus, theme) {\n        this.node = node;\n        this.title = title;\n        this.slidingSubmenus = slidingSubmenus;\n        this.selectedClass = selectedClass;\n        //  Add classname.\n        this.node.classList.add(prefix);\n        this.node.classList.add(prefix + \"--\" + theme);\n        this.node.classList.add(prefix + \"--\" + (this.slidingSubmenus ? 'navbar' : 'vertical'));\n        this._setSelectedl();\n        this._initAnchors();\n    }\n    Object.defineProperty(MmSlidingPanelsNavigation.prototype, \"prefix\", {\n        /** Prefix for the class. */\n        get: function () {\n            return prefix;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * Open the given panel.\n     *\n     * @param {HTMLElement} panel Panel to open.\n     */\n    MmSlidingPanelsNavigation.prototype.openPanel = function (panel) {\n        /** Parent LI for the panel.  */\n        var listitem = panel.parentElement;\n        //  Sliding submenus\n        if (this.slidingSubmenus) {\n            /** Title above the panel to open. */\n            var title_1 = panel.dataset.mmSpnTitle;\n            //  Opening the main level UL.\n            if (listitem === this.node) {\n                this.node.classList.add(prefix + \"--main\");\n            }\n            //  Opening a sub level UL.\n            else {\n                this.node.classList.remove(prefix + \"--main\");\n                //  Find title from parent LI.\n                if (!title_1) {\n                    (0,_helpers__WEBPACK_IMPORTED_MODULE_0__.r)(listitem.children).forEach(function (child) {\n                        if (child.matches('a, span')) {\n                            title_1 = child.textContent;\n                        }\n                    });\n                }\n            }\n            //  Use the default title.\n            if (!title_1) {\n                title_1 = this.title;\n            }\n            //  Set the title.\n            this.node.dataset.mmSpnTitle = title_1;\n            //  Unset all panels from being opened and parent.\n            (0,_helpers__WEBPACK_IMPORTED_MODULE_0__.$)(\".\" + prefix + \"--open\", this.node).forEach(function (open) {\n                open.classList.remove(prefix + \"--open\");\n                open.classList.remove(prefix + \"--parent\");\n            });\n            //  Set the current panel as being opened.\n            panel.classList.add(prefix + \"--open\");\n            panel.classList.remove(prefix + \"--parent\");\n            //  Set all parent panels as being parent.\n            var parent_1 = panel.parentElement.closest('ul');\n            while (parent_1) {\n                parent_1.classList.add(prefix + \"--open\");\n                parent_1.classList.add(prefix + \"--parent\");\n                parent_1 = parent_1.parentElement.closest('ul');\n            }\n        }\n        //  Vertical submenus\n        else {\n            /** Whether or not the panel is currently opened. */\n            var isOpened = panel.matches(\".\" + prefix + \"--open\");\n            //  Unset all panels from being opened and parent.\n            (0,_helpers__WEBPACK_IMPORTED_MODULE_0__.$)(\".\" + prefix + \"--open\", this.node).forEach(function (open) {\n                open.classList.remove(prefix + \"--open\");\n            });\n            //  Toggle the current panel.\n            panel.classList[isOpened ? 'remove' : 'add'](prefix + \"--open\");\n            //  Set all parent panels as being opened.\n            var parent_2 = panel.parentElement.closest('ul');\n            while (parent_2) {\n                parent_2.classList.add(prefix + \"--open\");\n                parent_2 = parent_2.parentElement.closest('ul');\n            }\n        }\n    };\n    /**\n     * Initiate the selected listitem / open the current panel.\n     */\n    MmSlidingPanelsNavigation.prototype._setSelectedl = function () {\n        /** All selected LIs. */\n        var listitems = (0,_helpers__WEBPACK_IMPORTED_MODULE_0__.$)('.' + this.selectedClass, this.node);\n        /** The last selected LI. */\n        var listitem = listitems[listitems.length - 1];\n        /** The opened UL. */\n        var panel = null;\n        if (listitem) {\n            panel = listitem.closest('ul');\n        }\n        if (!panel) {\n            panel = this.node.querySelector('ul');\n        }\n        this.openPanel(panel);\n    };\n    /**\n     * Initialize the click event handlers.\n     */\n    MmSlidingPanelsNavigation.prototype._initAnchors = function () {\n        var _this = this;\n        /**\n         * Clicking an A in the menu: prevent bubbling up to the LI.\n         *\n         * @param   {HTMLElement}    target The clicked element.\n         * @return  {boolean}       handled Whether or not the event was handled.\n         */\n        var clickAnchor = function (target) {\n            if (target.matches('a')) {\n                return true;\n            }\n            return false;\n        };\n        /**\n         * Click a LI or SPAN in the menu: open its submenu (if present).\n         *\n         * @param   {HTMLElement}    target The clicked element.\n         * @return  {boolean}               Whether or not the event was handled.\n         */\n        var openSubmenu = function (target) {\n            /** Parent listitem for the submenu.  */\n            var listitem;\n            //  Find the parent listitem.\n            if (target.closest('span')) {\n                listitem = target.parentElement;\n            }\n            else if (target.closest('li')) {\n                listitem = target;\n            }\n            else {\n                listitem = false;\n            }\n            if (listitem) {\n                (0,_helpers__WEBPACK_IMPORTED_MODULE_0__.r)(listitem.children).forEach(function (panel) {\n                    if (panel.matches('ul')) {\n                        _this.openPanel(panel);\n                    }\n                });\n                return true;\n            }\n            return false;\n        };\n        /**\n         * Click the menu (the navbar): close the last opened submenu.\n         *\n         * @param   {HTMLElement}    target The clicked element.\n         * @return  {boolean}               Whether or not the event was handled.\n         */\n        var closeSubmenu = function (target) {\n            /** The opened ULs. */\n            var panels = (0,_helpers__WEBPACK_IMPORTED_MODULE_0__.$)(\".\" + prefix + \"--open\", target);\n            /** The last opened UL. */\n            var panel = panels[panels.length - 1];\n            if (panel) {\n                /** The second to last opened UL. */\n                var parent_3 = panel.parentElement.closest('ul');\n                if (parent_3) {\n                    _this.openPanel(parent_3);\n                    return true;\n                }\n            }\n            return false;\n        };\n        this.node.addEventListener('click', function (evnt) {\n            var target = evnt.target;\n            var handled = false;\n            handled = handled || clickAnchor(target);\n            handled = handled || openSubmenu(target);\n            handled = handled || closeSubmenu(target);\n            if (handled) {\n                evnt.stopImmediatePropagation();\n            }\n        });\n    };\n    return MmSlidingPanelsNavigation;\n}());\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MmSlidingPanelsNavigation);\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/mmenu-light@3.2.2/node_modules/mmenu-light/esm/modules/sliding-panels-navigation/index.js?");

/***/ }),

/***/ "./node_modules/.pnpm/mmenu-light@3.2.2/node_modules/mmenu-light/src/mmenu-light.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/.pnpm/mmenu-light@3.2.2/node_modules/mmenu-light/src/mmenu-light.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _esm_core_index__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../esm/core/index */ \"./node_modules/.pnpm/mmenu-light@3.2.2/node_modules/mmenu-light/esm/core/index.js\");\n/*!\n * Mmenu Light\n * mmenujs.com/mmenu-light\n *\n * Copyright (c) Fred Heusschen\n * www.frebsite.nl\n *\n * License: CC-BY-4.0\n * http://creativecommons.org/licenses/by/4.0/\n */\n\n//\tThe module\n\n\n//  Export module\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_esm_core_index__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n\n//\tGlobal namespace\nwindow.MmenuLight = _esm_core_index__WEBPACK_IMPORTED_MODULE_0__[\"default\"];\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/mmenu-light@3.2.2/node_modules/mmenu-light/src/mmenu-light.js?");

/***/ }),

/***/ "./node_modules/.pnpm/sweetalert2@11.17.2/node_modules/sweetalert2/dist/sweetalert2.all.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/.pnpm/sweetalert2@11.17.2/node_modules/sweetalert2/dist/sweetalert2.all.js ***!
  \*************************************************************************************************/
/***/ (function(module) {

eval("/*!\n* sweetalert2 v11.17.2\n* Released under the MIT License.\n*/\n(function (global, factory) {\n   true ? module.exports = factory() :\n  0;\n})(this, (function () { 'use strict';\n\n  function _assertClassBrand(e, t, n) {\n    if (\"function\" == typeof e ? e === t : e.has(t)) return arguments.length < 3 ? t : n;\n    throw new TypeError(\"Private element is not present on this object\");\n  }\n  function _checkPrivateRedeclaration(e, t) {\n    if (t.has(e)) throw new TypeError(\"Cannot initialize the same private elements twice on an object\");\n  }\n  function _classPrivateFieldGet2(s, a) {\n    return s.get(_assertClassBrand(s, a));\n  }\n  function _classPrivateFieldInitSpec(e, t, a) {\n    _checkPrivateRedeclaration(e, t), t.set(e, a);\n  }\n  function _classPrivateFieldSet2(s, a, r) {\n    return s.set(_assertClassBrand(s, a), r), r;\n  }\n\n  const RESTORE_FOCUS_TIMEOUT = 100;\n\n  /** @type {GlobalState} */\n  const globalState = {};\n  const focusPreviousActiveElement = () => {\n    if (globalState.previousActiveElement instanceof HTMLElement) {\n      globalState.previousActiveElement.focus();\n      globalState.previousActiveElement = null;\n    } else if (document.body) {\n      document.body.focus();\n    }\n  };\n\n  /**\n   * Restore previous active (focused) element\n   *\n   * @param {boolean} returnFocus\n   * @returns {Promise<void>}\n   */\n  const restoreActiveElement = returnFocus => {\n    return new Promise(resolve => {\n      if (!returnFocus) {\n        return resolve();\n      }\n      const x = window.scrollX;\n      const y = window.scrollY;\n      globalState.restoreFocusTimeout = setTimeout(() => {\n        focusPreviousActiveElement();\n        resolve();\n      }, RESTORE_FOCUS_TIMEOUT); // issues/900\n\n      window.scrollTo(x, y);\n    });\n  };\n\n  const swalPrefix = 'swal2-';\n\n  /**\n   * @typedef {Record<SwalClass, string>} SwalClasses\n   */\n\n  /**\n   * @typedef {'success' | 'warning' | 'info' | 'question' | 'error'} SwalIcon\n   * @typedef {Record<SwalIcon, string>} SwalIcons\n   */\n\n  /** @type {SwalClass[]} */\n  const classNames = ['container', 'shown', 'height-auto', 'iosfix', 'popup', 'modal', 'no-backdrop', 'no-transition', 'toast', 'toast-shown', 'show', 'hide', 'close', 'title', 'html-container', 'actions', 'confirm', 'deny', 'cancel', 'default-outline', 'footer', 'icon', 'icon-content', 'image', 'input', 'file', 'range', 'select', 'radio', 'checkbox', 'label', 'textarea', 'inputerror', 'input-label', 'validation-message', 'progress-steps', 'active-progress-step', 'progress-step', 'progress-step-line', 'loader', 'loading', 'styled', 'top', 'top-start', 'top-end', 'top-left', 'top-right', 'center', 'center-start', 'center-end', 'center-left', 'center-right', 'bottom', 'bottom-start', 'bottom-end', 'bottom-left', 'bottom-right', 'grow-row', 'grow-column', 'grow-fullscreen', 'rtl', 'timer-progress-bar', 'timer-progress-bar-container', 'scrollbar-measure', 'icon-success', 'icon-warning', 'icon-info', 'icon-question', 'icon-error', 'draggable', 'dragging'];\n  const swalClasses = classNames.reduce((acc, className) => {\n    acc[className] = swalPrefix + className;\n    return acc;\n  }, /** @type {SwalClasses} */{});\n\n  /** @type {SwalIcon[]} */\n  const icons = ['success', 'warning', 'info', 'question', 'error'];\n  const iconTypes = icons.reduce((acc, icon) => {\n    acc[icon] = swalPrefix + icon;\n    return acc;\n  }, /** @type {SwalIcons} */{});\n\n  const consolePrefix = 'SweetAlert2:';\n\n  /**\n   * Capitalize the first letter of a string\n   *\n   * @param {string} str\n   * @returns {string}\n   */\n  const capitalizeFirstLetter = str => str.charAt(0).toUpperCase() + str.slice(1);\n\n  /**\n   * Standardize console warnings\n   *\n   * @param {string | string[]} message\n   */\n  const warn = message => {\n    console.warn(`${consolePrefix} ${typeof message === 'object' ? message.join(' ') : message}`);\n  };\n\n  /**\n   * Standardize console errors\n   *\n   * @param {string} message\n   */\n  const error = message => {\n    console.error(`${consolePrefix} ${message}`);\n  };\n\n  /**\n   * Private global state for `warnOnce`\n   *\n   * @type {string[]}\n   * @private\n   */\n  const previousWarnOnceMessages = [];\n\n  /**\n   * Show a console warning, but only if it hasn't already been shown\n   *\n   * @param {string} message\n   */\n  const warnOnce = message => {\n    if (!previousWarnOnceMessages.includes(message)) {\n      previousWarnOnceMessages.push(message);\n      warn(message);\n    }\n  };\n\n  /**\n   * Show a one-time console warning about deprecated params/methods\n   *\n   * @param {string} deprecatedParam\n   * @param {string?} useInstead\n   */\n  const warnAboutDeprecation = function (deprecatedParam) {\n    let useInstead = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n    warnOnce(`\"${deprecatedParam}\" is deprecated and will be removed in the next major release.${useInstead ? ` Use \"${useInstead}\" instead.` : ''}`);\n  };\n\n  /**\n   * If `arg` is a function, call it (with no arguments or context) and return the result.\n   * Otherwise, just pass the value through\n   *\n   * @param {Function | any} arg\n   * @returns {any}\n   */\n  const callIfFunction = arg => typeof arg === 'function' ? arg() : arg;\n\n  /**\n   * @param {any} arg\n   * @returns {boolean}\n   */\n  const hasToPromiseFn = arg => arg && typeof arg.toPromise === 'function';\n\n  /**\n   * @param {any} arg\n   * @returns {Promise<any>}\n   */\n  const asPromise = arg => hasToPromiseFn(arg) ? arg.toPromise() : Promise.resolve(arg);\n\n  /**\n   * @param {any} arg\n   * @returns {boolean}\n   */\n  const isPromise = arg => arg && Promise.resolve(arg) === arg;\n\n  /**\n   * Gets the popup container which contains the backdrop and the popup itself.\n   *\n   * @returns {HTMLElement | null}\n   */\n  const getContainer = () => document.body.querySelector(`.${swalClasses.container}`);\n\n  /**\n   * @param {string} selectorString\n   * @returns {HTMLElement | null}\n   */\n  const elementBySelector = selectorString => {\n    const container = getContainer();\n    return container ? container.querySelector(selectorString) : null;\n  };\n\n  /**\n   * @param {string} className\n   * @returns {HTMLElement | null}\n   */\n  const elementByClass = className => {\n    return elementBySelector(`.${className}`);\n  };\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getPopup = () => elementByClass(swalClasses.popup);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getIcon = () => elementByClass(swalClasses.icon);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getIconContent = () => elementByClass(swalClasses['icon-content']);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getTitle = () => elementByClass(swalClasses.title);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getHtmlContainer = () => elementByClass(swalClasses['html-container']);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getImage = () => elementByClass(swalClasses.image);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getProgressSteps = () => elementByClass(swalClasses['progress-steps']);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getValidationMessage = () => elementByClass(swalClasses['validation-message']);\n\n  /**\n   * @returns {HTMLButtonElement | null}\n   */\n  const getConfirmButton = () => (/** @type {HTMLButtonElement} */elementBySelector(`.${swalClasses.actions} .${swalClasses.confirm}`));\n\n  /**\n   * @returns {HTMLButtonElement | null}\n   */\n  const getCancelButton = () => (/** @type {HTMLButtonElement} */elementBySelector(`.${swalClasses.actions} .${swalClasses.cancel}`));\n\n  /**\n   * @returns {HTMLButtonElement | null}\n   */\n  const getDenyButton = () => (/** @type {HTMLButtonElement} */elementBySelector(`.${swalClasses.actions} .${swalClasses.deny}`));\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getInputLabel = () => elementByClass(swalClasses['input-label']);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getLoader = () => elementBySelector(`.${swalClasses.loader}`);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getActions = () => elementByClass(swalClasses.actions);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getFooter = () => elementByClass(swalClasses.footer);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getTimerProgressBar = () => elementByClass(swalClasses['timer-progress-bar']);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getCloseButton = () => elementByClass(swalClasses.close);\n\n  // https://github.com/jkup/focusable/blob/master/index.js\n  const focusable = `\n  a[href],\n  area[href],\n  input:not([disabled]),\n  select:not([disabled]),\n  textarea:not([disabled]),\n  button:not([disabled]),\n  iframe,\n  object,\n  embed,\n  [tabindex=\"0\"],\n  [contenteditable],\n  audio[controls],\n  video[controls],\n  summary\n`;\n  /**\n   * @returns {HTMLElement[]}\n   */\n  const getFocusableElements = () => {\n    const popup = getPopup();\n    if (!popup) {\n      return [];\n    }\n    /** @type {NodeListOf<HTMLElement>} */\n    const focusableElementsWithTabindex = popup.querySelectorAll('[tabindex]:not([tabindex=\"-1\"]):not([tabindex=\"0\"])');\n    const focusableElementsWithTabindexSorted = Array.from(focusableElementsWithTabindex)\n    // sort according to tabindex\n    .sort((a, b) => {\n      const tabindexA = parseInt(a.getAttribute('tabindex') || '0');\n      const tabindexB = parseInt(b.getAttribute('tabindex') || '0');\n      if (tabindexA > tabindexB) {\n        return 1;\n      } else if (tabindexA < tabindexB) {\n        return -1;\n      }\n      return 0;\n    });\n\n    /** @type {NodeListOf<HTMLElement>} */\n    const otherFocusableElements = popup.querySelectorAll(focusable);\n    const otherFocusableElementsFiltered = Array.from(otherFocusableElements).filter(el => el.getAttribute('tabindex') !== '-1');\n    return [...new Set(focusableElementsWithTabindexSorted.concat(otherFocusableElementsFiltered))].filter(el => isVisible$1(el));\n  };\n\n  /**\n   * @returns {boolean}\n   */\n  const isModal = () => {\n    return hasClass(document.body, swalClasses.shown) && !hasClass(document.body, swalClasses['toast-shown']) && !hasClass(document.body, swalClasses['no-backdrop']);\n  };\n\n  /**\n   * @returns {boolean}\n   */\n  const isToast = () => {\n    const popup = getPopup();\n    if (!popup) {\n      return false;\n    }\n    return hasClass(popup, swalClasses.toast);\n  };\n\n  /**\n   * @returns {boolean}\n   */\n  const isLoading = () => {\n    const popup = getPopup();\n    if (!popup) {\n      return false;\n    }\n    return popup.hasAttribute('data-loading');\n  };\n\n  /**\n   * Securely set innerHTML of an element\n   * https://github.com/sweetalert2/sweetalert2/issues/1926\n   *\n   * @param {HTMLElement} elem\n   * @param {string} html\n   */\n  const setInnerHtml = (elem, html) => {\n    elem.textContent = '';\n    if (html) {\n      const parser = new DOMParser();\n      const parsed = parser.parseFromString(html, `text/html`);\n      const head = parsed.querySelector('head');\n      if (head) {\n        Array.from(head.childNodes).forEach(child => {\n          elem.appendChild(child);\n        });\n      }\n      const body = parsed.querySelector('body');\n      if (body) {\n        Array.from(body.childNodes).forEach(child => {\n          if (child instanceof HTMLVideoElement || child instanceof HTMLAudioElement) {\n            elem.appendChild(child.cloneNode(true)); // https://github.com/sweetalert2/sweetalert2/issues/2507\n          } else {\n            elem.appendChild(child);\n          }\n        });\n      }\n    }\n  };\n\n  /**\n   * @param {HTMLElement} elem\n   * @param {string} className\n   * @returns {boolean}\n   */\n  const hasClass = (elem, className) => {\n    if (!className) {\n      return false;\n    }\n    const classList = className.split(/\\s+/);\n    for (let i = 0; i < classList.length; i++) {\n      if (!elem.classList.contains(classList[i])) {\n        return false;\n      }\n    }\n    return true;\n  };\n\n  /**\n   * @param {HTMLElement} elem\n   * @param {SweetAlertOptions} params\n   */\n  const removeCustomClasses = (elem, params) => {\n    Array.from(elem.classList).forEach(className => {\n      if (!Object.values(swalClasses).includes(className) && !Object.values(iconTypes).includes(className) && !Object.values(params.showClass || {}).includes(className)) {\n        elem.classList.remove(className);\n      }\n    });\n  };\n\n  /**\n   * @param {HTMLElement} elem\n   * @param {SweetAlertOptions} params\n   * @param {string} className\n   */\n  const applyCustomClass = (elem, params, className) => {\n    removeCustomClasses(elem, params);\n    if (!params.customClass) {\n      return;\n    }\n    const customClass = params.customClass[(/** @type {keyof SweetAlertCustomClass} */className)];\n    if (!customClass) {\n      return;\n    }\n    if (typeof customClass !== 'string' && !customClass.forEach) {\n      warn(`Invalid type of customClass.${className}! Expected string or iterable object, got \"${typeof customClass}\"`);\n      return;\n    }\n    addClass(elem, customClass);\n  };\n\n  /**\n   * @param {HTMLElement} popup\n   * @param {import('./renderers/renderInput').InputClass | SweetAlertInput} inputClass\n   * @returns {HTMLInputElement | null}\n   */\n  const getInput$1 = (popup, inputClass) => {\n    if (!inputClass) {\n      return null;\n    }\n    switch (inputClass) {\n      case 'select':\n      case 'textarea':\n      case 'file':\n        return popup.querySelector(`.${swalClasses.popup} > .${swalClasses[inputClass]}`);\n      case 'checkbox':\n        return popup.querySelector(`.${swalClasses.popup} > .${swalClasses.checkbox} input`);\n      case 'radio':\n        return popup.querySelector(`.${swalClasses.popup} > .${swalClasses.radio} input:checked`) || popup.querySelector(`.${swalClasses.popup} > .${swalClasses.radio} input:first-child`);\n      case 'range':\n        return popup.querySelector(`.${swalClasses.popup} > .${swalClasses.range} input`);\n      default:\n        return popup.querySelector(`.${swalClasses.popup} > .${swalClasses.input}`);\n    }\n  };\n\n  /**\n   * @param {HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement} input\n   */\n  const focusInput = input => {\n    input.focus();\n\n    // place cursor at end of text in text input\n    if (input.type !== 'file') {\n      // http://stackoverflow.com/a/2345915\n      const val = input.value;\n      input.value = '';\n      input.value = val;\n    }\n  };\n\n  /**\n   * @param {HTMLElement | HTMLElement[] | null} target\n   * @param {string | string[] | readonly string[] | undefined} classList\n   * @param {boolean} condition\n   */\n  const toggleClass = (target, classList, condition) => {\n    if (!target || !classList) {\n      return;\n    }\n    if (typeof classList === 'string') {\n      classList = classList.split(/\\s+/).filter(Boolean);\n    }\n    classList.forEach(className => {\n      if (Array.isArray(target)) {\n        target.forEach(elem => {\n          if (condition) {\n            elem.classList.add(className);\n          } else {\n            elem.classList.remove(className);\n          }\n        });\n      } else {\n        if (condition) {\n          target.classList.add(className);\n        } else {\n          target.classList.remove(className);\n        }\n      }\n    });\n  };\n\n  /**\n   * @param {HTMLElement | HTMLElement[] | null} target\n   * @param {string | string[] | readonly string[] | undefined} classList\n   */\n  const addClass = (target, classList) => {\n    toggleClass(target, classList, true);\n  };\n\n  /**\n   * @param {HTMLElement | HTMLElement[] | null} target\n   * @param {string | string[] | readonly string[] | undefined} classList\n   */\n  const removeClass = (target, classList) => {\n    toggleClass(target, classList, false);\n  };\n\n  /**\n   * Get direct child of an element by class name\n   *\n   * @param {HTMLElement} elem\n   * @param {string} className\n   * @returns {HTMLElement | undefined}\n   */\n  const getDirectChildByClass = (elem, className) => {\n    const children = Array.from(elem.children);\n    for (let i = 0; i < children.length; i++) {\n      const child = children[i];\n      if (child instanceof HTMLElement && hasClass(child, className)) {\n        return child;\n      }\n    }\n  };\n\n  /**\n   * @param {HTMLElement} elem\n   * @param {string} property\n   * @param {*} value\n   */\n  const applyNumericalStyle = (elem, property, value) => {\n    if (value === `${parseInt(value)}`) {\n      value = parseInt(value);\n    }\n    if (value || parseInt(value) === 0) {\n      elem.style.setProperty(property, typeof value === 'number' ? `${value}px` : value);\n    } else {\n      elem.style.removeProperty(property);\n    }\n  };\n\n  /**\n   * @param {HTMLElement | null} elem\n   * @param {string} display\n   */\n  const show = function (elem) {\n    let display = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'flex';\n    if (!elem) {\n      return;\n    }\n    elem.style.display = display;\n  };\n\n  /**\n   * @param {HTMLElement | null} elem\n   */\n  const hide = elem => {\n    if (!elem) {\n      return;\n    }\n    elem.style.display = 'none';\n  };\n\n  /**\n   * @param {HTMLElement | null} elem\n   * @param {string} display\n   */\n  const showWhenInnerHtmlPresent = function (elem) {\n    let display = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'block';\n    if (!elem) {\n      return;\n    }\n    new MutationObserver(() => {\n      toggle(elem, elem.innerHTML, display);\n    }).observe(elem, {\n      childList: true,\n      subtree: true\n    });\n  };\n\n  /**\n   * @param {HTMLElement} parent\n   * @param {string} selector\n   * @param {string} property\n   * @param {string} value\n   */\n  const setStyle = (parent, selector, property, value) => {\n    /** @type {HTMLElement | null} */\n    const el = parent.querySelector(selector);\n    if (el) {\n      el.style.setProperty(property, value);\n    }\n  };\n\n  /**\n   * @param {HTMLElement} elem\n   * @param {any} condition\n   * @param {string} display\n   */\n  const toggle = function (elem, condition) {\n    let display = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'flex';\n    if (condition) {\n      show(elem, display);\n    } else {\n      hide(elem);\n    }\n  };\n\n  /**\n   * borrowed from jquery $(elem).is(':visible') implementation\n   *\n   * @param {HTMLElement | null} elem\n   * @returns {boolean}\n   */\n  const isVisible$1 = elem => !!(elem && (elem.offsetWidth || elem.offsetHeight || elem.getClientRects().length));\n\n  /**\n   * @returns {boolean}\n   */\n  const allButtonsAreHidden = () => !isVisible$1(getConfirmButton()) && !isVisible$1(getDenyButton()) && !isVisible$1(getCancelButton());\n\n  /**\n   * @param {HTMLElement} elem\n   * @returns {boolean}\n   */\n  const isScrollable = elem => !!(elem.scrollHeight > elem.clientHeight);\n\n  /**\n   * borrowed from https://stackoverflow.com/a/46352119\n   *\n   * @param {HTMLElement} elem\n   * @returns {boolean}\n   */\n  const hasCssAnimation = elem => {\n    const style = window.getComputedStyle(elem);\n    const animDuration = parseFloat(style.getPropertyValue('animation-duration') || '0');\n    const transDuration = parseFloat(style.getPropertyValue('transition-duration') || '0');\n    return animDuration > 0 || transDuration > 0;\n  };\n\n  /**\n   * @param {number} timer\n   * @param {boolean} reset\n   */\n  const animateTimerProgressBar = function (timer) {\n    let reset = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    const timerProgressBar = getTimerProgressBar();\n    if (!timerProgressBar) {\n      return;\n    }\n    if (isVisible$1(timerProgressBar)) {\n      if (reset) {\n        timerProgressBar.style.transition = 'none';\n        timerProgressBar.style.width = '100%';\n      }\n      setTimeout(() => {\n        timerProgressBar.style.transition = `width ${timer / 1000}s linear`;\n        timerProgressBar.style.width = '0%';\n      }, 10);\n    }\n  };\n  const stopTimerProgressBar = () => {\n    const timerProgressBar = getTimerProgressBar();\n    if (!timerProgressBar) {\n      return;\n    }\n    const timerProgressBarWidth = parseInt(window.getComputedStyle(timerProgressBar).width);\n    timerProgressBar.style.removeProperty('transition');\n    timerProgressBar.style.width = '100%';\n    const timerProgressBarFullWidth = parseInt(window.getComputedStyle(timerProgressBar).width);\n    const timerProgressBarPercent = timerProgressBarWidth / timerProgressBarFullWidth * 100;\n    timerProgressBar.style.width = `${timerProgressBarPercent}%`;\n  };\n\n  /**\n   * Detect Node env\n   *\n   * @returns {boolean}\n   */\n  const isNodeEnv = () => typeof window === 'undefined' || typeof document === 'undefined';\n\n  const sweetHTML = `\n <div aria-labelledby=\"${swalClasses.title}\" aria-describedby=\"${swalClasses['html-container']}\" class=\"${swalClasses.popup}\" tabindex=\"-1\">\n   <button type=\"button\" class=\"${swalClasses.close}\"></button>\n   <ul class=\"${swalClasses['progress-steps']}\"></ul>\n   <div class=\"${swalClasses.icon}\"></div>\n   <img class=\"${swalClasses.image}\" />\n   <h2 class=\"${swalClasses.title}\" id=\"${swalClasses.title}\"></h2>\n   <div class=\"${swalClasses['html-container']}\" id=\"${swalClasses['html-container']}\"></div>\n   <input class=\"${swalClasses.input}\" id=\"${swalClasses.input}\" />\n   <input type=\"file\" class=\"${swalClasses.file}\" />\n   <div class=\"${swalClasses.range}\">\n     <input type=\"range\" />\n     <output></output>\n   </div>\n   <select class=\"${swalClasses.select}\" id=\"${swalClasses.select}\"></select>\n   <div class=\"${swalClasses.radio}\"></div>\n   <label class=\"${swalClasses.checkbox}\">\n     <input type=\"checkbox\" id=\"${swalClasses.checkbox}\" />\n     <span class=\"${swalClasses.label}\"></span>\n   </label>\n   <textarea class=\"${swalClasses.textarea}\" id=\"${swalClasses.textarea}\"></textarea>\n   <div class=\"${swalClasses['validation-message']}\" id=\"${swalClasses['validation-message']}\"></div>\n   <div class=\"${swalClasses.actions}\">\n     <div class=\"${swalClasses.loader}\"></div>\n     <button type=\"button\" class=\"${swalClasses.confirm}\"></button>\n     <button type=\"button\" class=\"${swalClasses.deny}\"></button>\n     <button type=\"button\" class=\"${swalClasses.cancel}\"></button>\n   </div>\n   <div class=\"${swalClasses.footer}\"></div>\n   <div class=\"${swalClasses['timer-progress-bar-container']}\">\n     <div class=\"${swalClasses['timer-progress-bar']}\"></div>\n   </div>\n </div>\n`.replace(/(^|\\n)\\s*/g, '');\n\n  /**\n   * @returns {boolean}\n   */\n  const resetOldContainer = () => {\n    const oldContainer = getContainer();\n    if (!oldContainer) {\n      return false;\n    }\n    oldContainer.remove();\n    removeClass([document.documentElement, document.body], [swalClasses['no-backdrop'], swalClasses['toast-shown'], swalClasses['has-column']]);\n    return true;\n  };\n  const resetValidationMessage$1 = () => {\n    globalState.currentInstance.resetValidationMessage();\n  };\n  const addInputChangeListeners = () => {\n    const popup = getPopup();\n    const input = getDirectChildByClass(popup, swalClasses.input);\n    const file = getDirectChildByClass(popup, swalClasses.file);\n    /** @type {HTMLInputElement} */\n    const range = popup.querySelector(`.${swalClasses.range} input`);\n    /** @type {HTMLOutputElement} */\n    const rangeOutput = popup.querySelector(`.${swalClasses.range} output`);\n    const select = getDirectChildByClass(popup, swalClasses.select);\n    /** @type {HTMLInputElement} */\n    const checkbox = popup.querySelector(`.${swalClasses.checkbox} input`);\n    const textarea = getDirectChildByClass(popup, swalClasses.textarea);\n    input.oninput = resetValidationMessage$1;\n    file.onchange = resetValidationMessage$1;\n    select.onchange = resetValidationMessage$1;\n    checkbox.onchange = resetValidationMessage$1;\n    textarea.oninput = resetValidationMessage$1;\n    range.oninput = () => {\n      resetValidationMessage$1();\n      rangeOutput.value = range.value;\n    };\n    range.onchange = () => {\n      resetValidationMessage$1();\n      rangeOutput.value = range.value;\n    };\n  };\n\n  /**\n   * @param {string | HTMLElement} target\n   * @returns {HTMLElement}\n   */\n  const getTarget = target => typeof target === 'string' ? document.querySelector(target) : target;\n\n  /**\n   * @param {SweetAlertOptions} params\n   */\n  const setupAccessibility = params => {\n    const popup = getPopup();\n    popup.setAttribute('role', params.toast ? 'alert' : 'dialog');\n    popup.setAttribute('aria-live', params.toast ? 'polite' : 'assertive');\n    if (!params.toast) {\n      popup.setAttribute('aria-modal', 'true');\n    }\n  };\n\n  /**\n   * @param {HTMLElement} targetElement\n   */\n  const setupRTL = targetElement => {\n    if (window.getComputedStyle(targetElement).direction === 'rtl') {\n      addClass(getContainer(), swalClasses.rtl);\n    }\n  };\n\n  /**\n   * Add modal + backdrop + no-war message for Russians to DOM\n   *\n   * @param {SweetAlertOptions} params\n   */\n  const init = params => {\n    // Clean up the old popup container if it exists\n    const oldContainerExisted = resetOldContainer();\n    if (isNodeEnv()) {\n      error('SweetAlert2 requires document to initialize');\n      return;\n    }\n    const container = document.createElement('div');\n    container.className = swalClasses.container;\n    if (oldContainerExisted) {\n      addClass(container, swalClasses['no-transition']);\n    }\n    setInnerHtml(container, sweetHTML);\n    container.dataset['swal2Theme'] = params.theme;\n    const targetElement = getTarget(params.target);\n    targetElement.appendChild(container);\n    setupAccessibility(params);\n    setupRTL(targetElement);\n    addInputChangeListeners();\n  };\n\n  /**\n   * @param {HTMLElement | object | string} param\n   * @param {HTMLElement} target\n   */\n  const parseHtmlToContainer = (param, target) => {\n    // DOM element\n    if (param instanceof HTMLElement) {\n      target.appendChild(param);\n    }\n\n    // Object\n    else if (typeof param === 'object') {\n      handleObject(param, target);\n    }\n\n    // Plain string\n    else if (param) {\n      setInnerHtml(target, param);\n    }\n  };\n\n  /**\n   * @param {any} param\n   * @param {HTMLElement} target\n   */\n  const handleObject = (param, target) => {\n    // JQuery element(s)\n    if (param.jquery) {\n      handleJqueryElem(target, param);\n    }\n\n    // For other objects use their string representation\n    else {\n      setInnerHtml(target, param.toString());\n    }\n  };\n\n  /**\n   * @param {HTMLElement} target\n   * @param {any} elem\n   */\n  const handleJqueryElem = (target, elem) => {\n    target.textContent = '';\n    if (0 in elem) {\n      for (let i = 0; i in elem; i++) {\n        target.appendChild(elem[i].cloneNode(true));\n      }\n    } else {\n      target.appendChild(elem.cloneNode(true));\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderActions = (instance, params) => {\n    const actions = getActions();\n    const loader = getLoader();\n    if (!actions || !loader) {\n      return;\n    }\n\n    // Actions (buttons) wrapper\n    if (!params.showConfirmButton && !params.showDenyButton && !params.showCancelButton) {\n      hide(actions);\n    } else {\n      show(actions);\n    }\n\n    // Custom class\n    applyCustomClass(actions, params, 'actions');\n\n    // Render all the buttons\n    renderButtons(actions, loader, params);\n\n    // Loader\n    setInnerHtml(loader, params.loaderHtml || '');\n    applyCustomClass(loader, params, 'loader');\n  };\n\n  /**\n   * @param {HTMLElement} actions\n   * @param {HTMLElement} loader\n   * @param {SweetAlertOptions} params\n   */\n  function renderButtons(actions, loader, params) {\n    const confirmButton = getConfirmButton();\n    const denyButton = getDenyButton();\n    const cancelButton = getCancelButton();\n    if (!confirmButton || !denyButton || !cancelButton) {\n      return;\n    }\n\n    // Render buttons\n    renderButton(confirmButton, 'confirm', params);\n    renderButton(denyButton, 'deny', params);\n    renderButton(cancelButton, 'cancel', params);\n    handleButtonsStyling(confirmButton, denyButton, cancelButton, params);\n    if (params.reverseButtons) {\n      if (params.toast) {\n        actions.insertBefore(cancelButton, confirmButton);\n        actions.insertBefore(denyButton, confirmButton);\n      } else {\n        actions.insertBefore(cancelButton, loader);\n        actions.insertBefore(denyButton, loader);\n        actions.insertBefore(confirmButton, loader);\n      }\n    }\n  }\n\n  /**\n   * @param {HTMLElement} confirmButton\n   * @param {HTMLElement} denyButton\n   * @param {HTMLElement} cancelButton\n   * @param {SweetAlertOptions} params\n   */\n  function handleButtonsStyling(confirmButton, denyButton, cancelButton, params) {\n    if (!params.buttonsStyling) {\n      removeClass([confirmButton, denyButton, cancelButton], swalClasses.styled);\n      return;\n    }\n    addClass([confirmButton, denyButton, cancelButton], swalClasses.styled);\n\n    // Buttons background colors\n    if (params.confirmButtonColor) {\n      confirmButton.style.backgroundColor = params.confirmButtonColor;\n      addClass(confirmButton, swalClasses['default-outline']);\n    }\n    if (params.denyButtonColor) {\n      denyButton.style.backgroundColor = params.denyButtonColor;\n      addClass(denyButton, swalClasses['default-outline']);\n    }\n    if (params.cancelButtonColor) {\n      cancelButton.style.backgroundColor = params.cancelButtonColor;\n      addClass(cancelButton, swalClasses['default-outline']);\n    }\n  }\n\n  /**\n   * @param {HTMLElement} button\n   * @param {'confirm' | 'deny' | 'cancel'} buttonType\n   * @param {SweetAlertOptions} params\n   */\n  function renderButton(button, buttonType, params) {\n    const buttonName = /** @type {'Confirm' | 'Deny' | 'Cancel'} */capitalizeFirstLetter(buttonType);\n    toggle(button, params[`show${buttonName}Button`], 'inline-block');\n    setInnerHtml(button, params[`${buttonType}ButtonText`] || ''); // Set caption text\n    button.setAttribute('aria-label', params[`${buttonType}ButtonAriaLabel`] || ''); // ARIA label\n\n    // Add buttons custom classes\n    button.className = swalClasses[buttonType];\n    applyCustomClass(button, params, `${buttonType}Button`);\n  }\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderCloseButton = (instance, params) => {\n    const closeButton = getCloseButton();\n    if (!closeButton) {\n      return;\n    }\n    setInnerHtml(closeButton, params.closeButtonHtml || '');\n\n    // Custom class\n    applyCustomClass(closeButton, params, 'closeButton');\n    toggle(closeButton, params.showCloseButton);\n    closeButton.setAttribute('aria-label', params.closeButtonAriaLabel || '');\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderContainer = (instance, params) => {\n    const container = getContainer();\n    if (!container) {\n      return;\n    }\n    handleBackdropParam(container, params.backdrop);\n    handlePositionParam(container, params.position);\n    handleGrowParam(container, params.grow);\n\n    // Custom class\n    applyCustomClass(container, params, 'container');\n  };\n\n  /**\n   * @param {HTMLElement} container\n   * @param {SweetAlertOptions['backdrop']} backdrop\n   */\n  function handleBackdropParam(container, backdrop) {\n    if (typeof backdrop === 'string') {\n      container.style.background = backdrop;\n    } else if (!backdrop) {\n      addClass([document.documentElement, document.body], swalClasses['no-backdrop']);\n    }\n  }\n\n  /**\n   * @param {HTMLElement} container\n   * @param {SweetAlertOptions['position']} position\n   */\n  function handlePositionParam(container, position) {\n    if (!position) {\n      return;\n    }\n    if (position in swalClasses) {\n      addClass(container, swalClasses[position]);\n    } else {\n      warn('The \"position\" parameter is not valid, defaulting to \"center\"');\n      addClass(container, swalClasses.center);\n    }\n  }\n\n  /**\n   * @param {HTMLElement} container\n   * @param {SweetAlertOptions['grow']} grow\n   */\n  function handleGrowParam(container, grow) {\n    if (!grow) {\n      return;\n    }\n    addClass(container, swalClasses[`grow-${grow}`]);\n  }\n\n  /**\n   * This module contains `WeakMap`s for each effectively-\"private  property\" that a `Swal` has.\n   * For example, to set the private property \"foo\" of `this` to \"bar\", you can `privateProps.foo.set(this, 'bar')`\n   * This is the approach that Babel will probably take to implement private methods/fields\n   *   https://github.com/tc39/proposal-private-methods\n   *   https://github.com/babel/babel/pull/7555\n   * Once we have the changes from that PR in Babel, and our core class fits reasonable in *one module*\n   *   then we can use that language feature.\n   */\n\n  var privateProps = {\n    innerParams: new WeakMap(),\n    domCache: new WeakMap()\n  };\n\n  /// <reference path=\"../../../../sweetalert2.d.ts\"/>\n\n\n  /** @type {InputClass[]} */\n  const inputClasses = ['input', 'file', 'range', 'select', 'radio', 'checkbox', 'textarea'];\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderInput = (instance, params) => {\n    const popup = getPopup();\n    if (!popup) {\n      return;\n    }\n    const innerParams = privateProps.innerParams.get(instance);\n    const rerender = !innerParams || params.input !== innerParams.input;\n    inputClasses.forEach(inputClass => {\n      const inputContainer = getDirectChildByClass(popup, swalClasses[inputClass]);\n      if (!inputContainer) {\n        return;\n      }\n\n      // set attributes\n      setAttributes(inputClass, params.inputAttributes);\n\n      // set class\n      inputContainer.className = swalClasses[inputClass];\n      if (rerender) {\n        hide(inputContainer);\n      }\n    });\n    if (params.input) {\n      if (rerender) {\n        showInput(params);\n      }\n      // set custom class\n      setCustomClass(params);\n    }\n  };\n\n  /**\n   * @param {SweetAlertOptions} params\n   */\n  const showInput = params => {\n    if (!params.input) {\n      return;\n    }\n    if (!renderInputType[params.input]) {\n      error(`Unexpected type of input! Expected ${Object.keys(renderInputType).join(' | ')}, got \"${params.input}\"`);\n      return;\n    }\n    const inputContainer = getInputContainer(params.input);\n    if (!inputContainer) {\n      return;\n    }\n    const input = renderInputType[params.input](inputContainer, params);\n    show(inputContainer);\n\n    // input autofocus\n    if (params.inputAutoFocus) {\n      setTimeout(() => {\n        focusInput(input);\n      });\n    }\n  };\n\n  /**\n   * @param {HTMLInputElement} input\n   */\n  const removeAttributes = input => {\n    for (let i = 0; i < input.attributes.length; i++) {\n      const attrName = input.attributes[i].name;\n      if (!['id', 'type', 'value', 'style'].includes(attrName)) {\n        input.removeAttribute(attrName);\n      }\n    }\n  };\n\n  /**\n   * @param {InputClass} inputClass\n   * @param {SweetAlertOptions['inputAttributes']} inputAttributes\n   */\n  const setAttributes = (inputClass, inputAttributes) => {\n    const popup = getPopup();\n    if (!popup) {\n      return;\n    }\n    const input = getInput$1(popup, inputClass);\n    if (!input) {\n      return;\n    }\n    removeAttributes(input);\n    for (const attr in inputAttributes) {\n      input.setAttribute(attr, inputAttributes[attr]);\n    }\n  };\n\n  /**\n   * @param {SweetAlertOptions} params\n   */\n  const setCustomClass = params => {\n    if (!params.input) {\n      return;\n    }\n    const inputContainer = getInputContainer(params.input);\n    if (inputContainer) {\n      applyCustomClass(inputContainer, params, 'input');\n    }\n  };\n\n  /**\n   * @param {HTMLInputElement | HTMLTextAreaElement} input\n   * @param {SweetAlertOptions} params\n   */\n  const setInputPlaceholder = (input, params) => {\n    if (!input.placeholder && params.inputPlaceholder) {\n      input.placeholder = params.inputPlaceholder;\n    }\n  };\n\n  /**\n   * @param {Input} input\n   * @param {Input} prependTo\n   * @param {SweetAlertOptions} params\n   */\n  const setInputLabel = (input, prependTo, params) => {\n    if (params.inputLabel) {\n      const label = document.createElement('label');\n      const labelClass = swalClasses['input-label'];\n      label.setAttribute('for', input.id);\n      label.className = labelClass;\n      if (typeof params.customClass === 'object') {\n        addClass(label, params.customClass.inputLabel);\n      }\n      label.innerText = params.inputLabel;\n      prependTo.insertAdjacentElement('beforebegin', label);\n    }\n  };\n\n  /**\n   * @param {SweetAlertInput} inputType\n   * @returns {HTMLElement | undefined}\n   */\n  const getInputContainer = inputType => {\n    const popup = getPopup();\n    if (!popup) {\n      return;\n    }\n    return getDirectChildByClass(popup, swalClasses[(/** @type {SwalClass} */inputType)] || swalClasses.input);\n  };\n\n  /**\n   * @param {HTMLInputElement | HTMLOutputElement | HTMLTextAreaElement} input\n   * @param {SweetAlertOptions['inputValue']} inputValue\n   */\n  const checkAndSetInputValue = (input, inputValue) => {\n    if (['string', 'number'].includes(typeof inputValue)) {\n      input.value = `${inputValue}`;\n    } else if (!isPromise(inputValue)) {\n      warn(`Unexpected type of inputValue! Expected \"string\", \"number\" or \"Promise\", got \"${typeof inputValue}\"`);\n    }\n  };\n\n  /** @type {Record<SweetAlertInput, (input: Input | HTMLElement, params: SweetAlertOptions) => Input>} */\n  const renderInputType = {};\n\n  /**\n   * @param {HTMLInputElement} input\n   * @param {SweetAlertOptions} params\n   * @returns {HTMLInputElement}\n   */\n  renderInputType.text = renderInputType.email = renderInputType.password = renderInputType.number = renderInputType.tel = renderInputType.url = renderInputType.search = renderInputType.date = renderInputType['datetime-local'] = renderInputType.time = renderInputType.week = renderInputType.month = /** @type {(input: Input | HTMLElement, params: SweetAlertOptions) => Input} */\n  (input, params) => {\n    checkAndSetInputValue(input, params.inputValue);\n    setInputLabel(input, input, params);\n    setInputPlaceholder(input, params);\n    input.type = params.input;\n    return input;\n  };\n\n  /**\n   * @param {HTMLInputElement} input\n   * @param {SweetAlertOptions} params\n   * @returns {HTMLInputElement}\n   */\n  renderInputType.file = (input, params) => {\n    setInputLabel(input, input, params);\n    setInputPlaceholder(input, params);\n    return input;\n  };\n\n  /**\n   * @param {HTMLInputElement} range\n   * @param {SweetAlertOptions} params\n   * @returns {HTMLInputElement}\n   */\n  renderInputType.range = (range, params) => {\n    const rangeInput = range.querySelector('input');\n    const rangeOutput = range.querySelector('output');\n    checkAndSetInputValue(rangeInput, params.inputValue);\n    rangeInput.type = params.input;\n    checkAndSetInputValue(rangeOutput, params.inputValue);\n    setInputLabel(rangeInput, range, params);\n    return range;\n  };\n\n  /**\n   * @param {HTMLSelectElement} select\n   * @param {SweetAlertOptions} params\n   * @returns {HTMLSelectElement}\n   */\n  renderInputType.select = (select, params) => {\n    select.textContent = '';\n    if (params.inputPlaceholder) {\n      const placeholder = document.createElement('option');\n      setInnerHtml(placeholder, params.inputPlaceholder);\n      placeholder.value = '';\n      placeholder.disabled = true;\n      placeholder.selected = true;\n      select.appendChild(placeholder);\n    }\n    setInputLabel(select, select, params);\n    return select;\n  };\n\n  /**\n   * @param {HTMLInputElement} radio\n   * @returns {HTMLInputElement}\n   */\n  renderInputType.radio = radio => {\n    radio.textContent = '';\n    return radio;\n  };\n\n  /**\n   * @param {HTMLLabelElement} checkboxContainer\n   * @param {SweetAlertOptions} params\n   * @returns {HTMLInputElement}\n   */\n  renderInputType.checkbox = (checkboxContainer, params) => {\n    const checkbox = getInput$1(getPopup(), 'checkbox');\n    checkbox.value = '1';\n    checkbox.checked = Boolean(params.inputValue);\n    const label = checkboxContainer.querySelector('span');\n    setInnerHtml(label, params.inputPlaceholder || params.inputLabel);\n    return checkbox;\n  };\n\n  /**\n   * @param {HTMLTextAreaElement} textarea\n   * @param {SweetAlertOptions} params\n   * @returns {HTMLTextAreaElement}\n   */\n  renderInputType.textarea = (textarea, params) => {\n    checkAndSetInputValue(textarea, params.inputValue);\n    setInputPlaceholder(textarea, params);\n    setInputLabel(textarea, textarea, params);\n\n    /**\n     * @param {HTMLElement} el\n     * @returns {number}\n     */\n    const getMargin = el => parseInt(window.getComputedStyle(el).marginLeft) + parseInt(window.getComputedStyle(el).marginRight);\n\n    // https://github.com/sweetalert2/sweetalert2/issues/2291\n    setTimeout(() => {\n      // https://github.com/sweetalert2/sweetalert2/issues/1699\n      if ('MutationObserver' in window) {\n        const initialPopupWidth = parseInt(window.getComputedStyle(getPopup()).width);\n        const textareaResizeHandler = () => {\n          // check if texarea is still in document (i.e. popup wasn't closed in the meantime)\n          if (!document.body.contains(textarea)) {\n            return;\n          }\n          const textareaWidth = textarea.offsetWidth + getMargin(textarea);\n          if (textareaWidth > initialPopupWidth) {\n            getPopup().style.width = `${textareaWidth}px`;\n          } else {\n            applyNumericalStyle(getPopup(), 'width', params.width);\n          }\n        };\n        new MutationObserver(textareaResizeHandler).observe(textarea, {\n          attributes: true,\n          attributeFilter: ['style']\n        });\n      }\n    });\n    return textarea;\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderContent = (instance, params) => {\n    const htmlContainer = getHtmlContainer();\n    if (!htmlContainer) {\n      return;\n    }\n    showWhenInnerHtmlPresent(htmlContainer);\n    applyCustomClass(htmlContainer, params, 'htmlContainer');\n\n    // Content as HTML\n    if (params.html) {\n      parseHtmlToContainer(params.html, htmlContainer);\n      show(htmlContainer, 'block');\n    }\n\n    // Content as plain text\n    else if (params.text) {\n      htmlContainer.textContent = params.text;\n      show(htmlContainer, 'block');\n    }\n\n    // No content\n    else {\n      hide(htmlContainer);\n    }\n    renderInput(instance, params);\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderFooter = (instance, params) => {\n    const footer = getFooter();\n    if (!footer) {\n      return;\n    }\n    showWhenInnerHtmlPresent(footer);\n    toggle(footer, params.footer, 'block');\n    if (params.footer) {\n      parseHtmlToContainer(params.footer, footer);\n    }\n\n    // Custom class\n    applyCustomClass(footer, params, 'footer');\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderIcon = (instance, params) => {\n    const innerParams = privateProps.innerParams.get(instance);\n    const icon = getIcon();\n    if (!icon) {\n      return;\n    }\n\n    // if the given icon already rendered, apply the styling without re-rendering the icon\n    if (innerParams && params.icon === innerParams.icon) {\n      // Custom or default content\n      setContent(icon, params);\n      applyStyles(icon, params);\n      return;\n    }\n    if (!params.icon && !params.iconHtml) {\n      hide(icon);\n      return;\n    }\n    if (params.icon && Object.keys(iconTypes).indexOf(params.icon) === -1) {\n      error(`Unknown icon! Expected \"success\", \"error\", \"warning\", \"info\" or \"question\", got \"${params.icon}\"`);\n      hide(icon);\n      return;\n    }\n    show(icon);\n\n    // Custom or default content\n    setContent(icon, params);\n    applyStyles(icon, params);\n\n    // Animate icon\n    addClass(icon, params.showClass && params.showClass.icon);\n\n    // Re-adjust the success icon on system theme change\n    const colorSchemeQueryList = window.matchMedia('(prefers-color-scheme: dark)');\n    colorSchemeQueryList.addEventListener('change', adjustSuccessIconBackgroundColor);\n  };\n\n  /**\n   * @param {HTMLElement} icon\n   * @param {SweetAlertOptions} params\n   */\n  const applyStyles = (icon, params) => {\n    for (const [iconType, iconClassName] of Object.entries(iconTypes)) {\n      if (params.icon !== iconType) {\n        removeClass(icon, iconClassName);\n      }\n    }\n    addClass(icon, params.icon && iconTypes[params.icon]);\n\n    // Icon color\n    setColor(icon, params);\n\n    // Success icon background color\n    adjustSuccessIconBackgroundColor();\n\n    // Custom class\n    applyCustomClass(icon, params, 'icon');\n  };\n\n  // Adjust success icon background color to match the popup background color\n  const adjustSuccessIconBackgroundColor = () => {\n    const popup = getPopup();\n    if (!popup) {\n      return;\n    }\n    const popupBackgroundColor = window.getComputedStyle(popup).getPropertyValue('background-color');\n    /** @type {NodeListOf<HTMLElement>} */\n    const successIconParts = popup.querySelectorAll('[class^=swal2-success-circular-line], .swal2-success-fix');\n    for (let i = 0; i < successIconParts.length; i++) {\n      successIconParts[i].style.backgroundColor = popupBackgroundColor;\n    }\n  };\n  const successIconHtml = `\n  <div class=\"swal2-success-circular-line-left\"></div>\n  <span class=\"swal2-success-line-tip\"></span> <span class=\"swal2-success-line-long\"></span>\n  <div class=\"swal2-success-ring\"></div> <div class=\"swal2-success-fix\"></div>\n  <div class=\"swal2-success-circular-line-right\"></div>\n`;\n  const errorIconHtml = `\n  <span class=\"swal2-x-mark\">\n    <span class=\"swal2-x-mark-line-left\"></span>\n    <span class=\"swal2-x-mark-line-right\"></span>\n  </span>\n`;\n\n  /**\n   * @param {HTMLElement} icon\n   * @param {SweetAlertOptions} params\n   */\n  const setContent = (icon, params) => {\n    if (!params.icon && !params.iconHtml) {\n      return;\n    }\n    let oldContent = icon.innerHTML;\n    let newContent = '';\n    if (params.iconHtml) {\n      newContent = iconContent(params.iconHtml);\n    } else if (params.icon === 'success') {\n      newContent = successIconHtml;\n      oldContent = oldContent.replace(/ style=\".*?\"/g, ''); // undo adjustSuccessIconBackgroundColor()\n    } else if (params.icon === 'error') {\n      newContent = errorIconHtml;\n    } else if (params.icon) {\n      const defaultIconHtml = {\n        question: '?',\n        warning: '!',\n        info: 'i'\n      };\n      newContent = iconContent(defaultIconHtml[params.icon]);\n    }\n    if (oldContent.trim() !== newContent.trim()) {\n      setInnerHtml(icon, newContent);\n    }\n  };\n\n  /**\n   * @param {HTMLElement} icon\n   * @param {SweetAlertOptions} params\n   */\n  const setColor = (icon, params) => {\n    if (!params.iconColor) {\n      return;\n    }\n    icon.style.color = params.iconColor;\n    icon.style.borderColor = params.iconColor;\n    for (const sel of ['.swal2-success-line-tip', '.swal2-success-line-long', '.swal2-x-mark-line-left', '.swal2-x-mark-line-right']) {\n      setStyle(icon, sel, 'background-color', params.iconColor);\n    }\n    setStyle(icon, '.swal2-success-ring', 'border-color', params.iconColor);\n  };\n\n  /**\n   * @param {string} content\n   * @returns {string}\n   */\n  const iconContent = content => `<div class=\"${swalClasses['icon-content']}\">${content}</div>`;\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderImage = (instance, params) => {\n    const image = getImage();\n    if (!image) {\n      return;\n    }\n    if (!params.imageUrl) {\n      hide(image);\n      return;\n    }\n    show(image, '');\n\n    // Src, alt\n    image.setAttribute('src', params.imageUrl);\n    image.setAttribute('alt', params.imageAlt || '');\n\n    // Width, height\n    applyNumericalStyle(image, 'width', params.imageWidth);\n    applyNumericalStyle(image, 'height', params.imageHeight);\n\n    // Class\n    image.className = swalClasses.image;\n    applyCustomClass(image, params, 'image');\n  };\n\n  let dragging = false;\n  let mousedownX = 0;\n  let mousedownY = 0;\n  let initialX = 0;\n  let initialY = 0;\n\n  /**\n   * @param {HTMLElement} popup\n   */\n  const addDraggableListeners = popup => {\n    popup.addEventListener('mousedown', down);\n    document.body.addEventListener('mousemove', move);\n    popup.addEventListener('mouseup', up);\n    popup.addEventListener('touchstart', down);\n    document.body.addEventListener('touchmove', move);\n    popup.addEventListener('touchend', up);\n  };\n\n  /**\n   * @param {HTMLElement} popup\n   */\n  const removeDraggableListeners = popup => {\n    popup.removeEventListener('mousedown', down);\n    document.body.removeEventListener('mousemove', move);\n    popup.removeEventListener('mouseup', up);\n    popup.removeEventListener('touchstart', down);\n    document.body.removeEventListener('touchmove', move);\n    popup.removeEventListener('touchend', up);\n  };\n\n  /**\n   * @param {MouseEvent | TouchEvent} event\n   */\n  const down = event => {\n    const popup = getPopup();\n    if (event.target === popup || getIcon().contains(/** @type {HTMLElement} */event.target)) {\n      dragging = true;\n      const clientXY = getClientXY(event);\n      mousedownX = clientXY.clientX;\n      mousedownY = clientXY.clientY;\n      initialX = parseInt(popup.style.insetInlineStart) || 0;\n      initialY = parseInt(popup.style.insetBlockStart) || 0;\n      addClass(popup, 'swal2-dragging');\n    }\n  };\n\n  /**\n   * @param {MouseEvent | TouchEvent} event\n   */\n  const move = event => {\n    const popup = getPopup();\n    if (dragging) {\n      let {\n        clientX,\n        clientY\n      } = getClientXY(event);\n      popup.style.insetInlineStart = `${initialX + (clientX - mousedownX)}px`;\n      popup.style.insetBlockStart = `${initialY + (clientY - mousedownY)}px`;\n    }\n  };\n  const up = () => {\n    const popup = getPopup();\n    dragging = false;\n    removeClass(popup, 'swal2-dragging');\n  };\n\n  /**\n   * @param {MouseEvent | TouchEvent} event\n   * @returns {{ clientX: number, clientY: number }}\n   */\n  const getClientXY = event => {\n    let clientX = 0,\n      clientY = 0;\n    if (event.type.startsWith('mouse')) {\n      clientX = /** @type {MouseEvent} */event.clientX;\n      clientY = /** @type {MouseEvent} */event.clientY;\n    } else if (event.type.startsWith('touch')) {\n      clientX = /** @type {TouchEvent} */event.touches[0].clientX;\n      clientY = /** @type {TouchEvent} */event.touches[0].clientY;\n    }\n    return {\n      clientX,\n      clientY\n    };\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderPopup = (instance, params) => {\n    const container = getContainer();\n    const popup = getPopup();\n    if (!container || !popup) {\n      return;\n    }\n\n    // Width\n    // https://github.com/sweetalert2/sweetalert2/issues/2170\n    if (params.toast) {\n      applyNumericalStyle(container, 'width', params.width);\n      popup.style.width = '100%';\n      const loader = getLoader();\n      if (loader) {\n        popup.insertBefore(loader, getIcon());\n      }\n    } else {\n      applyNumericalStyle(popup, 'width', params.width);\n    }\n\n    // Padding\n    applyNumericalStyle(popup, 'padding', params.padding);\n\n    // Color\n    if (params.color) {\n      popup.style.color = params.color;\n    }\n\n    // Background\n    if (params.background) {\n      popup.style.background = params.background;\n    }\n    hide(getValidationMessage());\n\n    // Classes\n    addClasses$1(popup, params);\n    if (params.draggable && !params.toast) {\n      addClass(popup, swalClasses.draggable);\n      addDraggableListeners(popup);\n    } else {\n      removeClass(popup, swalClasses.draggable);\n      removeDraggableListeners(popup);\n    }\n  };\n\n  /**\n   * @param {HTMLElement} popup\n   * @param {SweetAlertOptions} params\n   */\n  const addClasses$1 = (popup, params) => {\n    const showClass = params.showClass || {};\n    // Default Class + showClass when updating Swal.update({})\n    popup.className = `${swalClasses.popup} ${isVisible$1(popup) ? showClass.popup : ''}`;\n    if (params.toast) {\n      addClass([document.documentElement, document.body], swalClasses['toast-shown']);\n      addClass(popup, swalClasses.toast);\n    } else {\n      addClass(popup, swalClasses.modal);\n    }\n\n    // Custom class\n    applyCustomClass(popup, params, 'popup');\n    // TODO: remove in the next major\n    if (typeof params.customClass === 'string') {\n      addClass(popup, params.customClass);\n    }\n\n    // Icon class (#1842)\n    if (params.icon) {\n      addClass(popup, swalClasses[`icon-${params.icon}`]);\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderProgressSteps = (instance, params) => {\n    const progressStepsContainer = getProgressSteps();\n    if (!progressStepsContainer) {\n      return;\n    }\n    const {\n      progressSteps,\n      currentProgressStep\n    } = params;\n    if (!progressSteps || progressSteps.length === 0 || currentProgressStep === undefined) {\n      hide(progressStepsContainer);\n      return;\n    }\n    show(progressStepsContainer);\n    progressStepsContainer.textContent = '';\n    if (currentProgressStep >= progressSteps.length) {\n      warn('Invalid currentProgressStep parameter, it should be less than progressSteps.length ' + '(currentProgressStep like JS arrays starts from 0)');\n    }\n    progressSteps.forEach((step, index) => {\n      const stepEl = createStepElement(step);\n      progressStepsContainer.appendChild(stepEl);\n      if (index === currentProgressStep) {\n        addClass(stepEl, swalClasses['active-progress-step']);\n      }\n      if (index !== progressSteps.length - 1) {\n        const lineEl = createLineElement(params);\n        progressStepsContainer.appendChild(lineEl);\n      }\n    });\n  };\n\n  /**\n   * @param {string} step\n   * @returns {HTMLLIElement}\n   */\n  const createStepElement = step => {\n    const stepEl = document.createElement('li');\n    addClass(stepEl, swalClasses['progress-step']);\n    setInnerHtml(stepEl, step);\n    return stepEl;\n  };\n\n  /**\n   * @param {SweetAlertOptions} params\n   * @returns {HTMLLIElement}\n   */\n  const createLineElement = params => {\n    const lineEl = document.createElement('li');\n    addClass(lineEl, swalClasses['progress-step-line']);\n    if (params.progressStepsDistance) {\n      applyNumericalStyle(lineEl, 'width', params.progressStepsDistance);\n    }\n    return lineEl;\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderTitle = (instance, params) => {\n    const title = getTitle();\n    if (!title) {\n      return;\n    }\n    showWhenInnerHtmlPresent(title);\n    toggle(title, params.title || params.titleText, 'block');\n    if (params.title) {\n      parseHtmlToContainer(params.title, title);\n    }\n    if (params.titleText) {\n      title.innerText = params.titleText;\n    }\n\n    // Custom class\n    applyCustomClass(title, params, 'title');\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const render = (instance, params) => {\n    renderPopup(instance, params);\n    renderContainer(instance, params);\n    renderProgressSteps(instance, params);\n    renderIcon(instance, params);\n    renderImage(instance, params);\n    renderTitle(instance, params);\n    renderCloseButton(instance, params);\n    renderContent(instance, params);\n    renderActions(instance, params);\n    renderFooter(instance, params);\n    const popup = getPopup();\n    if (typeof params.didRender === 'function' && popup) {\n      params.didRender(popup);\n    }\n    globalState.eventEmitter.emit('didRender', popup);\n  };\n\n  /*\n   * Global function to determine if SweetAlert2 popup is shown\n   */\n  const isVisible = () => {\n    return isVisible$1(getPopup());\n  };\n\n  /*\n   * Global function to click 'Confirm' button\n   */\n  const clickConfirm = () => {\n    var _dom$getConfirmButton;\n    return (_dom$getConfirmButton = getConfirmButton()) === null || _dom$getConfirmButton === void 0 ? void 0 : _dom$getConfirmButton.click();\n  };\n\n  /*\n   * Global function to click 'Deny' button\n   */\n  const clickDeny = () => {\n    var _dom$getDenyButton;\n    return (_dom$getDenyButton = getDenyButton()) === null || _dom$getDenyButton === void 0 ? void 0 : _dom$getDenyButton.click();\n  };\n\n  /*\n   * Global function to click 'Cancel' button\n   */\n  const clickCancel = () => {\n    var _dom$getCancelButton;\n    return (_dom$getCancelButton = getCancelButton()) === null || _dom$getCancelButton === void 0 ? void 0 : _dom$getCancelButton.click();\n  };\n\n  /** @typedef {'cancel' | 'backdrop' | 'close' | 'esc' | 'timer'} DismissReason */\n\n  /** @type {Record<DismissReason, DismissReason>} */\n  const DismissReason = Object.freeze({\n    cancel: 'cancel',\n    backdrop: 'backdrop',\n    close: 'close',\n    esc: 'esc',\n    timer: 'timer'\n  });\n\n  /**\n   * @param {GlobalState} globalState\n   */\n  const removeKeydownHandler = globalState => {\n    if (globalState.keydownTarget && globalState.keydownHandlerAdded) {\n      globalState.keydownTarget.removeEventListener('keydown', globalState.keydownHandler, {\n        capture: globalState.keydownListenerCapture\n      });\n      globalState.keydownHandlerAdded = false;\n    }\n  };\n\n  /**\n   * @param {GlobalState} globalState\n   * @param {SweetAlertOptions} innerParams\n   * @param {*} dismissWith\n   */\n  const addKeydownHandler = (globalState, innerParams, dismissWith) => {\n    removeKeydownHandler(globalState);\n    if (!innerParams.toast) {\n      globalState.keydownHandler = e => keydownHandler(innerParams, e, dismissWith);\n      globalState.keydownTarget = innerParams.keydownListenerCapture ? window : getPopup();\n      globalState.keydownListenerCapture = innerParams.keydownListenerCapture;\n      globalState.keydownTarget.addEventListener('keydown', globalState.keydownHandler, {\n        capture: globalState.keydownListenerCapture\n      });\n      globalState.keydownHandlerAdded = true;\n    }\n  };\n\n  /**\n   * @param {number} index\n   * @param {number} increment\n   */\n  const setFocus = (index, increment) => {\n    var _dom$getPopup;\n    const focusableElements = getFocusableElements();\n    // search for visible elements and select the next possible match\n    if (focusableElements.length) {\n      index = index + increment;\n\n      // rollover to first item\n      if (index === focusableElements.length) {\n        index = 0;\n\n        // go to last item\n      } else if (index === -1) {\n        index = focusableElements.length - 1;\n      }\n      focusableElements[index].focus();\n      return;\n    }\n    // no visible focusable elements, focus the popup\n    (_dom$getPopup = getPopup()) === null || _dom$getPopup === void 0 || _dom$getPopup.focus();\n  };\n  const arrowKeysNextButton = ['ArrowRight', 'ArrowDown'];\n  const arrowKeysPreviousButton = ['ArrowLeft', 'ArrowUp'];\n\n  /**\n   * @param {SweetAlertOptions} innerParams\n   * @param {KeyboardEvent} event\n   * @param {Function} dismissWith\n   */\n  const keydownHandler = (innerParams, event, dismissWith) => {\n    if (!innerParams) {\n      return; // This instance has already been destroyed\n    }\n\n    // Ignore keydown during IME composition\n    // https://developer.mozilla.org/en-US/docs/Web/API/Document/keydown_event#ignoring_keydown_during_ime_composition\n    // https://github.com/sweetalert2/sweetalert2/issues/720\n    // https://github.com/sweetalert2/sweetalert2/issues/2406\n    if (event.isComposing || event.keyCode === 229) {\n      return;\n    }\n    if (innerParams.stopKeydownPropagation) {\n      event.stopPropagation();\n    }\n\n    // ENTER\n    if (event.key === 'Enter') {\n      handleEnter(event, innerParams);\n    }\n\n    // TAB\n    else if (event.key === 'Tab') {\n      handleTab(event);\n    }\n\n    // ARROWS - switch focus between buttons\n    else if ([...arrowKeysNextButton, ...arrowKeysPreviousButton].includes(event.key)) {\n      handleArrows(event.key);\n    }\n\n    // ESC\n    else if (event.key === 'Escape') {\n      handleEsc(event, innerParams, dismissWith);\n    }\n  };\n\n  /**\n   * @param {KeyboardEvent} event\n   * @param {SweetAlertOptions} innerParams\n   */\n  const handleEnter = (event, innerParams) => {\n    // https://github.com/sweetalert2/sweetalert2/issues/2386\n    if (!callIfFunction(innerParams.allowEnterKey)) {\n      return;\n    }\n    const input = getInput$1(getPopup(), innerParams.input);\n    if (event.target && input && event.target instanceof HTMLElement && event.target.outerHTML === input.outerHTML) {\n      if (['textarea', 'file'].includes(innerParams.input)) {\n        return; // do not submit\n      }\n      clickConfirm();\n      event.preventDefault();\n    }\n  };\n\n  /**\n   * @param {KeyboardEvent} event\n   */\n  const handleTab = event => {\n    const targetElement = event.target;\n    const focusableElements = getFocusableElements();\n    let btnIndex = -1;\n    for (let i = 0; i < focusableElements.length; i++) {\n      if (targetElement === focusableElements[i]) {\n        btnIndex = i;\n        break;\n      }\n    }\n\n    // Cycle to the next button\n    if (!event.shiftKey) {\n      setFocus(btnIndex, 1);\n    }\n\n    // Cycle to the prev button\n    else {\n      setFocus(btnIndex, -1);\n    }\n    event.stopPropagation();\n    event.preventDefault();\n  };\n\n  /**\n   * @param {string} key\n   */\n  const handleArrows = key => {\n    const actions = getActions();\n    const confirmButton = getConfirmButton();\n    const denyButton = getDenyButton();\n    const cancelButton = getCancelButton();\n    if (!actions || !confirmButton || !denyButton || !cancelButton) {\n      return;\n    }\n    /** @type HTMLElement[] */\n    const buttons = [confirmButton, denyButton, cancelButton];\n    if (document.activeElement instanceof HTMLElement && !buttons.includes(document.activeElement)) {\n      return;\n    }\n    const sibling = arrowKeysNextButton.includes(key) ? 'nextElementSibling' : 'previousElementSibling';\n    let buttonToFocus = document.activeElement;\n    if (!buttonToFocus) {\n      return;\n    }\n    for (let i = 0; i < actions.children.length; i++) {\n      buttonToFocus = buttonToFocus[sibling];\n      if (!buttonToFocus) {\n        return;\n      }\n      if (buttonToFocus instanceof HTMLButtonElement && isVisible$1(buttonToFocus)) {\n        break;\n      }\n    }\n    if (buttonToFocus instanceof HTMLButtonElement) {\n      buttonToFocus.focus();\n    }\n  };\n\n  /**\n   * @param {KeyboardEvent} event\n   * @param {SweetAlertOptions} innerParams\n   * @param {Function} dismissWith\n   */\n  const handleEsc = (event, innerParams, dismissWith) => {\n    if (callIfFunction(innerParams.allowEscapeKey)) {\n      event.preventDefault();\n      dismissWith(DismissReason.esc);\n    }\n  };\n\n  /**\n   * This module contains `WeakMap`s for each effectively-\"private  property\" that a `Swal` has.\n   * For example, to set the private property \"foo\" of `this` to \"bar\", you can `privateProps.foo.set(this, 'bar')`\n   * This is the approach that Babel will probably take to implement private methods/fields\n   *   https://github.com/tc39/proposal-private-methods\n   *   https://github.com/babel/babel/pull/7555\n   * Once we have the changes from that PR in Babel, and our core class fits reasonable in *one module*\n   *   then we can use that language feature.\n   */\n\n  var privateMethods = {\n    swalPromiseResolve: new WeakMap(),\n    swalPromiseReject: new WeakMap()\n  };\n\n  // From https://developer.paciellogroup.com/blog/2018/06/the-current-state-of-modal-dialog-accessibility/\n  // Adding aria-hidden=\"true\" to elements outside of the active modal dialog ensures that\n  // elements not within the active modal dialog will not be surfaced if a user opens a screen\n  // reader’s list of elements (headings, form controls, landmarks, etc.) in the document.\n\n  const setAriaHidden = () => {\n    const container = getContainer();\n    const bodyChildren = Array.from(document.body.children);\n    bodyChildren.forEach(el => {\n      if (el.contains(container)) {\n        return;\n      }\n      if (el.hasAttribute('aria-hidden')) {\n        el.setAttribute('data-previous-aria-hidden', el.getAttribute('aria-hidden') || '');\n      }\n      el.setAttribute('aria-hidden', 'true');\n    });\n  };\n  const unsetAriaHidden = () => {\n    const bodyChildren = Array.from(document.body.children);\n    bodyChildren.forEach(el => {\n      if (el.hasAttribute('data-previous-aria-hidden')) {\n        el.setAttribute('aria-hidden', el.getAttribute('data-previous-aria-hidden') || '');\n        el.removeAttribute('data-previous-aria-hidden');\n      } else {\n        el.removeAttribute('aria-hidden');\n      }\n    });\n  };\n\n  // @ts-ignore\n  const isSafariOrIOS = typeof window !== 'undefined' && !!window.GestureEvent; // true for Safari desktop + all iOS browsers https://stackoverflow.com/a/70585394\n\n  /**\n   * Fix iOS scrolling\n   * http://stackoverflow.com/q/39626302\n   */\n  const iOSfix = () => {\n    if (isSafariOrIOS && !hasClass(document.body, swalClasses.iosfix)) {\n      const offset = document.body.scrollTop;\n      document.body.style.top = `${offset * -1}px`;\n      addClass(document.body, swalClasses.iosfix);\n      lockBodyScroll();\n    }\n  };\n\n  /**\n   * https://github.com/sweetalert2/sweetalert2/issues/1246\n   */\n  const lockBodyScroll = () => {\n    const container = getContainer();\n    if (!container) {\n      return;\n    }\n    /** @type {boolean} */\n    let preventTouchMove;\n    /**\n     * @param {TouchEvent} event\n     */\n    container.ontouchstart = event => {\n      preventTouchMove = shouldPreventTouchMove(event);\n    };\n    /**\n     * @param {TouchEvent} event\n     */\n    container.ontouchmove = event => {\n      if (preventTouchMove) {\n        event.preventDefault();\n        event.stopPropagation();\n      }\n    };\n  };\n\n  /**\n   * @param {TouchEvent} event\n   * @returns {boolean}\n   */\n  const shouldPreventTouchMove = event => {\n    const target = event.target;\n    const container = getContainer();\n    const htmlContainer = getHtmlContainer();\n    if (!container || !htmlContainer) {\n      return false;\n    }\n    if (isStylus(event) || isZoom(event)) {\n      return false;\n    }\n    if (target === container) {\n      return true;\n    }\n    if (!isScrollable(container) && target instanceof HTMLElement && target.tagName !== 'INPUT' &&\n    // #1603\n    target.tagName !== 'TEXTAREA' &&\n    // #2266\n    !(isScrollable(htmlContainer) &&\n    // #1944\n    htmlContainer.contains(target))) {\n      return true;\n    }\n    return false;\n  };\n\n  /**\n   * https://github.com/sweetalert2/sweetalert2/issues/1786\n   *\n   * @param {*} event\n   * @returns {boolean}\n   */\n  const isStylus = event => {\n    return event.touches && event.touches.length && event.touches[0].touchType === 'stylus';\n  };\n\n  /**\n   * https://github.com/sweetalert2/sweetalert2/issues/1891\n   *\n   * @param {TouchEvent} event\n   * @returns {boolean}\n   */\n  const isZoom = event => {\n    return event.touches && event.touches.length > 1;\n  };\n  const undoIOSfix = () => {\n    if (hasClass(document.body, swalClasses.iosfix)) {\n      const offset = parseInt(document.body.style.top, 10);\n      removeClass(document.body, swalClasses.iosfix);\n      document.body.style.top = '';\n      document.body.scrollTop = offset * -1;\n    }\n  };\n\n  /**\n   * Measure scrollbar width for padding body during modal show/hide\n   * https://github.com/twbs/bootstrap/blob/master/js/src/modal.js\n   *\n   * @returns {number}\n   */\n  const measureScrollbar = () => {\n    const scrollDiv = document.createElement('div');\n    scrollDiv.className = swalClasses['scrollbar-measure'];\n    document.body.appendChild(scrollDiv);\n    const scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth;\n    document.body.removeChild(scrollDiv);\n    return scrollbarWidth;\n  };\n\n  /**\n   * Remember state in cases where opening and handling a modal will fiddle with it.\n   * @type {number | null}\n   */\n  let previousBodyPadding = null;\n\n  /**\n   * @param {string} initialBodyOverflow\n   */\n  const replaceScrollbarWithPadding = initialBodyOverflow => {\n    // for queues, do not do this more than once\n    if (previousBodyPadding !== null) {\n      return;\n    }\n    // if the body has overflow\n    if (document.body.scrollHeight > window.innerHeight || initialBodyOverflow === 'scroll' // https://github.com/sweetalert2/sweetalert2/issues/2663\n    ) {\n      // add padding so the content doesn't shift after removal of scrollbar\n      previousBodyPadding = parseInt(window.getComputedStyle(document.body).getPropertyValue('padding-right'));\n      document.body.style.paddingRight = `${previousBodyPadding + measureScrollbar()}px`;\n    }\n  };\n  const undoReplaceScrollbarWithPadding = () => {\n    if (previousBodyPadding !== null) {\n      document.body.style.paddingRight = `${previousBodyPadding}px`;\n      previousBodyPadding = null;\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {HTMLElement} container\n   * @param {boolean} returnFocus\n   * @param {Function} didClose\n   */\n  function removePopupAndResetState(instance, container, returnFocus, didClose) {\n    if (isToast()) {\n      triggerDidCloseAndDispose(instance, didClose);\n    } else {\n      restoreActiveElement(returnFocus).then(() => triggerDidCloseAndDispose(instance, didClose));\n      removeKeydownHandler(globalState);\n    }\n\n    // workaround for https://github.com/sweetalert2/sweetalert2/issues/2088\n    // for some reason removing the container in Safari will scroll the document to bottom\n    if (isSafariOrIOS) {\n      container.setAttribute('style', 'display:none !important');\n      container.removeAttribute('class');\n      container.innerHTML = '';\n    } else {\n      container.remove();\n    }\n    if (isModal()) {\n      undoReplaceScrollbarWithPadding();\n      undoIOSfix();\n      unsetAriaHidden();\n    }\n    removeBodyClasses();\n  }\n\n  /**\n   * Remove SweetAlert2 classes from body\n   */\n  function removeBodyClasses() {\n    removeClass([document.documentElement, document.body], [swalClasses.shown, swalClasses['height-auto'], swalClasses['no-backdrop'], swalClasses['toast-shown']]);\n  }\n\n  /**\n   * Instance method to close sweetAlert\n   *\n   * @param {any} resolveValue\n   */\n  function close(resolveValue) {\n    resolveValue = prepareResolveValue(resolveValue);\n    const swalPromiseResolve = privateMethods.swalPromiseResolve.get(this);\n    const didClose = triggerClosePopup(this);\n    if (this.isAwaitingPromise) {\n      // A swal awaiting for a promise (after a click on Confirm or Deny) cannot be dismissed anymore #2335\n      if (!resolveValue.isDismissed) {\n        handleAwaitingPromise(this);\n        swalPromiseResolve(resolveValue);\n      }\n    } else if (didClose) {\n      // Resolve Swal promise\n      swalPromiseResolve(resolveValue);\n    }\n  }\n  const triggerClosePopup = instance => {\n    const popup = getPopup();\n    if (!popup) {\n      return false;\n    }\n    const innerParams = privateProps.innerParams.get(instance);\n    if (!innerParams || hasClass(popup, innerParams.hideClass.popup)) {\n      return false;\n    }\n    removeClass(popup, innerParams.showClass.popup);\n    addClass(popup, innerParams.hideClass.popup);\n    const backdrop = getContainer();\n    removeClass(backdrop, innerParams.showClass.backdrop);\n    addClass(backdrop, innerParams.hideClass.backdrop);\n    handlePopupAnimation(instance, popup, innerParams);\n    return true;\n  };\n\n  /**\n   * @param {any} error\n   */\n  function rejectPromise(error) {\n    const rejectPromise = privateMethods.swalPromiseReject.get(this);\n    handleAwaitingPromise(this);\n    if (rejectPromise) {\n      // Reject Swal promise\n      rejectPromise(error);\n    }\n  }\n\n  /**\n   * @param {SweetAlert} instance\n   */\n  const handleAwaitingPromise = instance => {\n    if (instance.isAwaitingPromise) {\n      delete instance.isAwaitingPromise;\n      // The instance might have been previously partly destroyed, we must resume the destroy process in this case #2335\n      if (!privateProps.innerParams.get(instance)) {\n        instance._destroy();\n      }\n    }\n  };\n\n  /**\n   * @param {any} resolveValue\n   * @returns {SweetAlertResult}\n   */\n  const prepareResolveValue = resolveValue => {\n    // When user calls Swal.close()\n    if (typeof resolveValue === 'undefined') {\n      return {\n        isConfirmed: false,\n        isDenied: false,\n        isDismissed: true\n      };\n    }\n    return Object.assign({\n      isConfirmed: false,\n      isDenied: false,\n      isDismissed: false\n    }, resolveValue);\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {HTMLElement} popup\n   * @param {SweetAlertOptions} innerParams\n   */\n  const handlePopupAnimation = (instance, popup, innerParams) => {\n    var _globalState$eventEmi;\n    const container = getContainer();\n    // If animation is supported, animate\n    const animationIsSupported = hasCssAnimation(popup);\n    if (typeof innerParams.willClose === 'function') {\n      innerParams.willClose(popup);\n    }\n    (_globalState$eventEmi = globalState.eventEmitter) === null || _globalState$eventEmi === void 0 || _globalState$eventEmi.emit('willClose', popup);\n    if (animationIsSupported) {\n      animatePopup(instance, popup, container, innerParams.returnFocus, innerParams.didClose);\n    } else {\n      // Otherwise, remove immediately\n      removePopupAndResetState(instance, container, innerParams.returnFocus, innerParams.didClose);\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {HTMLElement} popup\n   * @param {HTMLElement} container\n   * @param {boolean} returnFocus\n   * @param {Function} didClose\n   */\n  const animatePopup = (instance, popup, container, returnFocus, didClose) => {\n    globalState.swalCloseEventFinishedCallback = removePopupAndResetState.bind(null, instance, container, returnFocus, didClose);\n    /**\n     * @param {AnimationEvent | TransitionEvent} e\n     */\n    const swalCloseAnimationFinished = function (e) {\n      if (e.target === popup) {\n        var _globalState$swalClos;\n        (_globalState$swalClos = globalState.swalCloseEventFinishedCallback) === null || _globalState$swalClos === void 0 || _globalState$swalClos.call(globalState);\n        delete globalState.swalCloseEventFinishedCallback;\n        popup.removeEventListener('animationend', swalCloseAnimationFinished);\n        popup.removeEventListener('transitionend', swalCloseAnimationFinished);\n      }\n    };\n    popup.addEventListener('animationend', swalCloseAnimationFinished);\n    popup.addEventListener('transitionend', swalCloseAnimationFinished);\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {Function} didClose\n   */\n  const triggerDidCloseAndDispose = (instance, didClose) => {\n    setTimeout(() => {\n      var _globalState$eventEmi2;\n      if (typeof didClose === 'function') {\n        didClose.bind(instance.params)();\n      }\n      (_globalState$eventEmi2 = globalState.eventEmitter) === null || _globalState$eventEmi2 === void 0 || _globalState$eventEmi2.emit('didClose');\n      // instance might have been destroyed already\n      if (instance._destroy) {\n        instance._destroy();\n      }\n    });\n  };\n\n  /**\n   * Shows loader (spinner), this is useful with AJAX requests.\n   * By default the loader be shown instead of the \"Confirm\" button.\n   *\n   * @param {HTMLButtonElement | null} [buttonToReplace]\n   */\n  const showLoading = buttonToReplace => {\n    let popup = getPopup();\n    if (!popup) {\n      new Swal();\n    }\n    popup = getPopup();\n    if (!popup) {\n      return;\n    }\n    const loader = getLoader();\n    if (isToast()) {\n      hide(getIcon());\n    } else {\n      replaceButton(popup, buttonToReplace);\n    }\n    show(loader);\n    popup.setAttribute('data-loading', 'true');\n    popup.setAttribute('aria-busy', 'true');\n    popup.focus();\n  };\n\n  /**\n   * @param {HTMLElement} popup\n   * @param {HTMLButtonElement | null} [buttonToReplace]\n   */\n  const replaceButton = (popup, buttonToReplace) => {\n    const actions = getActions();\n    const loader = getLoader();\n    if (!actions || !loader) {\n      return;\n    }\n    if (!buttonToReplace && isVisible$1(getConfirmButton())) {\n      buttonToReplace = getConfirmButton();\n    }\n    show(actions);\n    if (buttonToReplace) {\n      hide(buttonToReplace);\n      loader.setAttribute('data-button-to-replace', buttonToReplace.className);\n      actions.insertBefore(loader, buttonToReplace);\n    }\n    addClass([popup, actions], swalClasses.loading);\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const handleInputOptionsAndValue = (instance, params) => {\n    if (params.input === 'select' || params.input === 'radio') {\n      handleInputOptions(instance, params);\n    } else if (['text', 'email', 'number', 'tel', 'textarea'].some(i => i === params.input) && (hasToPromiseFn(params.inputValue) || isPromise(params.inputValue))) {\n      showLoading(getConfirmButton());\n      handleInputValue(instance, params);\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} innerParams\n   * @returns {SweetAlertInputValue}\n   */\n  const getInputValue = (instance, innerParams) => {\n    const input = instance.getInput();\n    if (!input) {\n      return null;\n    }\n    switch (innerParams.input) {\n      case 'checkbox':\n        return getCheckboxValue(input);\n      case 'radio':\n        return getRadioValue(input);\n      case 'file':\n        return getFileValue(input);\n      default:\n        return innerParams.inputAutoTrim ? input.value.trim() : input.value;\n    }\n  };\n\n  /**\n   * @param {HTMLInputElement} input\n   * @returns {number}\n   */\n  const getCheckboxValue = input => input.checked ? 1 : 0;\n\n  /**\n   * @param {HTMLInputElement} input\n   * @returns {string | null}\n   */\n  const getRadioValue = input => input.checked ? input.value : null;\n\n  /**\n   * @param {HTMLInputElement} input\n   * @returns {FileList | File | null}\n   */\n  const getFileValue = input => input.files && input.files.length ? input.getAttribute('multiple') !== null ? input.files : input.files[0] : null;\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const handleInputOptions = (instance, params) => {\n    const popup = getPopup();\n    if (!popup) {\n      return;\n    }\n    /**\n     * @param {Record<string, any>} inputOptions\n     */\n    const processInputOptions = inputOptions => {\n      if (params.input === 'select') {\n        populateSelectOptions(popup, formatInputOptions(inputOptions), params);\n      } else if (params.input === 'radio') {\n        populateRadioOptions(popup, formatInputOptions(inputOptions), params);\n      }\n    };\n    if (hasToPromiseFn(params.inputOptions) || isPromise(params.inputOptions)) {\n      showLoading(getConfirmButton());\n      asPromise(params.inputOptions).then(inputOptions => {\n        instance.hideLoading();\n        processInputOptions(inputOptions);\n      });\n    } else if (typeof params.inputOptions === 'object') {\n      processInputOptions(params.inputOptions);\n    } else {\n      error(`Unexpected type of inputOptions! Expected object, Map or Promise, got ${typeof params.inputOptions}`);\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const handleInputValue = (instance, params) => {\n    const input = instance.getInput();\n    if (!input) {\n      return;\n    }\n    hide(input);\n    asPromise(params.inputValue).then(inputValue => {\n      input.value = params.input === 'number' ? `${parseFloat(inputValue) || 0}` : `${inputValue}`;\n      show(input);\n      input.focus();\n      instance.hideLoading();\n    }).catch(err => {\n      error(`Error in inputValue promise: ${err}`);\n      input.value = '';\n      show(input);\n      input.focus();\n      instance.hideLoading();\n    });\n  };\n\n  /**\n   * @param {HTMLElement} popup\n   * @param {InputOptionFlattened[]} inputOptions\n   * @param {SweetAlertOptions} params\n   */\n  function populateSelectOptions(popup, inputOptions, params) {\n    const select = getDirectChildByClass(popup, swalClasses.select);\n    if (!select) {\n      return;\n    }\n    /**\n     * @param {HTMLElement} parent\n     * @param {string} optionLabel\n     * @param {string} optionValue\n     */\n    const renderOption = (parent, optionLabel, optionValue) => {\n      const option = document.createElement('option');\n      option.value = optionValue;\n      setInnerHtml(option, optionLabel);\n      option.selected = isSelected(optionValue, params.inputValue);\n      parent.appendChild(option);\n    };\n    inputOptions.forEach(inputOption => {\n      const optionValue = inputOption[0];\n      const optionLabel = inputOption[1];\n      // <optgroup> spec:\n      // https://www.w3.org/TR/html401/interact/forms.html#h-17.6\n      // \"...all OPTGROUP elements must be specified directly within a SELECT element (i.e., groups may not be nested)...\"\n      // check whether this is a <optgroup>\n      if (Array.isArray(optionLabel)) {\n        // if it is an array, then it is an <optgroup>\n        const optgroup = document.createElement('optgroup');\n        optgroup.label = optionValue;\n        optgroup.disabled = false; // not configurable for now\n        select.appendChild(optgroup);\n        optionLabel.forEach(o => renderOption(optgroup, o[1], o[0]));\n      } else {\n        // case of <option>\n        renderOption(select, optionLabel, optionValue);\n      }\n    });\n    select.focus();\n  }\n\n  /**\n   * @param {HTMLElement} popup\n   * @param {InputOptionFlattened[]} inputOptions\n   * @param {SweetAlertOptions} params\n   */\n  function populateRadioOptions(popup, inputOptions, params) {\n    const radio = getDirectChildByClass(popup, swalClasses.radio);\n    if (!radio) {\n      return;\n    }\n    inputOptions.forEach(inputOption => {\n      const radioValue = inputOption[0];\n      const radioLabel = inputOption[1];\n      const radioInput = document.createElement('input');\n      const radioLabelElement = document.createElement('label');\n      radioInput.type = 'radio';\n      radioInput.name = swalClasses.radio;\n      radioInput.value = radioValue;\n      if (isSelected(radioValue, params.inputValue)) {\n        radioInput.checked = true;\n      }\n      const label = document.createElement('span');\n      setInnerHtml(label, radioLabel);\n      label.className = swalClasses.label;\n      radioLabelElement.appendChild(radioInput);\n      radioLabelElement.appendChild(label);\n      radio.appendChild(radioLabelElement);\n    });\n    const radios = radio.querySelectorAll('input');\n    if (radios.length) {\n      radios[0].focus();\n    }\n  }\n\n  /**\n   * Converts `inputOptions` into an array of `[value, label]`s\n   *\n   * @param {Record<string, any>} inputOptions\n   * @typedef {string[]} InputOptionFlattened\n   * @returns {InputOptionFlattened[]}\n   */\n  const formatInputOptions = inputOptions => {\n    /** @type {InputOptionFlattened[]} */\n    const result = [];\n    if (inputOptions instanceof Map) {\n      inputOptions.forEach((value, key) => {\n        let valueFormatted = value;\n        if (typeof valueFormatted === 'object') {\n          // case of <optgroup>\n          valueFormatted = formatInputOptions(valueFormatted);\n        }\n        result.push([key, valueFormatted]);\n      });\n    } else {\n      Object.keys(inputOptions).forEach(key => {\n        let valueFormatted = inputOptions[key];\n        if (typeof valueFormatted === 'object') {\n          // case of <optgroup>\n          valueFormatted = formatInputOptions(valueFormatted);\n        }\n        result.push([key, valueFormatted]);\n      });\n    }\n    return result;\n  };\n\n  /**\n   * @param {string} optionValue\n   * @param {SweetAlertInputValue} inputValue\n   * @returns {boolean}\n   */\n  const isSelected = (optionValue, inputValue) => {\n    return !!inputValue && inputValue.toString() === optionValue.toString();\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   */\n  const handleConfirmButtonClick = instance => {\n    const innerParams = privateProps.innerParams.get(instance);\n    instance.disableButtons();\n    if (innerParams.input) {\n      handleConfirmOrDenyWithInput(instance, 'confirm');\n    } else {\n      confirm(instance, true);\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   */\n  const handleDenyButtonClick = instance => {\n    const innerParams = privateProps.innerParams.get(instance);\n    instance.disableButtons();\n    if (innerParams.returnInputValueOnDeny) {\n      handleConfirmOrDenyWithInput(instance, 'deny');\n    } else {\n      deny(instance, false);\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {Function} dismissWith\n   */\n  const handleCancelButtonClick = (instance, dismissWith) => {\n    instance.disableButtons();\n    dismissWith(DismissReason.cancel);\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {'confirm' | 'deny'} type\n   */\n  const handleConfirmOrDenyWithInput = (instance, type) => {\n    const innerParams = privateProps.innerParams.get(instance);\n    if (!innerParams.input) {\n      error(`The \"input\" parameter is needed to be set when using returnInputValueOn${capitalizeFirstLetter(type)}`);\n      return;\n    }\n    const input = instance.getInput();\n    const inputValue = getInputValue(instance, innerParams);\n    if (innerParams.inputValidator) {\n      handleInputValidator(instance, inputValue, type);\n    } else if (input && !input.checkValidity()) {\n      instance.enableButtons();\n      instance.showValidationMessage(innerParams.validationMessage || input.validationMessage);\n    } else if (type === 'deny') {\n      deny(instance, inputValue);\n    } else {\n      confirm(instance, inputValue);\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertInputValue} inputValue\n   * @param {'confirm' | 'deny'} type\n   */\n  const handleInputValidator = (instance, inputValue, type) => {\n    const innerParams = privateProps.innerParams.get(instance);\n    instance.disableInput();\n    const validationPromise = Promise.resolve().then(() => asPromise(innerParams.inputValidator(inputValue, innerParams.validationMessage)));\n    validationPromise.then(validationMessage => {\n      instance.enableButtons();\n      instance.enableInput();\n      if (validationMessage) {\n        instance.showValidationMessage(validationMessage);\n      } else if (type === 'deny') {\n        deny(instance, inputValue);\n      } else {\n        confirm(instance, inputValue);\n      }\n    });\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {any} value\n   */\n  const deny = (instance, value) => {\n    const innerParams = privateProps.innerParams.get(instance || undefined);\n    if (innerParams.showLoaderOnDeny) {\n      showLoading(getDenyButton());\n    }\n    if (innerParams.preDeny) {\n      instance.isAwaitingPromise = true; // Flagging the instance as awaiting a promise so it's own promise's reject/resolve methods doesn't get destroyed until the result from this preDeny's promise is received\n      const preDenyPromise = Promise.resolve().then(() => asPromise(innerParams.preDeny(value, innerParams.validationMessage)));\n      preDenyPromise.then(preDenyValue => {\n        if (preDenyValue === false) {\n          instance.hideLoading();\n          handleAwaitingPromise(instance);\n        } else {\n          instance.close({\n            isDenied: true,\n            value: typeof preDenyValue === 'undefined' ? value : preDenyValue\n          });\n        }\n      }).catch(error => rejectWith(instance || undefined, error));\n    } else {\n      instance.close({\n        isDenied: true,\n        value\n      });\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {any} value\n   */\n  const succeedWith = (instance, value) => {\n    instance.close({\n      isConfirmed: true,\n      value\n    });\n  };\n\n  /**\n   *\n   * @param {SweetAlert} instance\n   * @param {string} error\n   */\n  const rejectWith = (instance, error) => {\n    instance.rejectPromise(error);\n  };\n\n  /**\n   *\n   * @param {SweetAlert} instance\n   * @param {any} value\n   */\n  const confirm = (instance, value) => {\n    const innerParams = privateProps.innerParams.get(instance || undefined);\n    if (innerParams.showLoaderOnConfirm) {\n      showLoading();\n    }\n    if (innerParams.preConfirm) {\n      instance.resetValidationMessage();\n      instance.isAwaitingPromise = true; // Flagging the instance as awaiting a promise so it's own promise's reject/resolve methods doesn't get destroyed until the result from this preConfirm's promise is received\n      const preConfirmPromise = Promise.resolve().then(() => asPromise(innerParams.preConfirm(value, innerParams.validationMessage)));\n      preConfirmPromise.then(preConfirmValue => {\n        if (isVisible$1(getValidationMessage()) || preConfirmValue === false) {\n          instance.hideLoading();\n          handleAwaitingPromise(instance);\n        } else {\n          succeedWith(instance, typeof preConfirmValue === 'undefined' ? value : preConfirmValue);\n        }\n      }).catch(error => rejectWith(instance || undefined, error));\n    } else {\n      succeedWith(instance, value);\n    }\n  };\n\n  /**\n   * Hides loader and shows back the button which was hidden by .showLoading()\n   */\n  function hideLoading() {\n    // do nothing if popup is closed\n    const innerParams = privateProps.innerParams.get(this);\n    if (!innerParams) {\n      return;\n    }\n    const domCache = privateProps.domCache.get(this);\n    hide(domCache.loader);\n    if (isToast()) {\n      if (innerParams.icon) {\n        show(getIcon());\n      }\n    } else {\n      showRelatedButton(domCache);\n    }\n    removeClass([domCache.popup, domCache.actions], swalClasses.loading);\n    domCache.popup.removeAttribute('aria-busy');\n    domCache.popup.removeAttribute('data-loading');\n    domCache.confirmButton.disabled = false;\n    domCache.denyButton.disabled = false;\n    domCache.cancelButton.disabled = false;\n  }\n  const showRelatedButton = domCache => {\n    const buttonToReplace = domCache.popup.getElementsByClassName(domCache.loader.getAttribute('data-button-to-replace'));\n    if (buttonToReplace.length) {\n      show(buttonToReplace[0], 'inline-block');\n    } else if (allButtonsAreHidden()) {\n      hide(domCache.actions);\n    }\n  };\n\n  /**\n   * Gets the input DOM node, this method works with input parameter.\n   *\n   * @returns {HTMLInputElement | null}\n   */\n  function getInput() {\n    const innerParams = privateProps.innerParams.get(this);\n    const domCache = privateProps.domCache.get(this);\n    if (!domCache) {\n      return null;\n    }\n    return getInput$1(domCache.popup, innerParams.input);\n  }\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {string[]} buttons\n   * @param {boolean} disabled\n   */\n  function setButtonsDisabled(instance, buttons, disabled) {\n    const domCache = privateProps.domCache.get(instance);\n    buttons.forEach(button => {\n      domCache[button].disabled = disabled;\n    });\n  }\n\n  /**\n   * @param {HTMLInputElement | null} input\n   * @param {boolean} disabled\n   */\n  function setInputDisabled(input, disabled) {\n    const popup = getPopup();\n    if (!popup || !input) {\n      return;\n    }\n    if (input.type === 'radio') {\n      /** @type {NodeListOf<HTMLInputElement>} */\n      const radios = popup.querySelectorAll(`[name=\"${swalClasses.radio}\"]`);\n      for (let i = 0; i < radios.length; i++) {\n        radios[i].disabled = disabled;\n      }\n    } else {\n      input.disabled = disabled;\n    }\n  }\n\n  /**\n   * Enable all the buttons\n   * @this {SweetAlert}\n   */\n  function enableButtons() {\n    setButtonsDisabled(this, ['confirmButton', 'denyButton', 'cancelButton'], false);\n  }\n\n  /**\n   * Disable all the buttons\n   * @this {SweetAlert}\n   */\n  function disableButtons() {\n    setButtonsDisabled(this, ['confirmButton', 'denyButton', 'cancelButton'], true);\n  }\n\n  /**\n   * Enable the input field\n   * @this {SweetAlert}\n   */\n  function enableInput() {\n    setInputDisabled(this.getInput(), false);\n  }\n\n  /**\n   * Disable the input field\n   * @this {SweetAlert}\n   */\n  function disableInput() {\n    setInputDisabled(this.getInput(), true);\n  }\n\n  /**\n   * Show block with validation message\n   *\n   * @param {string} error\n   * @this {SweetAlert}\n   */\n  function showValidationMessage(error) {\n    const domCache = privateProps.domCache.get(this);\n    const params = privateProps.innerParams.get(this);\n    setInnerHtml(domCache.validationMessage, error);\n    domCache.validationMessage.className = swalClasses['validation-message'];\n    if (params.customClass && params.customClass.validationMessage) {\n      addClass(domCache.validationMessage, params.customClass.validationMessage);\n    }\n    show(domCache.validationMessage);\n    const input = this.getInput();\n    if (input) {\n      input.setAttribute('aria-invalid', 'true');\n      input.setAttribute('aria-describedby', swalClasses['validation-message']);\n      focusInput(input);\n      addClass(input, swalClasses.inputerror);\n    }\n  }\n\n  /**\n   * Hide block with validation message\n   *\n   * @this {SweetAlert}\n   */\n  function resetValidationMessage() {\n    const domCache = privateProps.domCache.get(this);\n    if (domCache.validationMessage) {\n      hide(domCache.validationMessage);\n    }\n    const input = this.getInput();\n    if (input) {\n      input.removeAttribute('aria-invalid');\n      input.removeAttribute('aria-describedby');\n      removeClass(input, swalClasses.inputerror);\n    }\n  }\n\n  const defaultParams = {\n    title: '',\n    titleText: '',\n    text: '',\n    html: '',\n    footer: '',\n    icon: undefined,\n    iconColor: undefined,\n    iconHtml: undefined,\n    template: undefined,\n    toast: false,\n    draggable: false,\n    animation: true,\n    theme: 'light',\n    showClass: {\n      popup: 'swal2-show',\n      backdrop: 'swal2-backdrop-show',\n      icon: 'swal2-icon-show'\n    },\n    hideClass: {\n      popup: 'swal2-hide',\n      backdrop: 'swal2-backdrop-hide',\n      icon: 'swal2-icon-hide'\n    },\n    customClass: {},\n    target: 'body',\n    color: undefined,\n    backdrop: true,\n    heightAuto: true,\n    allowOutsideClick: true,\n    allowEscapeKey: true,\n    allowEnterKey: true,\n    stopKeydownPropagation: true,\n    keydownListenerCapture: false,\n    showConfirmButton: true,\n    showDenyButton: false,\n    showCancelButton: false,\n    preConfirm: undefined,\n    preDeny: undefined,\n    confirmButtonText: 'OK',\n    confirmButtonAriaLabel: '',\n    confirmButtonColor: undefined,\n    denyButtonText: 'No',\n    denyButtonAriaLabel: '',\n    denyButtonColor: undefined,\n    cancelButtonText: 'Cancel',\n    cancelButtonAriaLabel: '',\n    cancelButtonColor: undefined,\n    buttonsStyling: true,\n    reverseButtons: false,\n    focusConfirm: true,\n    focusDeny: false,\n    focusCancel: false,\n    returnFocus: true,\n    showCloseButton: false,\n    closeButtonHtml: '&times;',\n    closeButtonAriaLabel: 'Close this dialog',\n    loaderHtml: '',\n    showLoaderOnConfirm: false,\n    showLoaderOnDeny: false,\n    imageUrl: undefined,\n    imageWidth: undefined,\n    imageHeight: undefined,\n    imageAlt: '',\n    timer: undefined,\n    timerProgressBar: false,\n    width: undefined,\n    padding: undefined,\n    background: undefined,\n    input: undefined,\n    inputPlaceholder: '',\n    inputLabel: '',\n    inputValue: '',\n    inputOptions: {},\n    inputAutoFocus: true,\n    inputAutoTrim: true,\n    inputAttributes: {},\n    inputValidator: undefined,\n    returnInputValueOnDeny: false,\n    validationMessage: undefined,\n    grow: false,\n    position: 'center',\n    progressSteps: [],\n    currentProgressStep: undefined,\n    progressStepsDistance: undefined,\n    willOpen: undefined,\n    didOpen: undefined,\n    didRender: undefined,\n    willClose: undefined,\n    didClose: undefined,\n    didDestroy: undefined,\n    scrollbarPadding: true\n  };\n  const updatableParams = ['allowEscapeKey', 'allowOutsideClick', 'background', 'buttonsStyling', 'cancelButtonAriaLabel', 'cancelButtonColor', 'cancelButtonText', 'closeButtonAriaLabel', 'closeButtonHtml', 'color', 'confirmButtonAriaLabel', 'confirmButtonColor', 'confirmButtonText', 'currentProgressStep', 'customClass', 'denyButtonAriaLabel', 'denyButtonColor', 'denyButtonText', 'didClose', 'didDestroy', 'draggable', 'footer', 'hideClass', 'html', 'icon', 'iconColor', 'iconHtml', 'imageAlt', 'imageHeight', 'imageUrl', 'imageWidth', 'preConfirm', 'preDeny', 'progressSteps', 'returnFocus', 'reverseButtons', 'showCancelButton', 'showCloseButton', 'showConfirmButton', 'showDenyButton', 'text', 'title', 'titleText', 'theme', 'willClose'];\n\n  /** @type {Record<string, string | undefined>} */\n  const deprecatedParams = {\n    allowEnterKey: undefined\n  };\n  const toastIncompatibleParams = ['allowOutsideClick', 'allowEnterKey', 'backdrop', 'draggable', 'focusConfirm', 'focusDeny', 'focusCancel', 'returnFocus', 'heightAuto', 'keydownListenerCapture'];\n\n  /**\n   * Is valid parameter\n   *\n   * @param {string} paramName\n   * @returns {boolean}\n   */\n  const isValidParameter = paramName => {\n    return Object.prototype.hasOwnProperty.call(defaultParams, paramName);\n  };\n\n  /**\n   * Is valid parameter for Swal.update() method\n   *\n   * @param {string} paramName\n   * @returns {boolean}\n   */\n  const isUpdatableParameter = paramName => {\n    return updatableParams.indexOf(paramName) !== -1;\n  };\n\n  /**\n   * Is deprecated parameter\n   *\n   * @param {string} paramName\n   * @returns {string | undefined}\n   */\n  const isDeprecatedParameter = paramName => {\n    return deprecatedParams[paramName];\n  };\n\n  /**\n   * @param {string} param\n   */\n  const checkIfParamIsValid = param => {\n    if (!isValidParameter(param)) {\n      warn(`Unknown parameter \"${param}\"`);\n    }\n  };\n\n  /**\n   * @param {string} param\n   */\n  const checkIfToastParamIsValid = param => {\n    if (toastIncompatibleParams.includes(param)) {\n      warn(`The parameter \"${param}\" is incompatible with toasts`);\n    }\n  };\n\n  /**\n   * @param {string} param\n   */\n  const checkIfParamIsDeprecated = param => {\n    const isDeprecated = isDeprecatedParameter(param);\n    if (isDeprecated) {\n      warnAboutDeprecation(param, isDeprecated);\n    }\n  };\n\n  /**\n   * Show relevant warnings for given params\n   *\n   * @param {SweetAlertOptions} params\n   */\n  const showWarningsForParams = params => {\n    if (params.backdrop === false && params.allowOutsideClick) {\n      warn('\"allowOutsideClick\" parameter requires `backdrop` parameter to be set to `true`');\n    }\n    if (params.theme && !['light', 'dark', 'auto', 'borderless'].includes(params.theme)) {\n      warn(`Invalid theme \"${params.theme}\". Expected \"light\", \"dark\", \"auto\", or \"borderless\"`);\n    }\n    for (const param in params) {\n      checkIfParamIsValid(param);\n      if (params.toast) {\n        checkIfToastParamIsValid(param);\n      }\n      checkIfParamIsDeprecated(param);\n    }\n  };\n\n  /**\n   * Updates popup parameters.\n   *\n   * @param {SweetAlertOptions} params\n   */\n  function update(params) {\n    const container = getContainer();\n    const popup = getPopup();\n    const innerParams = privateProps.innerParams.get(this);\n    if (!popup || hasClass(popup, innerParams.hideClass.popup)) {\n      warn(`You're trying to update the closed or closing popup, that won't work. Use the update() method in preConfirm parameter or show a new popup.`);\n      return;\n    }\n    const validUpdatableParams = filterValidParams(params);\n    const updatedParams = Object.assign({}, innerParams, validUpdatableParams);\n    showWarningsForParams(updatedParams);\n    container.dataset['swal2Theme'] = updatedParams.theme;\n    render(this, updatedParams);\n    privateProps.innerParams.set(this, updatedParams);\n    Object.defineProperties(this, {\n      params: {\n        value: Object.assign({}, this.params, params),\n        writable: false,\n        enumerable: true\n      }\n    });\n  }\n\n  /**\n   * @param {SweetAlertOptions} params\n   * @returns {SweetAlertOptions}\n   */\n  const filterValidParams = params => {\n    const validUpdatableParams = {};\n    Object.keys(params).forEach(param => {\n      if (isUpdatableParameter(param)) {\n        validUpdatableParams[param] = params[param];\n      } else {\n        warn(`Invalid parameter to update: ${param}`);\n      }\n    });\n    return validUpdatableParams;\n  };\n\n  /**\n   * Dispose the current SweetAlert2 instance\n   */\n  function _destroy() {\n    const domCache = privateProps.domCache.get(this);\n    const innerParams = privateProps.innerParams.get(this);\n    if (!innerParams) {\n      disposeWeakMaps(this); // The WeakMaps might have been partly destroyed, we must recall it to dispose any remaining WeakMaps #2335\n      return; // This instance has already been destroyed\n    }\n\n    // Check if there is another Swal closing\n    if (domCache.popup && globalState.swalCloseEventFinishedCallback) {\n      globalState.swalCloseEventFinishedCallback();\n      delete globalState.swalCloseEventFinishedCallback;\n    }\n    if (typeof innerParams.didDestroy === 'function') {\n      innerParams.didDestroy();\n    }\n    globalState.eventEmitter.emit('didDestroy');\n    disposeSwal(this);\n  }\n\n  /**\n   * @param {SweetAlert} instance\n   */\n  const disposeSwal = instance => {\n    disposeWeakMaps(instance);\n    // Unset this.params so GC will dispose it (#1569)\n    delete instance.params;\n    // Unset globalState props so GC will dispose globalState (#1569)\n    delete globalState.keydownHandler;\n    delete globalState.keydownTarget;\n    // Unset currentInstance\n    delete globalState.currentInstance;\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   */\n  const disposeWeakMaps = instance => {\n    // If the current instance is awaiting a promise result, we keep the privateMethods to call them once the promise result is retrieved #2335\n    if (instance.isAwaitingPromise) {\n      unsetWeakMaps(privateProps, instance);\n      instance.isAwaitingPromise = true;\n    } else {\n      unsetWeakMaps(privateMethods, instance);\n      unsetWeakMaps(privateProps, instance);\n      delete instance.isAwaitingPromise;\n      // Unset instance methods\n      delete instance.disableButtons;\n      delete instance.enableButtons;\n      delete instance.getInput;\n      delete instance.disableInput;\n      delete instance.enableInput;\n      delete instance.hideLoading;\n      delete instance.disableLoading;\n      delete instance.showValidationMessage;\n      delete instance.resetValidationMessage;\n      delete instance.close;\n      delete instance.closePopup;\n      delete instance.closeModal;\n      delete instance.closeToast;\n      delete instance.rejectPromise;\n      delete instance.update;\n      delete instance._destroy;\n    }\n  };\n\n  /**\n   * @param {object} obj\n   * @param {SweetAlert} instance\n   */\n  const unsetWeakMaps = (obj, instance) => {\n    for (const i in obj) {\n      obj[i].delete(instance);\n    }\n  };\n\n  var instanceMethods = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    _destroy: _destroy,\n    close: close,\n    closeModal: close,\n    closePopup: close,\n    closeToast: close,\n    disableButtons: disableButtons,\n    disableInput: disableInput,\n    disableLoading: hideLoading,\n    enableButtons: enableButtons,\n    enableInput: enableInput,\n    getInput: getInput,\n    handleAwaitingPromise: handleAwaitingPromise,\n    hideLoading: hideLoading,\n    rejectPromise: rejectPromise,\n    resetValidationMessage: resetValidationMessage,\n    showValidationMessage: showValidationMessage,\n    update: update\n  });\n\n  /**\n   * @param {SweetAlertOptions} innerParams\n   * @param {DomCache} domCache\n   * @param {Function} dismissWith\n   */\n  const handlePopupClick = (innerParams, domCache, dismissWith) => {\n    if (innerParams.toast) {\n      handleToastClick(innerParams, domCache, dismissWith);\n    } else {\n      // Ignore click events that had mousedown on the popup but mouseup on the container\n      // This can happen when the user drags a slider\n      handleModalMousedown(domCache);\n\n      // Ignore click events that had mousedown on the container but mouseup on the popup\n      handleContainerMousedown(domCache);\n      handleModalClick(innerParams, domCache, dismissWith);\n    }\n  };\n\n  /**\n   * @param {SweetAlertOptions} innerParams\n   * @param {DomCache} domCache\n   * @param {Function} dismissWith\n   */\n  const handleToastClick = (innerParams, domCache, dismissWith) => {\n    // Closing toast by internal click\n    domCache.popup.onclick = () => {\n      if (innerParams && (isAnyButtonShown(innerParams) || innerParams.timer || innerParams.input)) {\n        return;\n      }\n      dismissWith(DismissReason.close);\n    };\n  };\n\n  /**\n   * @param {SweetAlertOptions} innerParams\n   * @returns {boolean}\n   */\n  const isAnyButtonShown = innerParams => {\n    return !!(innerParams.showConfirmButton || innerParams.showDenyButton || innerParams.showCancelButton || innerParams.showCloseButton);\n  };\n  let ignoreOutsideClick = false;\n\n  /**\n   * @param {DomCache} domCache\n   */\n  const handleModalMousedown = domCache => {\n    domCache.popup.onmousedown = () => {\n      domCache.container.onmouseup = function (e) {\n        domCache.container.onmouseup = () => {};\n        // We only check if the mouseup target is the container because usually it doesn't\n        // have any other direct children aside of the popup\n        if (e.target === domCache.container) {\n          ignoreOutsideClick = true;\n        }\n      };\n    };\n  };\n\n  /**\n   * @param {DomCache} domCache\n   */\n  const handleContainerMousedown = domCache => {\n    domCache.container.onmousedown = e => {\n      // prevent the modal text from being selected on double click on the container (allowOutsideClick: false)\n      if (e.target === domCache.container) {\n        e.preventDefault();\n      }\n      domCache.popup.onmouseup = function (e) {\n        domCache.popup.onmouseup = () => {};\n        // We also need to check if the mouseup target is a child of the popup\n        if (e.target === domCache.popup || e.target instanceof HTMLElement && domCache.popup.contains(e.target)) {\n          ignoreOutsideClick = true;\n        }\n      };\n    };\n  };\n\n  /**\n   * @param {SweetAlertOptions} innerParams\n   * @param {DomCache} domCache\n   * @param {Function} dismissWith\n   */\n  const handleModalClick = (innerParams, domCache, dismissWith) => {\n    domCache.container.onclick = e => {\n      if (ignoreOutsideClick) {\n        ignoreOutsideClick = false;\n        return;\n      }\n      if (e.target === domCache.container && callIfFunction(innerParams.allowOutsideClick)) {\n        dismissWith(DismissReason.backdrop);\n      }\n    };\n  };\n\n  const isJqueryElement = elem => typeof elem === 'object' && elem.jquery;\n  const isElement = elem => elem instanceof Element || isJqueryElement(elem);\n  const argsToParams = args => {\n    const params = {};\n    if (typeof args[0] === 'object' && !isElement(args[0])) {\n      Object.assign(params, args[0]);\n    } else {\n      ['title', 'html', 'icon'].forEach((name, index) => {\n        const arg = args[index];\n        if (typeof arg === 'string' || isElement(arg)) {\n          params[name] = arg;\n        } else if (arg !== undefined) {\n          error(`Unexpected type of ${name}! Expected \"string\" or \"Element\", got ${typeof arg}`);\n        }\n      });\n    }\n    return params;\n  };\n\n  /**\n   * Main method to create a new SweetAlert2 popup\n   *\n   * @param  {...SweetAlertOptions} args\n   * @returns {Promise<SweetAlertResult>}\n   */\n  function fire() {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return new this(...args);\n  }\n\n  /**\n   * Returns an extended version of `Swal` containing `params` as defaults.\n   * Useful for reusing Swal configuration.\n   *\n   * For example:\n   *\n   * Before:\n   * const textPromptOptions = { input: 'text', showCancelButton: true }\n   * const {value: firstName} = await Swal.fire({ ...textPromptOptions, title: 'What is your first name?' })\n   * const {value: lastName} = await Swal.fire({ ...textPromptOptions, title: 'What is your last name?' })\n   *\n   * After:\n   * const TextPrompt = Swal.mixin({ input: 'text', showCancelButton: true })\n   * const {value: firstName} = await TextPrompt('What is your first name?')\n   * const {value: lastName} = await TextPrompt('What is your last name?')\n   *\n   * @param {SweetAlertOptions} mixinParams\n   * @returns {SweetAlert}\n   */\n  function mixin(mixinParams) {\n    class MixinSwal extends this {\n      _main(params, priorityMixinParams) {\n        return super._main(params, Object.assign({}, mixinParams, priorityMixinParams));\n      }\n    }\n    // @ts-ignore\n    return MixinSwal;\n  }\n\n  /**\n   * If `timer` parameter is set, returns number of milliseconds of timer remained.\n   * Otherwise, returns undefined.\n   *\n   * @returns {number | undefined}\n   */\n  const getTimerLeft = () => {\n    return globalState.timeout && globalState.timeout.getTimerLeft();\n  };\n\n  /**\n   * Stop timer. Returns number of milliseconds of timer remained.\n   * If `timer` parameter isn't set, returns undefined.\n   *\n   * @returns {number | undefined}\n   */\n  const stopTimer = () => {\n    if (globalState.timeout) {\n      stopTimerProgressBar();\n      return globalState.timeout.stop();\n    }\n  };\n\n  /**\n   * Resume timer. Returns number of milliseconds of timer remained.\n   * If `timer` parameter isn't set, returns undefined.\n   *\n   * @returns {number | undefined}\n   */\n  const resumeTimer = () => {\n    if (globalState.timeout) {\n      const remaining = globalState.timeout.start();\n      animateTimerProgressBar(remaining);\n      return remaining;\n    }\n  };\n\n  /**\n   * Resume timer. Returns number of milliseconds of timer remained.\n   * If `timer` parameter isn't set, returns undefined.\n   *\n   * @returns {number | undefined}\n   */\n  const toggleTimer = () => {\n    const timer = globalState.timeout;\n    return timer && (timer.running ? stopTimer() : resumeTimer());\n  };\n\n  /**\n   * Increase timer. Returns number of milliseconds of an updated timer.\n   * If `timer` parameter isn't set, returns undefined.\n   *\n   * @param {number} ms\n   * @returns {number | undefined}\n   */\n  const increaseTimer = ms => {\n    if (globalState.timeout) {\n      const remaining = globalState.timeout.increase(ms);\n      animateTimerProgressBar(remaining, true);\n      return remaining;\n    }\n  };\n\n  /**\n   * Check if timer is running. Returns true if timer is running\n   * or false if timer is paused or stopped.\n   * If `timer` parameter isn't set, returns undefined\n   *\n   * @returns {boolean}\n   */\n  const isTimerRunning = () => {\n    return !!(globalState.timeout && globalState.timeout.isRunning());\n  };\n\n  let bodyClickListenerAdded = false;\n  const clickHandlers = {};\n\n  /**\n   * @param {string} attr\n   */\n  function bindClickHandler() {\n    let attr = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'data-swal-template';\n    clickHandlers[attr] = this;\n    if (!bodyClickListenerAdded) {\n      document.body.addEventListener('click', bodyClickListener);\n      bodyClickListenerAdded = true;\n    }\n  }\n  const bodyClickListener = event => {\n    for (let el = event.target; el && el !== document; el = el.parentNode) {\n      for (const attr in clickHandlers) {\n        const template = el.getAttribute(attr);\n        if (template) {\n          clickHandlers[attr].fire({\n            template\n          });\n          return;\n        }\n      }\n    }\n  };\n\n  // Source: https://gist.github.com/mudge/5830382?permalink_comment_id=2691957#gistcomment-2691957\n\n  class EventEmitter {\n    constructor() {\n      /** @type {Events} */\n      this.events = {};\n    }\n\n    /**\n     * @param {string} eventName\n     * @returns {EventHandlers}\n     */\n    _getHandlersByEventName(eventName) {\n      if (typeof this.events[eventName] === 'undefined') {\n        // not Set because we need to keep the FIFO order\n        // https://github.com/sweetalert2/sweetalert2/pull/2763#discussion_r1748990334\n        this.events[eventName] = [];\n      }\n      return this.events[eventName];\n    }\n\n    /**\n     * @param {string} eventName\n     * @param {EventHandler} eventHandler\n     */\n    on(eventName, eventHandler) {\n      const currentHandlers = this._getHandlersByEventName(eventName);\n      if (!currentHandlers.includes(eventHandler)) {\n        currentHandlers.push(eventHandler);\n      }\n    }\n\n    /**\n     * @param {string} eventName\n     * @param {EventHandler} eventHandler\n     */\n    once(eventName, eventHandler) {\n      var _this = this;\n      /**\n       * @param {Array} args\n       */\n      const onceFn = function () {\n        _this.removeListener(eventName, onceFn);\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n        eventHandler.apply(_this, args);\n      };\n      this.on(eventName, onceFn);\n    }\n\n    /**\n     * @param {string} eventName\n     * @param {Array} args\n     */\n    emit(eventName) {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n      this._getHandlersByEventName(eventName).forEach(\n      /**\n       * @param {EventHandler} eventHandler\n       */\n      eventHandler => {\n        try {\n          eventHandler.apply(this, args);\n        } catch (error) {\n          console.error(error);\n        }\n      });\n    }\n\n    /**\n     * @param {string} eventName\n     * @param {EventHandler} eventHandler\n     */\n    removeListener(eventName, eventHandler) {\n      const currentHandlers = this._getHandlersByEventName(eventName);\n      const index = currentHandlers.indexOf(eventHandler);\n      if (index > -1) {\n        currentHandlers.splice(index, 1);\n      }\n    }\n\n    /**\n     * @param {string} eventName\n     */\n    removeAllListeners(eventName) {\n      if (this.events[eventName] !== undefined) {\n        // https://github.com/sweetalert2/sweetalert2/pull/2763#discussion_r1749239222\n        this.events[eventName].length = 0;\n      }\n    }\n    reset() {\n      this.events = {};\n    }\n  }\n\n  globalState.eventEmitter = new EventEmitter();\n\n  /**\n   * @param {string} eventName\n   * @param {EventHandler} eventHandler\n   */\n  const on = (eventName, eventHandler) => {\n    globalState.eventEmitter.on(eventName, eventHandler);\n  };\n\n  /**\n   * @param {string} eventName\n   * @param {EventHandler} eventHandler\n   */\n  const once = (eventName, eventHandler) => {\n    globalState.eventEmitter.once(eventName, eventHandler);\n  };\n\n  /**\n   * @param {string} [eventName]\n   * @param {EventHandler} [eventHandler]\n   */\n  const off = (eventName, eventHandler) => {\n    // Remove all handlers for all events\n    if (!eventName) {\n      globalState.eventEmitter.reset();\n      return;\n    }\n    if (eventHandler) {\n      // Remove a specific handler\n      globalState.eventEmitter.removeListener(eventName, eventHandler);\n    } else {\n      // Remove all handlers for a specific event\n      globalState.eventEmitter.removeAllListeners(eventName);\n    }\n  };\n\n  var staticMethods = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    argsToParams: argsToParams,\n    bindClickHandler: bindClickHandler,\n    clickCancel: clickCancel,\n    clickConfirm: clickConfirm,\n    clickDeny: clickDeny,\n    enableLoading: showLoading,\n    fire: fire,\n    getActions: getActions,\n    getCancelButton: getCancelButton,\n    getCloseButton: getCloseButton,\n    getConfirmButton: getConfirmButton,\n    getContainer: getContainer,\n    getDenyButton: getDenyButton,\n    getFocusableElements: getFocusableElements,\n    getFooter: getFooter,\n    getHtmlContainer: getHtmlContainer,\n    getIcon: getIcon,\n    getIconContent: getIconContent,\n    getImage: getImage,\n    getInputLabel: getInputLabel,\n    getLoader: getLoader,\n    getPopup: getPopup,\n    getProgressSteps: getProgressSteps,\n    getTimerLeft: getTimerLeft,\n    getTimerProgressBar: getTimerProgressBar,\n    getTitle: getTitle,\n    getValidationMessage: getValidationMessage,\n    increaseTimer: increaseTimer,\n    isDeprecatedParameter: isDeprecatedParameter,\n    isLoading: isLoading,\n    isTimerRunning: isTimerRunning,\n    isUpdatableParameter: isUpdatableParameter,\n    isValidParameter: isValidParameter,\n    isVisible: isVisible,\n    mixin: mixin,\n    off: off,\n    on: on,\n    once: once,\n    resumeTimer: resumeTimer,\n    showLoading: showLoading,\n    stopTimer: stopTimer,\n    toggleTimer: toggleTimer\n  });\n\n  class Timer {\n    /**\n     * @param {Function} callback\n     * @param {number} delay\n     */\n    constructor(callback, delay) {\n      this.callback = callback;\n      this.remaining = delay;\n      this.running = false;\n      this.start();\n    }\n\n    /**\n     * @returns {number}\n     */\n    start() {\n      if (!this.running) {\n        this.running = true;\n        this.started = new Date();\n        this.id = setTimeout(this.callback, this.remaining);\n      }\n      return this.remaining;\n    }\n\n    /**\n     * @returns {number}\n     */\n    stop() {\n      if (this.started && this.running) {\n        this.running = false;\n        clearTimeout(this.id);\n        this.remaining -= new Date().getTime() - this.started.getTime();\n      }\n      return this.remaining;\n    }\n\n    /**\n     * @param {number} n\n     * @returns {number}\n     */\n    increase(n) {\n      const running = this.running;\n      if (running) {\n        this.stop();\n      }\n      this.remaining += n;\n      if (running) {\n        this.start();\n      }\n      return this.remaining;\n    }\n\n    /**\n     * @returns {number}\n     */\n    getTimerLeft() {\n      if (this.running) {\n        this.stop();\n        this.start();\n      }\n      return this.remaining;\n    }\n\n    /**\n     * @returns {boolean}\n     */\n    isRunning() {\n      return this.running;\n    }\n  }\n\n  const swalStringParams = ['swal-title', 'swal-html', 'swal-footer'];\n\n  /**\n   * @param {SweetAlertOptions} params\n   * @returns {SweetAlertOptions}\n   */\n  const getTemplateParams = params => {\n    const template = typeof params.template === 'string' ? (/** @type {HTMLTemplateElement} */document.querySelector(params.template)) : params.template;\n    if (!template) {\n      return {};\n    }\n    /** @type {DocumentFragment} */\n    const templateContent = template.content;\n    showWarningsForElements(templateContent);\n    const result = Object.assign(getSwalParams(templateContent), getSwalFunctionParams(templateContent), getSwalButtons(templateContent), getSwalImage(templateContent), getSwalIcon(templateContent), getSwalInput(templateContent), getSwalStringParams(templateContent, swalStringParams));\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   * @returns {Record<string, any>}\n   */\n  const getSwalParams = templateContent => {\n    /** @type {Record<string, any>} */\n    const result = {};\n    /** @type {HTMLElement[]} */\n    const swalParams = Array.from(templateContent.querySelectorAll('swal-param'));\n    swalParams.forEach(param => {\n      showWarningsForAttributes(param, ['name', 'value']);\n      const paramName = /** @type {keyof SweetAlertOptions} */param.getAttribute('name');\n      const value = param.getAttribute('value');\n      if (!paramName || !value) {\n        return;\n      }\n      if (typeof defaultParams[paramName] === 'boolean') {\n        result[paramName] = value !== 'false';\n      } else if (typeof defaultParams[paramName] === 'object') {\n        result[paramName] = JSON.parse(value);\n      } else {\n        result[paramName] = value;\n      }\n    });\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   * @returns {Record<string, any>}\n   */\n  const getSwalFunctionParams = templateContent => {\n    /** @type {Record<string, any>} */\n    const result = {};\n    /** @type {HTMLElement[]} */\n    const swalFunctions = Array.from(templateContent.querySelectorAll('swal-function-param'));\n    swalFunctions.forEach(param => {\n      const paramName = /** @type {keyof SweetAlertOptions} */param.getAttribute('name');\n      const value = param.getAttribute('value');\n      if (!paramName || !value) {\n        return;\n      }\n      result[paramName] = new Function(`return ${value}`)();\n    });\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   * @returns {Record<string, any>}\n   */\n  const getSwalButtons = templateContent => {\n    /** @type {Record<string, any>} */\n    const result = {};\n    /** @type {HTMLElement[]} */\n    const swalButtons = Array.from(templateContent.querySelectorAll('swal-button'));\n    swalButtons.forEach(button => {\n      showWarningsForAttributes(button, ['type', 'color', 'aria-label']);\n      const type = button.getAttribute('type');\n      if (!type || !['confirm', 'cancel', 'deny'].includes(type)) {\n        return;\n      }\n      result[`${type}ButtonText`] = button.innerHTML;\n      result[`show${capitalizeFirstLetter(type)}Button`] = true;\n      if (button.hasAttribute('color')) {\n        result[`${type}ButtonColor`] = button.getAttribute('color');\n      }\n      if (button.hasAttribute('aria-label')) {\n        result[`${type}ButtonAriaLabel`] = button.getAttribute('aria-label');\n      }\n    });\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   * @returns {Pick<SweetAlertOptions, 'imageUrl' | 'imageWidth' | 'imageHeight' | 'imageAlt'>}\n   */\n  const getSwalImage = templateContent => {\n    const result = {};\n    /** @type {HTMLElement | null} */\n    const image = templateContent.querySelector('swal-image');\n    if (image) {\n      showWarningsForAttributes(image, ['src', 'width', 'height', 'alt']);\n      if (image.hasAttribute('src')) {\n        result.imageUrl = image.getAttribute('src') || undefined;\n      }\n      if (image.hasAttribute('width')) {\n        result.imageWidth = image.getAttribute('width') || undefined;\n      }\n      if (image.hasAttribute('height')) {\n        result.imageHeight = image.getAttribute('height') || undefined;\n      }\n      if (image.hasAttribute('alt')) {\n        result.imageAlt = image.getAttribute('alt') || undefined;\n      }\n    }\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   * @returns {Record<string, any>}\n   */\n  const getSwalIcon = templateContent => {\n    const result = {};\n    /** @type {HTMLElement | null} */\n    const icon = templateContent.querySelector('swal-icon');\n    if (icon) {\n      showWarningsForAttributes(icon, ['type', 'color']);\n      if (icon.hasAttribute('type')) {\n        result.icon = icon.getAttribute('type');\n      }\n      if (icon.hasAttribute('color')) {\n        result.iconColor = icon.getAttribute('color');\n      }\n      result.iconHtml = icon.innerHTML;\n    }\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   * @returns {Record<string, any>}\n   */\n  const getSwalInput = templateContent => {\n    /** @type {Record<string, any>} */\n    const result = {};\n    /** @type {HTMLElement | null} */\n    const input = templateContent.querySelector('swal-input');\n    if (input) {\n      showWarningsForAttributes(input, ['type', 'label', 'placeholder', 'value']);\n      result.input = input.getAttribute('type') || 'text';\n      if (input.hasAttribute('label')) {\n        result.inputLabel = input.getAttribute('label');\n      }\n      if (input.hasAttribute('placeholder')) {\n        result.inputPlaceholder = input.getAttribute('placeholder');\n      }\n      if (input.hasAttribute('value')) {\n        result.inputValue = input.getAttribute('value');\n      }\n    }\n    /** @type {HTMLElement[]} */\n    const inputOptions = Array.from(templateContent.querySelectorAll('swal-input-option'));\n    if (inputOptions.length) {\n      result.inputOptions = {};\n      inputOptions.forEach(option => {\n        showWarningsForAttributes(option, ['value']);\n        const optionValue = option.getAttribute('value');\n        if (!optionValue) {\n          return;\n        }\n        const optionName = option.innerHTML;\n        result.inputOptions[optionValue] = optionName;\n      });\n    }\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   * @param {string[]} paramNames\n   * @returns {Record<string, any>}\n   */\n  const getSwalStringParams = (templateContent, paramNames) => {\n    /** @type {Record<string, any>} */\n    const result = {};\n    for (const i in paramNames) {\n      const paramName = paramNames[i];\n      /** @type {HTMLElement | null} */\n      const tag = templateContent.querySelector(paramName);\n      if (tag) {\n        showWarningsForAttributes(tag, []);\n        result[paramName.replace(/^swal-/, '')] = tag.innerHTML.trim();\n      }\n    }\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   */\n  const showWarningsForElements = templateContent => {\n    const allowedElements = swalStringParams.concat(['swal-param', 'swal-function-param', 'swal-button', 'swal-image', 'swal-icon', 'swal-input', 'swal-input-option']);\n    Array.from(templateContent.children).forEach(el => {\n      const tagName = el.tagName.toLowerCase();\n      if (!allowedElements.includes(tagName)) {\n        warn(`Unrecognized element <${tagName}>`);\n      }\n    });\n  };\n\n  /**\n   * @param {HTMLElement} el\n   * @param {string[]} allowedAttributes\n   */\n  const showWarningsForAttributes = (el, allowedAttributes) => {\n    Array.from(el.attributes).forEach(attribute => {\n      if (allowedAttributes.indexOf(attribute.name) === -1) {\n        warn([`Unrecognized attribute \"${attribute.name}\" on <${el.tagName.toLowerCase()}>.`, `${allowedAttributes.length ? `Allowed attributes are: ${allowedAttributes.join(', ')}` : 'To set the value, use HTML within the element.'}`]);\n      }\n    });\n  };\n\n  const SHOW_CLASS_TIMEOUT = 10;\n\n  /**\n   * Open popup, add necessary classes and styles, fix scrollbar\n   *\n   * @param {SweetAlertOptions} params\n   */\n  const openPopup = params => {\n    const container = getContainer();\n    const popup = getPopup();\n    if (typeof params.willOpen === 'function') {\n      params.willOpen(popup);\n    }\n    globalState.eventEmitter.emit('willOpen', popup);\n    const bodyStyles = window.getComputedStyle(document.body);\n    const initialBodyOverflow = bodyStyles.overflowY;\n    addClasses(container, popup, params);\n\n    // scrolling is 'hidden' until animation is done, after that 'auto'\n    setTimeout(() => {\n      setScrollingVisibility(container, popup);\n    }, SHOW_CLASS_TIMEOUT);\n    if (isModal()) {\n      fixScrollContainer(container, params.scrollbarPadding, initialBodyOverflow);\n      setAriaHidden();\n    }\n    if (!isToast() && !globalState.previousActiveElement) {\n      globalState.previousActiveElement = document.activeElement;\n    }\n    if (typeof params.didOpen === 'function') {\n      setTimeout(() => params.didOpen(popup));\n    }\n    globalState.eventEmitter.emit('didOpen', popup);\n    removeClass(container, swalClasses['no-transition']);\n  };\n\n  /**\n   * @param {AnimationEvent} event\n   */\n  const swalOpenAnimationFinished = event => {\n    const popup = getPopup();\n    if (event.target !== popup) {\n      return;\n    }\n    const container = getContainer();\n    popup.removeEventListener('animationend', swalOpenAnimationFinished);\n    popup.removeEventListener('transitionend', swalOpenAnimationFinished);\n    container.style.overflowY = 'auto';\n  };\n\n  /**\n   * @param {HTMLElement} container\n   * @param {HTMLElement} popup\n   */\n  const setScrollingVisibility = (container, popup) => {\n    if (hasCssAnimation(popup)) {\n      container.style.overflowY = 'hidden';\n      popup.addEventListener('animationend', swalOpenAnimationFinished);\n      popup.addEventListener('transitionend', swalOpenAnimationFinished);\n    } else {\n      container.style.overflowY = 'auto';\n    }\n  };\n\n  /**\n   * @param {HTMLElement} container\n   * @param {boolean} scrollbarPadding\n   * @param {string} initialBodyOverflow\n   */\n  const fixScrollContainer = (container, scrollbarPadding, initialBodyOverflow) => {\n    iOSfix();\n    if (scrollbarPadding && initialBodyOverflow !== 'hidden') {\n      replaceScrollbarWithPadding(initialBodyOverflow);\n    }\n\n    // sweetalert2/issues/1247\n    setTimeout(() => {\n      container.scrollTop = 0;\n    });\n  };\n\n  /**\n   * @param {HTMLElement} container\n   * @param {HTMLElement} popup\n   * @param {SweetAlertOptions} params\n   */\n  const addClasses = (container, popup, params) => {\n    addClass(container, params.showClass.backdrop);\n    if (params.animation) {\n      // this workaround with opacity is needed for https://github.com/sweetalert2/sweetalert2/issues/2059\n      popup.style.setProperty('opacity', '0', 'important');\n      show(popup, 'grid');\n      setTimeout(() => {\n        // Animate popup right after showing it\n        addClass(popup, params.showClass.popup);\n        // and remove the opacity workaround\n        popup.style.removeProperty('opacity');\n      }, SHOW_CLASS_TIMEOUT); // 10ms in order to fix #2062\n    } else {\n      show(popup, 'grid');\n    }\n    addClass([document.documentElement, document.body], swalClasses.shown);\n    if (params.heightAuto && params.backdrop && !params.toast) {\n      addClass([document.documentElement, document.body], swalClasses['height-auto']);\n    }\n  };\n\n  var defaultInputValidators = {\n    /**\n     * @param {string} string\n     * @param {string} [validationMessage]\n     * @returns {Promise<string | void>}\n     */\n    email: (string, validationMessage) => {\n      return /^[a-zA-Z0-9.+_'-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z0-9-]+$/.test(string) ? Promise.resolve() : Promise.resolve(validationMessage || 'Invalid email address');\n    },\n    /**\n     * @param {string} string\n     * @param {string} [validationMessage]\n     * @returns {Promise<string | void>}\n     */\n    url: (string, validationMessage) => {\n      // taken from https://stackoverflow.com/a/3809435 with a small change from #1306 and #2013\n      return /^https?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\\.[a-z]{2,63}\\b([-a-zA-Z0-9@:%_+.~#?&/=]*)$/.test(string) ? Promise.resolve() : Promise.resolve(validationMessage || 'Invalid URL');\n    }\n  };\n\n  /**\n   * @param {SweetAlertOptions} params\n   */\n  function setDefaultInputValidators(params) {\n    // Use default `inputValidator` for supported input types if not provided\n    if (params.inputValidator) {\n      return;\n    }\n    if (params.input === 'email') {\n      params.inputValidator = defaultInputValidators['email'];\n    }\n    if (params.input === 'url') {\n      params.inputValidator = defaultInputValidators['url'];\n    }\n  }\n\n  /**\n   * @param {SweetAlertOptions} params\n   */\n  function validateCustomTargetElement(params) {\n    // Determine if the custom target element is valid\n    if (!params.target || typeof params.target === 'string' && !document.querySelector(params.target) || typeof params.target !== 'string' && !params.target.appendChild) {\n      warn('Target parameter is not valid, defaulting to \"body\"');\n      params.target = 'body';\n    }\n  }\n\n  /**\n   * Set type, text and actions on popup\n   *\n   * @param {SweetAlertOptions} params\n   */\n  function setParameters(params) {\n    setDefaultInputValidators(params);\n\n    // showLoaderOnConfirm && preConfirm\n    if (params.showLoaderOnConfirm && !params.preConfirm) {\n      warn('showLoaderOnConfirm is set to true, but preConfirm is not defined.\\n' + 'showLoaderOnConfirm should be used together with preConfirm, see usage example:\\n' + 'https://sweetalert2.github.io/#ajax-request');\n    }\n    validateCustomTargetElement(params);\n\n    // Replace newlines with <br> in title\n    if (typeof params.title === 'string') {\n      params.title = params.title.split('\\n').join('<br />');\n    }\n    init(params);\n  }\n\n  /** @type {SweetAlert} */\n  let currentInstance;\n  var _promise = /*#__PURE__*/new WeakMap();\n  class SweetAlert {\n    /**\n     * @param {...any} args\n     * @this {SweetAlert}\n     */\n    constructor() {\n      /**\n       * @type {Promise<SweetAlertResult>}\n       */\n      _classPrivateFieldInitSpec(this, _promise, void 0);\n      // Prevent run in Node env\n      if (typeof window === 'undefined') {\n        return;\n      }\n      currentInstance = this;\n\n      // @ts-ignore\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      const outerParams = Object.freeze(this.constructor.argsToParams(args));\n\n      /** @type {Readonly<SweetAlertOptions>} */\n      this.params = outerParams;\n\n      /** @type {boolean} */\n      this.isAwaitingPromise = false;\n      _classPrivateFieldSet2(_promise, this, this._main(currentInstance.params));\n    }\n    _main(userParams) {\n      let mixinParams = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      showWarningsForParams(Object.assign({}, mixinParams, userParams));\n      if (globalState.currentInstance) {\n        const swalPromiseResolve = privateMethods.swalPromiseResolve.get(globalState.currentInstance);\n        const {\n          isAwaitingPromise\n        } = globalState.currentInstance;\n        globalState.currentInstance._destroy();\n        if (!isAwaitingPromise) {\n          swalPromiseResolve({\n            isDismissed: true\n          });\n        }\n        if (isModal()) {\n          unsetAriaHidden();\n        }\n      }\n      globalState.currentInstance = currentInstance;\n      const innerParams = prepareParams(userParams, mixinParams);\n      setParameters(innerParams);\n      Object.freeze(innerParams);\n\n      // clear the previous timer\n      if (globalState.timeout) {\n        globalState.timeout.stop();\n        delete globalState.timeout;\n      }\n\n      // clear the restore focus timeout\n      clearTimeout(globalState.restoreFocusTimeout);\n      const domCache = populateDomCache(currentInstance);\n      render(currentInstance, innerParams);\n      privateProps.innerParams.set(currentInstance, innerParams);\n      return swalPromise(currentInstance, domCache, innerParams);\n    }\n\n    // `catch` cannot be the name of a module export, so we define our thenable methods here instead\n    then(onFulfilled) {\n      return _classPrivateFieldGet2(_promise, this).then(onFulfilled);\n    }\n    finally(onFinally) {\n      return _classPrivateFieldGet2(_promise, this).finally(onFinally);\n    }\n  }\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {DomCache} domCache\n   * @param {SweetAlertOptions} innerParams\n   * @returns {Promise}\n   */\n  const swalPromise = (instance, domCache, innerParams) => {\n    return new Promise((resolve, reject) => {\n      // functions to handle all closings/dismissals\n      /**\n       * @param {DismissReason} dismiss\n       */\n      const dismissWith = dismiss => {\n        instance.close({\n          isDismissed: true,\n          dismiss\n        });\n      };\n      privateMethods.swalPromiseResolve.set(instance, resolve);\n      privateMethods.swalPromiseReject.set(instance, reject);\n      domCache.confirmButton.onclick = () => {\n        handleConfirmButtonClick(instance);\n      };\n      domCache.denyButton.onclick = () => {\n        handleDenyButtonClick(instance);\n      };\n      domCache.cancelButton.onclick = () => {\n        handleCancelButtonClick(instance, dismissWith);\n      };\n      domCache.closeButton.onclick = () => {\n        dismissWith(DismissReason.close);\n      };\n      handlePopupClick(innerParams, domCache, dismissWith);\n      addKeydownHandler(globalState, innerParams, dismissWith);\n      handleInputOptionsAndValue(instance, innerParams);\n      openPopup(innerParams);\n      setupTimer(globalState, innerParams, dismissWith);\n      initFocus(domCache, innerParams);\n\n      // Scroll container to top on open (#1247, #1946)\n      setTimeout(() => {\n        domCache.container.scrollTop = 0;\n      });\n    });\n  };\n\n  /**\n   * @param {SweetAlertOptions} userParams\n   * @param {SweetAlertOptions} mixinParams\n   * @returns {SweetAlertOptions}\n   */\n  const prepareParams = (userParams, mixinParams) => {\n    const templateParams = getTemplateParams(userParams);\n    const params = Object.assign({}, defaultParams, mixinParams, templateParams, userParams); // precedence is described in #2131\n    params.showClass = Object.assign({}, defaultParams.showClass, params.showClass);\n    params.hideClass = Object.assign({}, defaultParams.hideClass, params.hideClass);\n    if (params.animation === false) {\n      params.showClass = {\n        backdrop: 'swal2-noanimation'\n      };\n      params.hideClass = {};\n    }\n    return params;\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @returns {DomCache}\n   */\n  const populateDomCache = instance => {\n    const domCache = {\n      popup: getPopup(),\n      container: getContainer(),\n      actions: getActions(),\n      confirmButton: getConfirmButton(),\n      denyButton: getDenyButton(),\n      cancelButton: getCancelButton(),\n      loader: getLoader(),\n      closeButton: getCloseButton(),\n      validationMessage: getValidationMessage(),\n      progressSteps: getProgressSteps()\n    };\n    privateProps.domCache.set(instance, domCache);\n    return domCache;\n  };\n\n  /**\n   * @param {GlobalState} globalState\n   * @param {SweetAlertOptions} innerParams\n   * @param {Function} dismissWith\n   */\n  const setupTimer = (globalState, innerParams, dismissWith) => {\n    const timerProgressBar = getTimerProgressBar();\n    hide(timerProgressBar);\n    if (innerParams.timer) {\n      globalState.timeout = new Timer(() => {\n        dismissWith('timer');\n        delete globalState.timeout;\n      }, innerParams.timer);\n      if (innerParams.timerProgressBar) {\n        show(timerProgressBar);\n        applyCustomClass(timerProgressBar, innerParams, 'timerProgressBar');\n        setTimeout(() => {\n          if (globalState.timeout && globalState.timeout.running) {\n            // timer can be already stopped or unset at this point\n            animateTimerProgressBar(innerParams.timer);\n          }\n        });\n      }\n    }\n  };\n\n  /**\n   * Initialize focus in the popup:\n   *\n   * 1. If `toast` is `true`, don't steal focus from the document.\n   * 2. Else if there is an [autofocus] element, focus it.\n   * 3. Else if `focusConfirm` is `true` and confirm button is visible, focus it.\n   * 4. Else if `focusDeny` is `true` and deny button is visible, focus it.\n   * 5. Else if `focusCancel` is `true` and cancel button is visible, focus it.\n   * 6. Else focus the first focusable element in a popup (if any).\n   *\n   * @param {DomCache} domCache\n   * @param {SweetAlertOptions} innerParams\n   */\n  const initFocus = (domCache, innerParams) => {\n    if (innerParams.toast) {\n      return;\n    }\n    // TODO: this is dumb, remove `allowEnterKey` param in the next major version\n    if (!callIfFunction(innerParams.allowEnterKey)) {\n      warnAboutDeprecation('allowEnterKey');\n      blurActiveElement();\n      return;\n    }\n    if (focusAutofocus(domCache)) {\n      return;\n    }\n    if (focusButton(domCache, innerParams)) {\n      return;\n    }\n    setFocus(-1, 1);\n  };\n\n  /**\n   * @param {DomCache} domCache\n   * @returns {boolean}\n   */\n  const focusAutofocus = domCache => {\n    const autofocusElements = Array.from(domCache.popup.querySelectorAll('[autofocus]'));\n    for (const autofocusElement of autofocusElements) {\n      if (autofocusElement instanceof HTMLElement && isVisible$1(autofocusElement)) {\n        autofocusElement.focus();\n        return true;\n      }\n    }\n    return false;\n  };\n\n  /**\n   * @param {DomCache} domCache\n   * @param {SweetAlertOptions} innerParams\n   * @returns {boolean}\n   */\n  const focusButton = (domCache, innerParams) => {\n    if (innerParams.focusDeny && isVisible$1(domCache.denyButton)) {\n      domCache.denyButton.focus();\n      return true;\n    }\n    if (innerParams.focusCancel && isVisible$1(domCache.cancelButton)) {\n      domCache.cancelButton.focus();\n      return true;\n    }\n    if (innerParams.focusConfirm && isVisible$1(domCache.confirmButton)) {\n      domCache.confirmButton.focus();\n      return true;\n    }\n    return false;\n  };\n  const blurActiveElement = () => {\n    if (document.activeElement instanceof HTMLElement && typeof document.activeElement.blur === 'function') {\n      document.activeElement.blur();\n    }\n  };\n\n  // Dear russian users visiting russian sites. Let's have fun.\n  if (typeof window !== 'undefined' && /^ru\\b/.test(navigator.language) && location.host.match(/\\.(ru|su|by|xn--p1ai)$/)) {\n    const now = new Date();\n    const initiationDate = localStorage.getItem('swal-initiation');\n    if (!initiationDate) {\n      localStorage.setItem('swal-initiation', `${now}`);\n    } else if ((now.getTime() - Date.parse(initiationDate)) / (1000 * 60 * 60 * 24) > 3) {\n      setTimeout(() => {\n        document.body.style.pointerEvents = 'none';\n        const ukrainianAnthem = document.createElement('audio');\n        ukrainianAnthem.src = 'https://flag-gimn.ru/wp-content/uploads/2021/09/Ukraina.mp3';\n        ukrainianAnthem.loop = true;\n        document.body.appendChild(ukrainianAnthem);\n        setTimeout(() => {\n          ukrainianAnthem.play().catch(() => {\n            // ignore\n          });\n        }, 2500);\n      }, 500);\n    }\n  }\n\n  // Assign instance methods from src/instanceMethods/*.js to prototype\n  SweetAlert.prototype.disableButtons = disableButtons;\n  SweetAlert.prototype.enableButtons = enableButtons;\n  SweetAlert.prototype.getInput = getInput;\n  SweetAlert.prototype.disableInput = disableInput;\n  SweetAlert.prototype.enableInput = enableInput;\n  SweetAlert.prototype.hideLoading = hideLoading;\n  SweetAlert.prototype.disableLoading = hideLoading;\n  SweetAlert.prototype.showValidationMessage = showValidationMessage;\n  SweetAlert.prototype.resetValidationMessage = resetValidationMessage;\n  SweetAlert.prototype.close = close;\n  SweetAlert.prototype.closePopup = close;\n  SweetAlert.prototype.closeModal = close;\n  SweetAlert.prototype.closeToast = close;\n  SweetAlert.prototype.rejectPromise = rejectPromise;\n  SweetAlert.prototype.update = update;\n  SweetAlert.prototype._destroy = _destroy;\n\n  // Assign static methods from src/staticMethods/*.js to constructor\n  Object.assign(SweetAlert, staticMethods);\n\n  // Proxy to instance methods to constructor, for now, for backwards compatibility\n  Object.keys(instanceMethods).forEach(key => {\n    /**\n     * @param {...any} args\n     * @returns {any | undefined}\n     */\n    SweetAlert[key] = function () {\n      if (currentInstance && currentInstance[key]) {\n        return currentInstance[key](...arguments);\n      }\n      return null;\n    };\n  });\n  SweetAlert.DismissReason = DismissReason;\n  SweetAlert.version = '11.17.2';\n\n  const Swal = SweetAlert;\n  // @ts-ignore\n  Swal.default = Swal;\n\n  return Swal;\n\n}));\nif (typeof this !== 'undefined' && this.Sweetalert2){this.swal = this.sweetAlert = this.Swal = this.SweetAlert = this.Sweetalert2}\n\"undefined\"!=typeof document&&function(e,t){var n=e.createElement(\"style\");if(e.getElementsByTagName(\"head\")[0].appendChild(n),n.styleSheet)n.styleSheet.disabled||(n.styleSheet.cssText=t);else try{n.innerHTML=t}catch(e){n.innerText=t}}(document,\":root{--swal2-container-padding: 0.625em;--swal2-backdrop: rgba(0, 0, 0, 0.4);--swal2-width: 32em;--swal2-padding: 0 0 1.25em;--swal2-border: none;--swal2-border-radius: 0.3125rem;--swal2-background: white;--swal2-color: #545454;--swal2-footer-border-color: #eee;--swal2-show-animation: swal2-show 0.3s;--swal2-hide-animation: swal2-hide 0.15s forwards;--swal2-input-background: transparent;--swal2-progress-step-background: #add8e6;--swal2-validation-message-background: #f0f0f0;--swal2-validation-message-color: #666;--swal2-close-button-position: initial;--swal2-close-button-inset: auto;--swal2-close-button-font-size: 2.5em;--swal2-close-button-color: #ccc}[data-swal2-theme=dark]{--swal2-dark-theme-black: #19191a;--swal2-dark-theme-white: #e1e1e1;--swal2-background: var(--swal2-dark-theme-black);--swal2-color: var(--swal2-dark-theme-white);--swal2-footer-border-color: #555;--swal2-input-background: color-mix(in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10%);--swal2-validation-message-background: color-mix( in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10% );--swal2-validation-message-color: var(--swal2-dark-theme-white)}@media(prefers-color-scheme: dark){[data-swal2-theme=auto]{--swal2-dark-theme-black: #19191a;--swal2-dark-theme-white: #e1e1e1;--swal2-background: var(--swal2-dark-theme-black);--swal2-color: var(--swal2-dark-theme-white);--swal2-footer-border-color: #555;--swal2-input-background: color-mix(in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10%);--swal2-validation-message-background: color-mix( in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10% );--swal2-validation-message-color: var(--swal2-dark-theme-white)}}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown){overflow:hidden}body.swal2-height-auto{height:auto !important}body.swal2-no-backdrop .swal2-container{background-color:rgba(0,0,0,0) !important;pointer-events:none}body.swal2-no-backdrop .swal2-container .swal2-popup{pointer-events:all}body.swal2-no-backdrop .swal2-container .swal2-modal{box-shadow:0 0 10px var(--swal2-backdrop)}body.swal2-toast-shown .swal2-container{box-sizing:border-box;width:360px;max-width:100%;background-color:rgba(0,0,0,0);pointer-events:none}body.swal2-toast-shown .swal2-container.swal2-top{inset:0 auto auto 50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-top-end,body.swal2-toast-shown .swal2-container.swal2-top-right{inset:0 0 auto auto}body.swal2-toast-shown .swal2-container.swal2-top-start,body.swal2-toast-shown .swal2-container.swal2-top-left{inset:0 auto auto 0}body.swal2-toast-shown .swal2-container.swal2-center-start,body.swal2-toast-shown .swal2-container.swal2-center-left{inset:50% auto auto 0;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-center{inset:50% auto auto 50%;transform:translate(-50%, -50%)}body.swal2-toast-shown .swal2-container.swal2-center-end,body.swal2-toast-shown .swal2-container.swal2-center-right{inset:50% 0 auto auto;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-start,body.swal2-toast-shown .swal2-container.swal2-bottom-left{inset:auto auto 0 0}body.swal2-toast-shown .swal2-container.swal2-bottom{inset:auto auto 0 50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-end,body.swal2-toast-shown .swal2-container.swal2-bottom-right{inset:auto 0 0 auto}@media print{body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown){overflow-y:scroll !important}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown)>[aria-hidden=true]{display:none}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown) .swal2-container{position:static !important}}div:where(.swal2-container){display:grid;position:fixed;z-index:1060;inset:0;box-sizing:border-box;grid-template-areas:\\\"top-start     top            top-end\\\" \\\"center-start  center         center-end\\\" \\\"bottom-start  bottom-center  bottom-end\\\";grid-template-rows:minmax(min-content, auto) minmax(min-content, auto) minmax(min-content, auto);height:100%;padding:var(--swal2-container-padding);overflow-x:hidden;transition:background-color .1s;-webkit-overflow-scrolling:touch}div:where(.swal2-container).swal2-backdrop-show,div:where(.swal2-container).swal2-noanimation{background:var(--swal2-backdrop)}div:where(.swal2-container).swal2-backdrop-hide{background:rgba(0,0,0,0) !important}div:where(.swal2-container).swal2-top-start,div:where(.swal2-container).swal2-center-start,div:where(.swal2-container).swal2-bottom-start{grid-template-columns:minmax(0, 1fr) auto auto}div:where(.swal2-container).swal2-top,div:where(.swal2-container).swal2-center,div:where(.swal2-container).swal2-bottom{grid-template-columns:auto minmax(0, 1fr) auto}div:where(.swal2-container).swal2-top-end,div:where(.swal2-container).swal2-center-end,div:where(.swal2-container).swal2-bottom-end{grid-template-columns:auto auto minmax(0, 1fr)}div:where(.swal2-container).swal2-top-start>.swal2-popup{align-self:start}div:where(.swal2-container).swal2-top>.swal2-popup{grid-column:2;place-self:start center}div:where(.swal2-container).swal2-top-end>.swal2-popup,div:where(.swal2-container).swal2-top-right>.swal2-popup{grid-column:3;place-self:start end}div:where(.swal2-container).swal2-center-start>.swal2-popup,div:where(.swal2-container).swal2-center-left>.swal2-popup{grid-row:2;align-self:center}div:where(.swal2-container).swal2-center>.swal2-popup{grid-column:2;grid-row:2;place-self:center center}div:where(.swal2-container).swal2-center-end>.swal2-popup,div:where(.swal2-container).swal2-center-right>.swal2-popup{grid-column:3;grid-row:2;place-self:center end}div:where(.swal2-container).swal2-bottom-start>.swal2-popup,div:where(.swal2-container).swal2-bottom-left>.swal2-popup{grid-column:1;grid-row:3;align-self:end}div:where(.swal2-container).swal2-bottom>.swal2-popup{grid-column:2;grid-row:3;place-self:end center}div:where(.swal2-container).swal2-bottom-end>.swal2-popup,div:where(.swal2-container).swal2-bottom-right>.swal2-popup{grid-column:3;grid-row:3;place-self:end end}div:where(.swal2-container).swal2-grow-row>.swal2-popup,div:where(.swal2-container).swal2-grow-fullscreen>.swal2-popup{grid-column:1/4;width:100%}div:where(.swal2-container).swal2-grow-column>.swal2-popup,div:where(.swal2-container).swal2-grow-fullscreen>.swal2-popup{grid-row:1/4;align-self:stretch}div:where(.swal2-container).swal2-no-transition{transition:none !important}div:where(.swal2-container) div:where(.swal2-popup){display:none;position:relative;box-sizing:border-box;grid-template-columns:minmax(0, 100%);width:var(--swal2-width);max-width:100%;padding:var(--swal2-padding);border:var(--swal2-border);border-radius:var(--swal2-border-radius);background:var(--swal2-background);color:var(--swal2-color);font-family:inherit;font-size:1rem}div:where(.swal2-container) div:where(.swal2-popup):focus{outline:none}div:where(.swal2-container) div:where(.swal2-popup).swal2-loading{overflow-y:hidden}div:where(.swal2-container) div:where(.swal2-popup).swal2-draggable{cursor:grab}div:where(.swal2-container) div:where(.swal2-popup).swal2-draggable div:where(.swal2-icon){cursor:grab}div:where(.swal2-container) div:where(.swal2-popup).swal2-dragging{cursor:grabbing}div:where(.swal2-container) div:where(.swal2-popup).swal2-dragging div:where(.swal2-icon){cursor:grabbing}div:where(.swal2-container) h2:where(.swal2-title){position:relative;max-width:100%;margin:0;padding:.8em 1em 0;color:inherit;font-size:1.875em;font-weight:600;text-align:center;text-transform:none;word-wrap:break-word;cursor:initial}div:where(.swal2-container) div:where(.swal2-actions){display:flex;z-index:1;box-sizing:border-box;flex-wrap:wrap;align-items:center;justify-content:center;width:auto;margin:1.25em auto 0;padding:0}div:where(.swal2-container) div:where(.swal2-actions):not(.swal2-loading) .swal2-styled[disabled]{opacity:.4}div:where(.swal2-container) div:where(.swal2-actions):not(.swal2-loading) .swal2-styled:hover{background-image:linear-gradient(rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.1))}div:where(.swal2-container) div:where(.swal2-actions):not(.swal2-loading) .swal2-styled:active{background-image:linear-gradient(rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2))}div:where(.swal2-container) div:where(.swal2-loader){display:none;align-items:center;justify-content:center;width:2.2em;height:2.2em;margin:0 1.875em;animation:swal2-rotate-loading 1.5s linear 0s infinite normal;border-width:.25em;border-style:solid;border-radius:100%;border-color:#2778c4 rgba(0,0,0,0) #2778c4 rgba(0,0,0,0)}div:where(.swal2-container) button:where(.swal2-styled){margin:.3125em;padding:.625em 1.1em;transition:box-shadow .1s;box-shadow:0 0 0 3px rgba(0,0,0,0);font-weight:500}div:where(.swal2-container) button:where(.swal2-styled):not([disabled]){cursor:pointer}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm){border:0;border-radius:.25em;background:initial;background-color:#7066e0;color:#fff;font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm):focus-visible{box-shadow:0 0 0 3px rgba(112,102,224,.5)}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny){border:0;border-radius:.25em;background:initial;background-color:#dc3741;color:#fff;font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny):focus-visible{box-shadow:0 0 0 3px rgba(220,55,65,.5)}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel){border:0;border-radius:.25em;background:initial;background-color:#6e7881;color:#fff;font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel):focus-visible{box-shadow:0 0 0 3px rgba(110,120,129,.5)}div:where(.swal2-container) button:where(.swal2-styled).swal2-default-outline:focus-visible{box-shadow:0 0 0 3px rgba(100,150,200,.5)}div:where(.swal2-container) button:where(.swal2-styled):focus-visible{outline:none}div:where(.swal2-container) button:where(.swal2-styled)::-moz-focus-inner{border:0}div:where(.swal2-container) div:where(.swal2-footer){margin:1em 0 0;padding:1em 1em 0;border-top:1px solid var(--swal2-footer-border-color);color:inherit;font-size:1em;text-align:center;cursor:initial}div:where(.swal2-container) .swal2-timer-progress-bar-container{position:absolute;right:0;bottom:0;left:0;grid-column:auto !important;overflow:hidden;border-bottom-right-radius:var(--swal2-border-radius);border-bottom-left-radius:var(--swal2-border-radius)}div:where(.swal2-container) div:where(.swal2-timer-progress-bar){width:100%;height:.25em;background:rgba(0,0,0,.2)}div:where(.swal2-container) img:where(.swal2-image){max-width:100%;margin:2em auto 1em;cursor:initial}div:where(.swal2-container) button:where(.swal2-close){position:var(--swal2-close-button-position);inset:var(--swal2-close-button-inset);z-index:2;align-items:center;justify-content:center;width:1.2em;height:1.2em;margin-top:0;margin-right:0;margin-bottom:-1.2em;padding:0;overflow:hidden;transition:color .1s,box-shadow .1s;border:none;border-radius:var(--swal2-border-radius);background:rgba(0,0,0,0);color:var(--swal2-close-button-color);font-family:monospace;font-size:var(--swal2-close-button-font-size);cursor:pointer;justify-self:end}div:where(.swal2-container) button:where(.swal2-close):hover{transform:none;background:rgba(0,0,0,0);color:#f27474}div:where(.swal2-container) button:where(.swal2-close):focus-visible{outline:none;box-shadow:inset 0 0 0 3px rgba(100,150,200,.5)}div:where(.swal2-container) button:where(.swal2-close)::-moz-focus-inner{border:0}div:where(.swal2-container) div:where(.swal2-html-container){z-index:1;justify-content:center;margin:0;padding:1em 1.6em .3em;overflow:auto;color:inherit;font-size:1.125em;font-weight:normal;line-height:normal;text-align:center;word-wrap:break-word;word-break:break-word;cursor:initial}div:where(.swal2-container) input:where(.swal2-input),div:where(.swal2-container) input:where(.swal2-file),div:where(.swal2-container) textarea:where(.swal2-textarea),div:where(.swal2-container) select:where(.swal2-select),div:where(.swal2-container) div:where(.swal2-radio),div:where(.swal2-container) label:where(.swal2-checkbox){margin:1em 2em 3px}div:where(.swal2-container) input:where(.swal2-input),div:where(.swal2-container) input:where(.swal2-file),div:where(.swal2-container) textarea:where(.swal2-textarea){box-sizing:border-box;width:auto;transition:border-color .1s,box-shadow .1s;border:1px solid #d9d9d9;border-radius:.1875em;background:var(--swal2-input-background);box-shadow:inset 0 1px 1px rgba(0,0,0,.06),0 0 0 3px rgba(0,0,0,0);color:inherit;font-size:1.125em}div:where(.swal2-container) input:where(.swal2-input).swal2-inputerror,div:where(.swal2-container) input:where(.swal2-file).swal2-inputerror,div:where(.swal2-container) textarea:where(.swal2-textarea).swal2-inputerror{border-color:#f27474 !important;box-shadow:0 0 2px #f27474 !important}div:where(.swal2-container) input:where(.swal2-input):focus,div:where(.swal2-container) input:where(.swal2-file):focus,div:where(.swal2-container) textarea:where(.swal2-textarea):focus{border:1px solid #b4dbed;outline:none;box-shadow:inset 0 1px 1px rgba(0,0,0,.06),0 0 0 3px rgba(100,150,200,.5)}div:where(.swal2-container) input:where(.swal2-input)::placeholder,div:where(.swal2-container) input:where(.swal2-file)::placeholder,div:where(.swal2-container) textarea:where(.swal2-textarea)::placeholder{color:#ccc}div:where(.swal2-container) .swal2-range{margin:1em 2em 3px;background:var(--swal2-background)}div:where(.swal2-container) .swal2-range input{width:80%}div:where(.swal2-container) .swal2-range output{width:20%;color:inherit;font-weight:600;text-align:center}div:where(.swal2-container) .swal2-range input,div:where(.swal2-container) .swal2-range output{height:2.625em;padding:0;font-size:1.125em;line-height:2.625em}div:where(.swal2-container) .swal2-input{height:2.625em;padding:0 .75em}div:where(.swal2-container) .swal2-file{width:75%;margin-right:auto;margin-left:auto;background:var(--swal2-input-background);font-size:1.125em}div:where(.swal2-container) .swal2-textarea{height:6.75em;padding:.75em}div:where(.swal2-container) .swal2-select{min-width:50%;max-width:100%;padding:.375em .625em;background:var(--swal2-input-background);color:inherit;font-size:1.125em}div:where(.swal2-container) .swal2-radio,div:where(.swal2-container) .swal2-checkbox{align-items:center;justify-content:center;background:var(--swal2-background);color:inherit}div:where(.swal2-container) .swal2-radio label,div:where(.swal2-container) .swal2-checkbox label{margin:0 .6em;font-size:1.125em}div:where(.swal2-container) .swal2-radio input,div:where(.swal2-container) .swal2-checkbox input{flex-shrink:0;margin:0 .4em}div:where(.swal2-container) label:where(.swal2-input-label){display:flex;justify-content:center;margin:1em auto 0}div:where(.swal2-container) div:where(.swal2-validation-message){align-items:center;justify-content:center;margin:1em 0 0;padding:.625em;overflow:hidden;background:var(--swal2-validation-message-background);color:var(--swal2-validation-message-color);font-size:1em;font-weight:300}div:where(.swal2-container) div:where(.swal2-validation-message)::before{content:\\\"!\\\";display:inline-block;width:1.5em;min-width:1.5em;height:1.5em;margin:0 .625em;border-radius:50%;background-color:#f27474;color:#fff;font-weight:600;line-height:1.5em;text-align:center}div:where(.swal2-container) .swal2-progress-steps{flex-wrap:wrap;align-items:center;max-width:100%;margin:1.25em auto;padding:0;background:rgba(0,0,0,0);font-weight:600}div:where(.swal2-container) .swal2-progress-steps li{display:inline-block;position:relative}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step{z-index:20;flex-shrink:0;width:2em;height:2em;border-radius:2em;background:#2778c4;color:#fff;line-height:2em;text-align:center}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step{background:#2778c4}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step{background:var(--swal2-progress-step-background);color:#fff}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step-line{background:var(--swal2-progress-step-background)}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step-line{z-index:10;flex-shrink:0;width:2.5em;height:.4em;margin:0 -1px;background:#2778c4}div:where(.swal2-icon){position:relative;box-sizing:content-box;justify-content:center;width:5em;height:5em;margin:2.5em auto .6em;border:.25em solid rgba(0,0,0,0);border-radius:50%;border-color:#000;font-family:inherit;line-height:5em;cursor:default;user-select:none}div:where(.swal2-icon) .swal2-icon-content{display:flex;align-items:center;font-size:3.75em}div:where(.swal2-icon).swal2-error{border-color:#f27474;color:#f27474}div:where(.swal2-icon).swal2-error .swal2-x-mark{position:relative;flex-grow:1}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line]{display:block;position:absolute;top:2.3125em;width:2.9375em;height:.3125em;border-radius:.125em;background-color:#f27474}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line][class$=left]{left:1.0625em;transform:rotate(45deg)}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line][class$=right]{right:1em;transform:rotate(-45deg)}div:where(.swal2-icon).swal2-error.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-error.swal2-icon-show .swal2-x-mark{animation:swal2-animate-error-x-mark .5s}div:where(.swal2-icon).swal2-warning{border-color:#f8bb86;color:#f8bb86}div:where(.swal2-icon).swal2-warning.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-warning.swal2-icon-show .swal2-icon-content{animation:swal2-animate-i-mark .5s}div:where(.swal2-icon).swal2-info{border-color:#3fc3ee;color:#3fc3ee}div:where(.swal2-icon).swal2-info.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-info.swal2-icon-show .swal2-icon-content{animation:swal2-animate-i-mark .8s}div:where(.swal2-icon).swal2-question{border-color:#87adbd;color:#87adbd}div:where(.swal2-icon).swal2-question.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-question.swal2-icon-show .swal2-icon-content{animation:swal2-animate-question-mark .8s}div:where(.swal2-icon).swal2-success{border-color:#a5dc86;color:#a5dc86}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line]{position:absolute;width:3.75em;height:7.5em;border-radius:50%}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line][class$=left]{top:-0.4375em;left:-2.0635em;transform:rotate(-45deg);transform-origin:3.75em 3.75em;border-radius:7.5em 0 0 7.5em}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line][class$=right]{top:-0.6875em;left:1.875em;transform:rotate(-45deg);transform-origin:0 3.75em;border-radius:0 7.5em 7.5em 0}div:where(.swal2-icon).swal2-success .swal2-success-ring{position:absolute;z-index:2;top:-0.25em;left:-0.25em;box-sizing:content-box;width:100%;height:100%;border:.25em solid rgba(165,220,134,.3);border-radius:50%}div:where(.swal2-icon).swal2-success .swal2-success-fix{position:absolute;z-index:1;top:.5em;left:1.625em;width:.4375em;height:5.625em;transform:rotate(-45deg)}div:where(.swal2-icon).swal2-success [class^=swal2-success-line]{display:block;position:absolute;z-index:2;height:.3125em;border-radius:.125em;background-color:#a5dc86}div:where(.swal2-icon).swal2-success [class^=swal2-success-line][class$=tip]{top:2.875em;left:.8125em;width:1.5625em;transform:rotate(45deg)}div:where(.swal2-icon).swal2-success [class^=swal2-success-line][class$=long]{top:2.375em;right:.5em;width:2.9375em;transform:rotate(-45deg)}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-line-tip{animation:swal2-animate-success-line-tip .75s}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-line-long{animation:swal2-animate-success-line-long .75s}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-circular-line-right{animation:swal2-rotate-success-circular-line 4.25s ease-in}[class^=swal2]{-webkit-tap-highlight-color:rgba(0,0,0,0)}.swal2-show{animation:var(--swal2-show-animation)}.swal2-hide{animation:var(--swal2-hide-animation)}.swal2-noanimation{transition:none}.swal2-scrollbar-measure{position:absolute;top:-9999px;width:50px;height:50px;overflow:scroll}.swal2-rtl .swal2-close{margin-right:initial;margin-left:0}.swal2-rtl .swal2-timer-progress-bar{right:0;left:auto}.swal2-toast{box-sizing:border-box;grid-column:1/4 !important;grid-row:1/4 !important;grid-template-columns:min-content auto min-content;padding:1em;overflow-y:hidden;background:var(--swal2-background);box-shadow:0 0 1px rgba(0,0,0,.075),0 1px 2px rgba(0,0,0,.075),1px 2px 4px rgba(0,0,0,.075),1px 3px 8px rgba(0,0,0,.075),2px 4px 16px rgba(0,0,0,.075);pointer-events:all}.swal2-toast>*{grid-column:2}.swal2-toast h2:where(.swal2-title){margin:.5em 1em;padding:0;font-size:1em;text-align:initial}.swal2-toast .swal2-loading{justify-content:center}.swal2-toast input:where(.swal2-input){height:2em;margin:.5em;font-size:1em}.swal2-toast .swal2-validation-message{font-size:1em}.swal2-toast div:where(.swal2-footer){margin:.5em 0 0;padding:.5em 0 0;font-size:.8em}.swal2-toast button:where(.swal2-close){grid-column:3/3;grid-row:1/99;align-self:center;width:.8em;height:.8em;margin:0;font-size:2em}.swal2-toast div:where(.swal2-html-container){margin:.5em 1em;padding:0;overflow:initial;font-size:1em;text-align:initial}.swal2-toast div:where(.swal2-html-container):empty{padding:0}.swal2-toast .swal2-loader{grid-column:1;grid-row:1/99;align-self:center;width:2em;height:2em;margin:.25em}.swal2-toast .swal2-icon{grid-column:1;grid-row:1/99;align-self:center;width:2em;min-width:2em;height:2em;margin:0 .5em 0 0}.swal2-toast .swal2-icon .swal2-icon-content{display:flex;align-items:center;font-size:1.8em;font-weight:bold}.swal2-toast .swal2-icon.swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line]{top:.875em;width:1.375em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left]{left:.3125em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right]{right:.3125em}.swal2-toast div:where(.swal2-actions){justify-content:flex-start;height:auto;margin:0;margin-top:.5em;padding:0 .5em}.swal2-toast button:where(.swal2-styled){margin:.25em .5em;padding:.4em .6em;font-size:1em}.swal2-toast .swal2-success{border-color:#a5dc86}.swal2-toast .swal2-success [class^=swal2-success-circular-line]{position:absolute;width:1.6em;height:3em;border-radius:50%}.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=left]{top:-0.8em;left:-0.5em;transform:rotate(-45deg);transform-origin:2em 2em;border-radius:4em 0 0 4em}.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=right]{top:-0.25em;left:.9375em;transform-origin:0 1.5em;border-radius:0 4em 4em 0}.swal2-toast .swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-toast .swal2-success .swal2-success-fix{top:0;left:.4375em;width:.4375em;height:2.6875em}.swal2-toast .swal2-success [class^=swal2-success-line]{height:.3125em}.swal2-toast .swal2-success [class^=swal2-success-line][class$=tip]{top:1.125em;left:.1875em;width:.75em}.swal2-toast .swal2-success [class^=swal2-success-line][class$=long]{top:.9375em;right:.1875em;width:1.375em}.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-tip{animation:swal2-toast-animate-success-line-tip .75s}.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-long{animation:swal2-toast-animate-success-line-long .75s}.swal2-toast.swal2-show{animation:swal2-toast-show .5s}.swal2-toast.swal2-hide{animation:swal2-toast-hide .1s forwards}@keyframes swal2-show{0%{transform:scale(0.7)}45%{transform:scale(1.05)}80%{transform:scale(0.95)}100%{transform:scale(1)}}@keyframes swal2-hide{0%{transform:scale(1);opacity:1}100%{transform:scale(0.5);opacity:0}}@keyframes swal2-animate-success-line-tip{0%{top:1.1875em;left:.0625em;width:0}54%{top:1.0625em;left:.125em;width:0}70%{top:2.1875em;left:-0.375em;width:3.125em}84%{top:3em;left:1.3125em;width:1.0625em}100%{top:2.8125em;left:.8125em;width:1.5625em}}@keyframes swal2-animate-success-line-long{0%{top:3.375em;right:2.875em;width:0}65%{top:3.375em;right:2.875em;width:0}84%{top:2.1875em;right:0;width:3.4375em}100%{top:2.375em;right:.5em;width:2.9375em}}@keyframes swal2-rotate-success-circular-line{0%{transform:rotate(-45deg)}5%{transform:rotate(-45deg)}12%{transform:rotate(-405deg)}100%{transform:rotate(-405deg)}}@keyframes swal2-animate-error-x-mark{0%{margin-top:1.625em;transform:scale(0.4);opacity:0}50%{margin-top:1.625em;transform:scale(0.4);opacity:0}80%{margin-top:-0.375em;transform:scale(1.15)}100%{margin-top:0;transform:scale(1);opacity:1}}@keyframes swal2-animate-error-icon{0%{transform:rotateX(100deg);opacity:0}100%{transform:rotateX(0deg);opacity:1}}@keyframes swal2-rotate-loading{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}@keyframes swal2-animate-question-mark{0%{transform:rotateY(-360deg)}100%{transform:rotateY(0)}}@keyframes swal2-animate-i-mark{0%{transform:rotateZ(45deg);opacity:0}25%{transform:rotateZ(-25deg);opacity:.4}50%{transform:rotateZ(15deg);opacity:.8}75%{transform:rotateZ(-5deg);opacity:1}100%{transform:rotateX(0);opacity:1}}@keyframes swal2-toast-show{0%{transform:translateY(-0.625em) rotateZ(2deg)}33%{transform:translateY(0) rotateZ(-2deg)}66%{transform:translateY(0.3125em) rotateZ(2deg)}100%{transform:translateY(0) rotateZ(0deg)}}@keyframes swal2-toast-hide{100%{transform:rotateZ(1deg);opacity:0}}@keyframes swal2-toast-animate-success-line-tip{0%{top:.5625em;left:.0625em;width:0}54%{top:.125em;left:.125em;width:0}70%{top:.625em;left:-0.25em;width:1.625em}84%{top:1.0625em;left:.75em;width:.5em}100%{top:1.125em;left:.1875em;width:.75em}}@keyframes swal2-toast-animate-success-line-long{0%{top:1.625em;right:1.375em;width:0}65%{top:1.25em;right:.9375em;width:0}84%{top:.9375em;right:0;width:1.125em}100%{top:.9375em;right:.1875em;width:1.375em}}\");\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/sweetalert2@11.17.2/node_modules/sweetalert2/dist/sweetalert2.all.js?");

/***/ }),

/***/ "./src/assets/js/app-helpers.js":
/*!**************************************!*\
  !*** ./src/assets/js/app-helpers.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AppHelpers)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/typeof */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/typeof.js\");\n/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\");\n/* harmony import */ var _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/classCallCheck */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/classCallCheck.js\");\n/* harmony import */ var _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/createClass */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/createClass.js\");\n\n\n\n\nvar AppHelpers = /*#__PURE__*/function () {\n  function AppHelpers() {\n    (0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, AppHelpers);\n  }\n  return (0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(AppHelpers, [{\n    key: \"toggleClassIf\",\n    value:\n    /**\r\n     * @param {string} selector\r\n     * @param {array<string>} classes1\r\n     * @param {array<string>} classes2\r\n     * @param callback\r\n     */\n    function toggleClassIf(selector, classes1, classes2, callback) {\n      var _this = this;\n      document.querySelectorAll(selector).forEach(function (element) {\n        return _this.toggleElementClassIf(element, classes1, classes2, callback);\n      });\n      return this;\n    }\n  }, {\n    key: \"toggleElementClassIf\",\n    value: function toggleElementClassIf(elements, classes1, classes2, callback) {\n      classes1 = Array.isArray(classes1) ? classes1 : classes1.split(' ');\n      classes2 = Array.isArray(classes2) ? classes2 : classes2.split(' ');\n      var elementsArray = Array.isArray(elements) ? elements : [elements];\n      elementsArray.forEach(function (element) {\n        var _element$classList, _element$classList2;\n        if (!element) return;\n        var isClasses1 = callback(element);\n        (_element$classList = element.classList).remove.apply(_element$classList, (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(isClasses1 ? classes2 : classes1));\n        (_element$classList2 = element.classList).add.apply(_element$classList2, (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(isClasses1 ? classes1 : classes2));\n      });\n      return this;\n    }\n\n    /**\r\n     * @param {string|HTMLElement} selector\r\n     * @return {null|HTMLElement}\r\n     */\n  }, {\n    key: \"element\",\n    value: function element(selector) {\n      if ((0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(selector) == 'object') {\n        return selector;\n      }\n      if (selector === '.total-price' || selector === '.before-price') {\n        return document.querySelectorAll(selector);\n      }\n      return document.querySelector(selector);\n    }\n\n    /**\r\n     * @param {string} name\r\n     * @param {string} selector\r\n     * @return {Helpers}\r\n     */\n  }, {\n    key: \"watchElement\",\n    value: function watchElement(name, selector) {\n      this[name] = this.element(selector);\n      return this;\n    }\n\n    /**\r\n     * @param {Object.<string, string>} elements\r\n     */\n  }, {\n    key: \"watchElements\",\n    value: function watchElements(elements) {\n      var _this2 = this;\n      Object.entries(elements).forEach(function (element) {\n        return _this2.watchElement(element[0], element[1]);\n      });\n      return this;\n    }\n\n    /**\r\n     * @param {string} action\r\n     * @param {string|HTMLElement} element\r\n     * @param {function} callback\r\n     * @param {object|undefined} options\r\n     * @return {AppHelpers}\r\n     */\n  }, {\n    key: \"on\",\n    value: function on(action, element, callback) {\n      var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n      if ((0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(element) == 'object') {\n        this.element(element).addEventListener(action, callback, options);\n        return this;\n      }\n\n      //if it's selector loop through all of the elements\n      document.querySelectorAll(element).forEach(function (el) {\n        return el.addEventListener(action, callback, options);\n      });\n      return this;\n    }\n\n    /**\r\n     * @param {string|HTMLElement} element\r\n     * @param {function} callback\r\n     * @return {AppHelpers}\r\n     */\n  }, {\n    key: \"onClick\",\n    value: function onClick(element, callback) {\n      return this.on('click', element, callback);\n    }\n\n    /**\r\n     * @param {string|HTMLElement} element\r\n     * @param {function} callback\r\n     * @return {AppHelpers}\r\n     */\n  }, {\n    key: \"onKeyUp\",\n    value: function onKeyUp(element, callback) {\n      return this.on('keyup', element, callback);\n    }\n\n    /**\r\n     * @param {string|HTMLElement} element\r\n     * @param {function} callback\r\n     * @return {AppHelpers}\r\n     */\n  }, {\n    key: \"all\",\n    value: function all(element, callback) {\n      document.querySelectorAll(element).forEach(callback);\n      return this;\n    }\n\n    /**\r\n     * @param {string|HTMLElement} element\r\n     * @return {AppHelpers}\r\n     */\n  }, {\n    key: \"hideElement\",\n    value: function hideElement(element) {\n      this.element(element).style.display = 'none';\n      return this;\n    }\n\n    /**\r\n     * @param {string|HTMLElement} element\r\n     * @return {AppHelpers}\r\n     */\n  }, {\n    key: \"showElement\",\n    value: function showElement(element) {\n      var display = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'block';\n      this.element(element).style.display = display;\n      return this;\n    }\n\n    /**\r\n     * 💡 you can pass multi classes: this.removeClass(element, 'class_1', 'class_2', ...)\r\n     * @param {string|HTMLElement} element\r\n     * @param {string} className\r\n     * @return {AppHelpers}\r\n     */\n  }, {\n    key: \"removeClass\",\n    value: function removeClass(element, className) {\n      var _this$element$classLi;\n      (_this$element$classLi = this.element(element).classList).remove.apply(_this$element$classLi, (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(Array.from(arguments).slice(1)));\n      return this;\n    }\n\n    /**\r\n     * 💡 you can pass multi classes: this.addClass(element, 'class_1', 'class_2', ...)\r\n     * @param {string|HTMLElement} element\r\n     * @param {string} className\r\n     * @return {AppHelpers}\r\n     */\n  }, {\n    key: \"addClass\",\n    value: function addClass(element, className) {\n      var _this$element$classLi2;\n      (_this$element$classLi2 = this.element(element).classList).add.apply(_this$element$classLi2, (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(Array.from(arguments).slice(1)));\n      return this;\n    }\n  }]);\n}();\n\n\n//# sourceURL=webpack://theme-raed/./src/assets/js/app-helpers.js?");

/***/ }),

/***/ "./src/assets/js/app.js":
/*!******************************!*\
  !*** ./src/assets/js/app.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/typeof */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/typeof.js\");\n/* harmony import */ var _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/classCallCheck */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/classCallCheck.js\");\n/* harmony import */ var _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/createClass */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/createClass.js\");\n/* harmony import */ var _babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/possibleConstructorReturn */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/possibleConstructorReturn.js\");\n/* harmony import */ var _babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/getPrototypeOf */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/getPrototypeOf.js\");\n/* harmony import */ var _babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/inherits */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/inherits.js\");\n/* harmony import */ var mmenu_light__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! mmenu-light */ \"./node_modules/.pnpm/mmenu-light@3.2.2/node_modules/mmenu-light/src/mmenu-light.js\");\n/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! sweetalert2 */ \"./node_modules/.pnpm/sweetalert2@11.17.2/node_modules/sweetalert2/dist/sweetalert2.all.js\");\n/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(sweetalert2__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _partials_anime__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./partials/anime */ \"./src/assets/js/partials/anime.js\");\n/* harmony import */ var _partials_tooltip__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./partials/tooltip */ \"./src/assets/js/partials/tooltip.js\");\n/* harmony import */ var _app_helpers__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./app-helpers */ \"./src/assets/js/app-helpers.js\");\n/* harmony import */ var _partials_category_search__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./partials/category-search */ \"./src/assets/js/partials/category-search.js\");\n/* harmony import */ var _main_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./main.js */ \"./src/assets/js/main.js\");\n\n\n\n\n\n\nfunction _callSuper(t, o, e) { return o = (0,_babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(o), (0,_babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0,_babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\n\n\n\n\n\n\n\n// import './particles-background.js'; // تم تعطيل الخلفية المتحركة\nvar App = /*#__PURE__*/function (_AppHelpers) {\n  function App() {\n    var _this;\n    (0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(this, App);\n    _this = _callSuper(this, App);\n    window.app = _this;\n    return _this;\n  }\n  (0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(App, _AppHelpers);\n  return (0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(App, [{\n    key: \"loadTheApp\",\n    value: function loadTheApp() {\n      this.commonThings();\n      this.initiateNotifier();\n      this.initiateMobileMenu();\n      if (header_is_sticky) {\n        this.initiateStickyMenu();\n      }\n      this.initAddToCart();\n      this.initiateAdAlert();\n      this.initiateDropdowns();\n      this.initiateModals();\n      this.initiateCollapse();\n      this.initAttachWishlistListeners();\n      this.changeMenuDirection();\n      this.initCustomComponents();\n      (0,_partials_tooltip__WEBPACK_IMPORTED_MODULE_9__[\"default\"])();\n      this.loadModalImgOnclick();\n      salla.comment.event.onAdded(function () {\n        return window.location.reload();\n      });\n      this.status = 'ready';\n      document.dispatchEvent(new CustomEvent('theme::ready'));\n      this.log('Theme Loaded 🎉');\n    }\n\n    /**\n     * تهيئة المكونات المخصصة\n     */\n  }, {\n    key: \"initCustomComponents\",\n    value: function initCustomComponents() {\n      this.log('تحميل المكونات المخصصة...');\n\n      // التأكد من أن العنصر المخصص للبحث مسجل\n      if (!customElements.get('category-search')) {\n        this.log('تسجيل مكون البحث يدوياً');\n        customElements.define('category-search', _partials_category_search__WEBPACK_IMPORTED_MODULE_11__[\"default\"]);\n      }\n      this.log('اكتمل تحميل المكونات المخصصة');\n    }\n  }, {\n    key: \"log\",\n    value: function log(message) {\n      salla.log(\"ThemeApp(Raed)::\".concat(message));\n      return this;\n    }\n\n    // fix Menu Direction at the third level >> The menu at the third level was popping off the page\n  }, {\n    key: \"changeMenuDirection\",\n    value: function changeMenuDirection() {\n      app.all('.root-level.has-children', function (item) {\n        if (item.classList.contains('change-menu-dir')) return;\n        app.on('mouseover', item, function () {\n          var submenu = item.querySelector('.sub-menu .sub-menu');\n          if (submenu) {\n            var rect = submenu.getBoundingClientRect();\n            (rect.left < 10 || rect.right > window.innerWidth - 10) && app.addClass(item, 'change-menu-dir');\n          }\n        });\n      });\n    }\n  }, {\n    key: \"loadModalImgOnclick\",\n    value: function loadModalImgOnclick() {\n      document.querySelectorAll('.load-img-onclick').forEach(function (link) {\n        link.addEventListener('click', function (event) {\n          event.preventDefault();\n          var modal = document.querySelector('#' + link.dataset.modalId),\n            img = modal.querySelector('img'),\n            imgSrc = img.dataset.src;\n          modal.open();\n          if (img.classList.contains('loaded')) return;\n          img.src = imgSrc;\n          img.classList.add('loaded');\n        });\n      });\n    }\n  }, {\n    key: \"commonThings\",\n    value: function commonThings() {\n      this.cleanContentArticles('.content-entry');\n    }\n  }, {\n    key: \"cleanContentArticles\",\n    value: function cleanContentArticles(elementsSelector) {\n      var articleElements = document.querySelectorAll(elementsSelector);\n      if (articleElements.length) {\n        articleElements.forEach(function (article) {\n          article.innerHTML = article.innerHTML.replace(/\\&nbsp;/g, ' ');\n        });\n      }\n    }\n  }, {\n    key: \"isElementLoaded\",\n    value: function isElementLoaded(selector) {\n      return new Promise(function (resolve) {\n        var interval = setInterval(function () {\n          if (document.querySelector(selector)) {\n            clearInterval(interval);\n            return resolve(document.querySelector(selector));\n          }\n        }, 160);\n      });\n    }\n  }, {\n    key: \"copyToClipboard\",\n    value: function copyToClipboard(event) {\n      var _this2 = this;\n      event.preventDefault();\n      var aux = document.createElement(\"input\"),\n        btn = event.currentTarget;\n      aux.setAttribute(\"value\", btn.dataset.content);\n      document.body.appendChild(aux);\n      aux.select();\n      document.execCommand(\"copy\");\n      document.body.removeChild(aux);\n      this.toggleElementClassIf(btn, 'copied', 'code-to-copy', function () {\n        return true;\n      });\n      setTimeout(function () {\n        _this2.toggleElementClassIf(btn, 'code-to-copy', 'copied', function () {\n          return true;\n        });\n      }, 1000);\n    }\n  }, {\n    key: \"initiateNotifier\",\n    value: function initiateNotifier() {\n      salla.notify.setNotifier(function (message, type, data) {\n        if ((0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(message) == 'object') {\n          return sweetalert2__WEBPACK_IMPORTED_MODULE_7___default().fire(message).then(type);\n        }\n        return sweetalert2__WEBPACK_IMPORTED_MODULE_7___default().mixin({\n          toast: true,\n          position: salla.config.get('theme.is_rtl') ? 'top-start' : 'top-end',\n          showConfirmButton: false,\n          timer: 2000,\n          didOpen: function didOpen(toast) {\n            toast.addEventListener('mouseenter', (sweetalert2__WEBPACK_IMPORTED_MODULE_7___default().stopTimer));\n            toast.addEventListener('mouseleave', (sweetalert2__WEBPACK_IMPORTED_MODULE_7___default().resumeTimer));\n          }\n        }).fire({\n          icon: type,\n          title: message,\n          showCloseButton: true,\n          timerProgressBar: true\n        });\n      });\n    }\n  }, {\n    key: \"initiateMobileMenu\",\n    value: function initiateMobileMenu() {\n      var _this3 = this;\n      this.isElementLoaded('#mobile-menu').then(function (menu) {\n        var mobileMenu = new mmenu_light__WEBPACK_IMPORTED_MODULE_6__[\"default\"](menu, \"(max-width: 1024px)\", \"( slidingSubmenus: false)\");\n        salla.lang.onLoaded(function () {\n          mobileMenu.navigation({\n            title: salla.lang.get('blocks.header.main_menu')\n          });\n        });\n        var drawer = mobileMenu.offcanvas({\n          position: salla.config.get('theme.is_rtl') ? \"right\" : 'left'\n        });\n\n        // فتح القائمة\n        _this3.onClick(\"a[href='#mobile-menu']\", function (event) {\n          event.preventDefault();\n          document.body.classList.add('menu-opened');\n\n          // إغلاق أي dropdowns مفتوحة\n          document.querySelectorAll('.dropdown-toggler.is-opened').forEach(function (dropdown) {\n            dropdown.classList.remove('is-opened');\n          });\n          document.body.classList.remove('dropdown--is-opened');\n          drawer.open();\n        });\n\n        // إغلاق القائمة\n        _this3.onClick(\".close-mobile-menu\", function (event) {\n          event.preventDefault();\n          document.body.classList.remove('menu-opened');\n          drawer.close();\n        });\n\n        // إغلاق القائمة عند النقر على الخلفية\n        document.addEventListener('click', function (event) {\n          if (event.target.classList.contains('mm-ocd__backdrop')) {\n            document.body.classList.remove('menu-opened');\n          }\n        });\n\n        // إغلاق القائمة بالـ escape key\n        document.addEventListener('keydown', function (event) {\n          if (event.key === 'Escape' && document.body.classList.contains('menu-opened')) {\n            document.body.classList.remove('menu-opened');\n            drawer.close();\n          }\n        });\n      });\n    }\n  }, {\n    key: \"initAttachWishlistListeners\",\n    value: function initAttachWishlistListeners() {\n      var isListenerAttached = false;\n      function toggleFavoriteIcon(id) {\n        var isAdded = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n        document.querySelectorAll('.s-product-card-wishlist-btn[data-id=\"' + id + '\"]').forEach(function (btn) {\n          app.toggleElementClassIf(btn, 's-product-card-wishlist-added', 'not-added', function () {\n            return isAdded;\n          });\n          app.toggleElementClassIf(btn, 'pulse-anime', 'un-favorited', function () {\n            return isAdded;\n          });\n        });\n      }\n      if (!isListenerAttached) {\n        salla.wishlist.event.onAdded(function (event, id) {\n          return toggleFavoriteIcon(id);\n        });\n        salla.wishlist.event.onRemoved(function (event, id) {\n          return toggleFavoriteIcon(id, false);\n        });\n        isListenerAttached = true; // Mark the listener as attached\n      }\n    }\n  }, {\n    key: \"initiateStickyMenu\",\n    value: function initiateStickyMenu() {\n      var _this$element,\n        _this4 = this;\n      var header = this.element('#mainnav'),\n        height = (_this$element = this.element('#mainnav .inner')) === null || _this$element === void 0 ? void 0 : _this$element.clientHeight;\n      //when it's landing page, there is no header\n      if (!header) {\n        return;\n      }\n      window.addEventListener('load', function () {\n        return setTimeout(function () {\n          return _this4.setHeaderHeight();\n        }, 500);\n      });\n      window.addEventListener('resize', function () {\n        return _this4.setHeaderHeight();\n      });\n      window.addEventListener('scroll', function () {\n        window.scrollY >= header.offsetTop + height ? header.classList.add('fixed-pinned', 'animated') : header.classList.remove('fixed-pinned');\n        window.scrollY >= 200 ? header.classList.add('fixed-header') : header.classList.remove('fixed-header', 'animated');\n      }, {\n        passive: true\n      });\n    }\n  }, {\n    key: \"setHeaderHeight\",\n    value: function setHeaderHeight() {\n      var height = this.element('#mainnav .inner').clientHeight,\n        header = this.element('#mainnav');\n      header.style.height = height + 'px';\n    }\n\n    /**\n     * Because salla caches the response, it's important to keep the alert disabled if the visitor closed it.\n     * by store the status of the ad in local storage `salla.storage.set(...)`\n     */\n  }, {\n    key: \"initiateAdAlert\",\n    value: function initiateAdAlert() {\n      var ad = this.element(\".salla-advertisement\");\n      if (!ad) {\n        return;\n      }\n      if (!salla.storage.get('statusAd-' + ad.dataset.id)) {\n        ad.classList.remove('hidden');\n      }\n      this.onClick('.ad-close', function (event) {\n        event.preventDefault();\n        salla.storage.set('statusAd-' + ad.dataset.id, 'dismissed');\n        anime({\n          targets: '.salla-advertisement',\n          opacity: [1, 0],\n          duration: 300,\n          height: [ad.clientHeight, 0],\n          easing: 'easeInOutQuad'\n        });\n      });\n    }\n  }, {\n    key: \"initiateDropdowns\",\n    value: function initiateDropdowns() {\n      // إزالة المستمعين القدامى لتجنب التكرار\n      document.removeEventListener('click', this.handleDropdownClick);\n      this.handleDropdownClick = function (event) {\n        var btn = event.target.closest('.dropdown__trigger');\n        if (btn) {\n          event.preventDefault();\n          event.stopPropagation();\n\n          // إغلاق جميع الـ dropdowns الأخرى\n          document.querySelectorAll('.dropdown-toggler.is-opened').forEach(function (dropdown) {\n            if (dropdown !== btn.parentElement) {\n              dropdown.classList.remove('is-opened');\n            }\n          });\n\n          // تبديل حالة الـ dropdown الحالي\n          btn.parentElement.classList.toggle('is-opened');\n          document.body.classList.toggle('dropdown--is-opened', document.querySelector('.dropdown-toggler.is-opened') !== null);\n        } else {\n          // النقر خارج الـ dropdown\n          var clickedInsideMenu = event.target.closest('.dropdown__menu');\n          var clickedCloseBtn = event.target.classList.contains('dropdown__close');\n          if (!clickedInsideMenu || clickedCloseBtn) {\n            document.querySelectorAll('.dropdown-toggler.is-opened').forEach(function (dropdown) {\n              dropdown.classList.remove('is-opened');\n            });\n            document.body.classList.remove('dropdown--is-opened');\n          }\n        }\n      };\n      document.addEventListener('click', this.handleDropdownClick);\n\n      // إضافة مستمع للـ escape key\n      document.addEventListener('keydown', function (event) {\n        if (event.key === 'Escape') {\n          document.querySelectorAll('.dropdown-toggler.is-opened').forEach(function (dropdown) {\n            dropdown.classList.remove('is-opened');\n          });\n          document.body.classList.remove('dropdown--is-opened');\n        }\n      });\n    }\n  }, {\n    key: \"initiateModals\",\n    value: function initiateModals() {\n      var _this5 = this;\n      this.onClick('[data-modal-trigger]', function (e) {\n        var id = '#' + e.target.dataset.modalTrigger;\n        _this5.removeClass(id, 'hidden');\n        setTimeout(function () {\n          return _this5.toggleModal(id, true);\n        }); //small amont of time to running toggle After adding hidden\n      });\n      salla.event.document.onClick(\"[data-close-modal]\", function (e) {\n        return _this5.toggleModal('#' + e.target.dataset.closeModal, false);\n      });\n    }\n  }, {\n    key: \"toggleModal\",\n    value: function toggleModal(id, isOpen) {\n      var _this6 = this;\n      this.toggleClassIf(\"\".concat(id, \" .s-salla-modal-overlay\"), 'ease-out duration-300 opacity-100', 'opacity-0', function () {\n        return isOpen;\n      }).toggleClassIf(\"\".concat(id, \" .s-salla-modal-body\"), 'ease-out duration-300 opacity-100 translate-y-0 sm:scale-100',\n      //add these classes\n      'opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95',\n      //remove these classes\n      function () {\n        return isOpen;\n      }).toggleElementClassIf(document.body, 'modal-is-open', 'modal-is-closed', function () {\n        return isOpen;\n      });\n      if (!isOpen) {\n        setTimeout(function () {\n          return _this6.addClass(id, 'hidden');\n        }, 350);\n      }\n    }\n  }, {\n    key: \"initiateCollapse\",\n    value: function initiateCollapse() {\n      var _this7 = this;\n      document.querySelectorAll('.btn--collapse').forEach(function (trigger) {\n        var content = document.querySelector('#' + trigger.dataset.show);\n        var state = {\n          isOpen: false\n        };\n        var onOpen = function onOpen() {\n          return anime({\n            targets: content,\n            duration: 225,\n            height: content.scrollHeight,\n            opacity: [0, 1],\n            easing: 'easeOutQuart'\n          });\n        };\n        var onClose = function onClose() {\n          return anime({\n            targets: content,\n            duration: 225,\n            height: 0,\n            opacity: [1, 0],\n            easing: 'easeOutQuart'\n          });\n        };\n        var toggleState = function toggleState(isOpen) {\n          state.isOpen = !isOpen;\n          _this7.toggleElementClassIf([content, trigger], 'is-closed', 'is-opened', function () {\n            return isOpen;\n          });\n        };\n        trigger.addEventListener('click', function () {\n          var isOpen = state.isOpen;\n          toggleState(isOpen);\n          isOpen ? onClose() : onOpen();\n        });\n      });\n    }\n\n    /**\n     * Workaround for seeking to simplify & clean, There are three ways to use this method:\n     * 1- direct call: `this.anime('.my-selector')` - will use default values\n     * 2- direct call with overriding defaults: `this.anime('.my-selector', {duration:3000})`\n     * 3- return object to play it letter: `this.anime('.my-selector', false).duration(3000).play()` - will not play animation unless calling play method.\n     * @param {string|HTMLElement} selector\n     * @param {object|undefined|null|null} options - in case there is need to set attributes one by one set it `false`;\n     * @return {Anime|*}\n     */\n  }, {\n    key: \"anime\",\n    value: function anime(selector) {\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n      var anime = new _partials_anime__WEBPACK_IMPORTED_MODULE_8__[\"default\"](selector, options);\n      return options === false ? anime : anime.play();\n    }\n\n    /**\n     * These actions are responsible for pressing \"add to cart\" button,\n     * they can be from any page, especially when mega-menu is enabled\n     */\n  }, {\n    key: \"initAddToCart\",\n    value: function initAddToCart() {\n      salla.cart.event.onUpdated(function (summary) {\n        document.querySelectorAll('[data-cart-total]').forEach(function (el) {\n          return el.innerHTML = salla.money(summary.total);\n        });\n        document.querySelectorAll('[data-cart-count]').forEach(function (el) {\n          return el.innerText = salla.helpers.number(summary.count);\n        });\n      });\n      salla.cart.event.onItemAdded(function (response, prodId) {\n        app.element('salla-cart-summary').animateToCart(app.element(\"#product-\".concat(prodId, \" img\")));\n      });\n    }\n  }]);\n}(_app_helpers__WEBPACK_IMPORTED_MODULE_10__[\"default\"]);\nsalla.onReady(function () {\n  return new App().loadTheApp();\n});\ndocument.addEventListener('DOMContentLoaded', function () {\n  console.log('تم تحميل تطبيق المتجر بنجاح');\n});\n\n//# sourceURL=webpack://theme-raed/./src/assets/js/app.js?");

/***/ }),

/***/ "./src/assets/js/base-page.js":
/*!************************************!*\
  !*** ./src/assets/js/base-page.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/classCallCheck */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/classCallCheck.js\");\n/* harmony import */ var _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/createClass */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/createClass.js\");\n\n\nvar BasePage = /*#__PURE__*/function () {\n  function BasePage() {\n    (0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this, BasePage);\n  }\n  return (0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(BasePage, [{\n    key: \"onReady\",\n    value: function onReady() {\n      //\n    }\n  }, {\n    key: \"registerEvents\",\n    value: function registerEvents() {\n      //\n    }\n\n    /**\r\n     * To avoid loading unwanted classes, unless it's wanted page\r\n     * @param {null|string[]} allowedPages\r\n     * @return {*}\r\n     */\n  }, {\n    key: \"initiate\",\n    value: function initiate(allowedPages) {\n      if (allowedPages && !allowedPages.includes(salla.config.get('page.slug'))) {\n        return app.log(\"The Class For (\".concat(allowedPages.join(','), \") Skipped.\"));\n      }\n      this.onReady();\n      this.registerEvents();\n      app.log(\"The Class For (\".concat((allowedPages === null || allowedPages === void 0 ? void 0 : allowedPages.join(',')) || '*', \") Loaded\\uD83C\\uDF89\"));\n    }\n  }]);\n}();\n/**\r\n * Because we merged multi classes into one file, there is no need to initiate all of them\r\n */\nBasePage.initiateWhenReady = function () {\n  var _window$app,\n    _this = this;\n  var allowedPages = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n  if (((_window$app = window.app) === null || _window$app === void 0 ? void 0 : _window$app.status) === 'ready') {\n    new this().initiate(allowedPages);\n  } else {\n    document.addEventListener('theme::ready', function () {\n      return new _this().initiate(allowedPages);\n    });\n  }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BasePage);\n\n//# sourceURL=webpack://theme-raed/./src/assets/js/base-page.js?");

/***/ }),

/***/ "./src/assets/js/blog.js":
/*!*******************************!*\
  !*** ./src/assets/js/blog.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\");\n/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\");\n/* harmony import */ var _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/classCallCheck */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/classCallCheck.js\");\n/* harmony import */ var _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/createClass */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/createClass.js\");\n/* harmony import */ var _babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/possibleConstructorReturn */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/possibleConstructorReturn.js\");\n/* harmony import */ var _babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/getPrototypeOf */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/getPrototypeOf.js\");\n/* harmony import */ var _babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @babel/runtime/helpers/inherits */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/inherits.js\");\n/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @babel/runtime/regenerator */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/regenerator/index.js\");\n/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _base_page__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./base-page */ \"./src/assets/js/base-page.js\");\n\n\n\n\n\n\n\n\nfunction _callSuper(t, o, e) { return o = (0,_babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(o), (0,_babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0,_babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\n\nvar Blog = /*#__PURE__*/function (_BasePage) {\n  function Blog() {\n    (0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, Blog);\n    return _callSuper(this, Blog, arguments);\n  }\n  (0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(Blog, _BasePage);\n  return (0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(Blog, [{\n    key: \"onReady\",\n    value: function onReady() {\n      this.initToggleLike();\n    }\n  }, {\n    key: \"initToggleLike\",\n    value: function initToggleLike() {\n      var _this = this;\n      var likeBtn = document.querySelector('#blog-like');\n      if (!likeBtn || !salla.url.is_page('blog.single')) {\n        return;\n      }\n      var blogId = likeBtn.dataset.blogId;\n      var likedBlogs = JSON.parse(localStorage.getItem('liked_blogs')) || [];\n      this.isLiked = likedBlogs.includes(blogId);\n      if (this.isLiked) {\n        likeBtn.classList.add('liked');\n      }\n      likeBtn.addEventListener('click', /*#__PURE__*/function () {\n        var _ref = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_7___default().mark(function _callee(event) {\n          var originalContent, endpoint, _e$response;\n          return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_7___default().wrap(function _callee$(_context) {\n            while (1) switch (_context.prev = _context.next) {\n              case 0:\n                event.preventDefault();\n                if (!salla.config.isGuest()) {\n                  _context.next = 3;\n                  break;\n                }\n                return _context.abrupt(\"return\", salla.notify.error(salla.lang.get('common.messages.must_login')));\n              case 3:\n                originalContent = likeBtn.innerHTML;\n                likeBtn.querySelector('i').outerHTML = '<span class=\"loader loader--small\"></span>';\n                endpoint = _this.isLiked ? \"blogs/\".concat(blogId, \"/unlike\") : \"blogs/\".concat(blogId, \"/like\");\n                _context.prev = 6;\n                _context.next = 9;\n                return salla.api.request(endpoint, '', _this.isLiked ? 'delete' : 'put');\n              case 9:\n                likeBtn.innerHTML = originalContent;\n                _this.updateLikedBlogs(blogId, !_this.isLiked);\n                _this.updateLikesCount(!_this.isLiked);\n                _this.isLiked = !_this.isLiked;\n                _context.next = 19;\n                break;\n              case 15:\n                _context.prev = 15;\n                _context.t0 = _context[\"catch\"](6);\n                likeBtn.innerHTML = originalContent;\n                if (((_e$response = _context.t0.response) === null || _e$response === void 0 ? void 0 : _e$response.status) === 409) {\n                  _this.handleExistingLike(likeBtn, blogId);\n                }\n              case 19:\n              case \"end\":\n                return _context.stop();\n            }\n          }, _callee, null, [[6, 15]]);\n        }));\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }());\n    }\n  }, {\n    key: \"handleExistingLike\",\n    value: function handleExistingLike(likeBtn, blogId) {\n      var isLiked = likeBtn.classList.contains('liked');\n      this.updateLikedBlogs(blogId, !isLiked);\n      this.updateLikesCount(!isLiked);\n      this.isLiked = !isLiked;\n    }\n  }, {\n    key: \"updateLikedBlogs\",\n    value: function updateLikedBlogs(blogId, add) {\n      var likedBlogs = JSON.parse(localStorage.getItem('liked_blogs')) || [];\n      var updatedBlogs = add ? [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(likedBlogs), [blogId]) : likedBlogs.filter(function (id) {\n        return id !== blogId;\n      });\n      localStorage.setItem('liked_blogs', JSON.stringify(updatedBlogs));\n    }\n  }, {\n    key: \"updateLikesCount\",\n    value: function updateLikesCount(isLiked) {\n      var likeButton = document.querySelector('#blog-like');\n      var countSpan = likeButton.querySelector('span');\n      var currentCount = parseInt(countSpan === null || countSpan === void 0 ? void 0 : countSpan.innerText) || 0;\n      likeButton.classList.toggle(\"liked\", isLiked);\n      anime({\n        targets: countSpan,\n        innerHTML: isLiked ? currentCount + 1 : currentCount - 1,\n        duration: 400,\n        round: 1,\n        easing: 'easeOutExpo',\n        complete: function complete() {\n          countSpan.removeAttribute('style');\n        }\n      });\n      anime({\n        targets: countSpan,\n        scale: [1, 1.2],\n        duration: 300,\n        easing: 'easeInOutQuad'\n      });\n    }\n  }]);\n}(_base_page__WEBPACK_IMPORTED_MODULE_8__[\"default\"]);\nBlog.initiateWhenReady(['blog.single', 'blog.index']);\n\n//# sourceURL=webpack://theme-raed/./src/assets/js/blog.js?");

/***/ }),

/***/ "./src/assets/js/main.js":
/*!*******************************!*\
  !*** ./src/assets/js/main.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _partials_category_search_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./partials/category-search.js */ \"./src/assets/js/partials/category-search.js\");\n/* harmony import */ var _partials_category_filter_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./partials/category-filter.js */ \"./src/assets/js/partials/category-filter.js\");\n// استيراد العناصر المخصصة\n\n\n\n/**\r\n * تحسينات الإصدار 1.3.0:\r\n * - إصلاح خطأ SourceValue بإرسال قيمة عددية\r\n * - معالجة خطأ نقاط نهاية API غير المتوفرة (410 Gone)\r\n * - تبسيط استراتيجية البحث للتركيز على الطرق الأكثر موثوقية\r\n * - تحسين البحث المحلي بتنقية النتائج بناءً على صلتها بالفئة\r\n * - دعم مختلف إصدارات منصة سلة\r\n */\n\n// إضافة العناصر المخصصة إلى الهيدر بعد تحميل المستند\ndocument.addEventListener('DOMContentLoaded', function () {\n  console.log('تم تحميل مكونات البحث والفلترة المحسّنة - الإصدار 1.3.0');\n  console.log('تم إصلاح مشكلة ظهور المنتجات في الفئات غير المناسبة ومعالجة أخطاء API غير المتوفر');\n\n  // إضافة عنصر البحث بالفئات\n  var headerSearch = document.querySelector('.header .S-search');\n  if (headerSearch) {\n    // استبدال عنصر البحث الافتراضي بعنصر البحث المخصص\n    var categorySearch = document.createElement('category-search');\n    headerSearch.innerHTML = '';\n    headerSearch.appendChild(categorySearch);\n  }\n\n  // إضافة عنصر فلترة الفئات\n  var headerElement = document.querySelector('.header .container') || document.querySelector('.header');\n  if (headerElement) {\n    var categoryFilter = document.createElement('category-filter');\n\n    // إذا كان هناك قائمة في الهيدر، نضيف العنصر بعدها\n    var headerNav = document.querySelector('.header .main-nav');\n    if (headerNav) {\n      headerNav.parentNode.insertBefore(categoryFilter, headerNav.nextSibling);\n    } else {\n      // إضافة العنصر في نهاية الهيدر\n      headerElement.appendChild(categoryFilter);\n    }\n  }\n});\n\n//# sourceURL=webpack://theme-raed/./src/assets/js/main.js?");

/***/ }),

/***/ "./src/assets/js/partials/anime.js":
/*!*****************************************!*\
  !*** ./src/assets/js/partials/anime.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/classCallCheck */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/classCallCheck.js\");\n/* harmony import */ var _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/createClass */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/createClass.js\");\n/* harmony import */ var animejs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! animejs */ \"./node_modules/.pnpm/animejs@3.2.2/node_modules/animejs/lib/anime.es.js\");\n\n\n\nwindow.anime = animejs__WEBPACK_IMPORTED_MODULE_2__[\"default\"];\nvar Anime = /*#__PURE__*/function () {\n  function Anime(selector, options) {\n    (0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this, Anime);\n    this.options = {\n      targets: selector,\n      opacity: [0, 1],\n      delay: function delay(el, i) {\n        return i * 100;\n      },\n      duration: 2000\n    };\n    this.setOptions(options);\n  }\n\n  /**\r\n   * @param options\r\n   * @return {Anime}\r\n   */\n  return (0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(Anime, [{\n    key: \"setOptions\",\n    value: function setOptions(options) {\n      this.options = Object.assign(this.options, options || {});\n      return this;\n    }\n\n    /**\r\n     * @param duration\r\n     * @return {Anime}\r\n     */\n  }, {\n    key: \"duration\",\n    value: function duration(_duration) {\n      return this.set('duration', _duration);\n    }\n\n    /**\r\n     * @param opacity\r\n     * @return {Anime}\r\n     */\n  }, {\n    key: \"opacity\",\n    value: function opacity(_opacity) {\n      return this.set('opacity', _opacity);\n    }\n\n    /**\r\n     * @param delay\r\n     * @return {Anime}\r\n     */\n  }, {\n    key: \"delay\",\n    value: function delay(_delay) {\n      return this.set('delay', _delay);\n    }\n\n    /**\r\n     * @param scale\r\n     * @return {Anime}\r\n     */\n  }, {\n    key: \"scale\",\n    value: function scale(_scale) {\n      return this.set('scale', _scale);\n    }\n\n    /**\r\n     * @param translateY\r\n     * @return {Anime}\r\n     */\n  }, {\n    key: \"translateY\",\n    value: function translateY(_translateY) {\n      return this.set('translateY', _translateY);\n    }\n\n    /**\r\n     * @param translateX\r\n     * @return {Anime}\r\n     */\n  }, {\n    key: \"translateX\",\n    value: function translateX(_translateX) {\n      return this.set('translateX', _translateX);\n    }\n\n    /**\r\n     * @param height\r\n     * @return {Anime}\r\n     */\n  }, {\n    key: \"height\",\n    value: function height(_height) {\n      return this.set('height', _height);\n    }\n\n    /**\r\n     * @param margin\r\n     * @return {Anime}\r\n     */\n  }, {\n    key: \"margin\",\n    value: function margin(_margin) {\n      return this.set('margin', _margin);\n    }\n\n    /**\r\n     * @param easing\r\n     * @return {Anime}\r\n     */\n  }, {\n    key: \"easing\",\n    value: function easing(_easing) {\n      return this.set('easing', _easing);\n    }\n\n    /**\r\n     * @param complete\r\n     * @return {Anime}\r\n     */\n  }, {\n    key: \"complete\",\n    value: function complete(_complete) {\n      return this.set('complete', _complete);\n    }\n\n    /**\r\n     * @param key\r\n     * @param value\r\n     * @return {Anime}\r\n     */\n  }, {\n    key: \"set\",\n    value: function set(key, value) {\n      this.options[key] = value;\n      return this;\n    }\n\n    /**\r\n     * @param number\r\n     * @return {Anime}\r\n     */\n  }, {\n    key: \"stagger\",\n    value: function stagger(number) {\n      this.delay = animejs__WEBPACK_IMPORTED_MODULE_2__[\"default\"].stagger(number);\n      return this;\n    }\n\n    /**\r\n     * @param padding\r\n     * @return {Anime}\r\n     */\n  }, {\n    key: \"paddingBottom\",\n    value: function paddingBottom(padding) {\n      return this.set('padding-bottom', padding);\n    }\n\n    /**\r\n     * @param padding\r\n     * @return {Anime}\r\n     */\n  }, {\n    key: \"paddingTop\",\n    value: function paddingTop(padding) {\n      return this.set('padding-top', padding);\n    }\n\n    /**\r\n     * @return {{}}\r\n     */\n  }, {\n    key: \"play\",\n    value: function play() {\n      return (0,animejs__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this.options);\n    }\n  }]);\n}();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Anime);\n\n//# sourceURL=webpack://theme-raed/./src/assets/js/partials/anime.js?");

/***/ }),

/***/ "./src/assets/js/partials/category-filter.js":
/*!***************************************************!*\
  !*** ./src/assets/js/partials/category-filter.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\");\n/* harmony import */ var _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/classCallCheck */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/classCallCheck.js\");\n/* harmony import */ var _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/createClass */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/createClass.js\");\n/* harmony import */ var _babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/possibleConstructorReturn */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/possibleConstructorReturn.js\");\n/* harmony import */ var _babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/getPrototypeOf */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/getPrototypeOf.js\");\n/* harmony import */ var _babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/inherits */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/inherits.js\");\n/* harmony import */ var _babel_runtime_helpers_wrapNativeSuper__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @babel/runtime/helpers/wrapNativeSuper */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/wrapNativeSuper.js\");\n\n\n\n\n\n\nfunction _createForOfIteratorHelper(r, e) { var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t[\"return\"] || t[\"return\"](); } finally { if (u) throw o; } } }; }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _callSuper(t, o, e) { return o = (0,_babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(o), (0,_babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0,_babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\n\n\n\n\n/**\n * مكون فلترة حسب الفئات\n * يعرض أيقونات الفئات في الهيدر ويظهر المنتجات لكل فئة عند النقر عليها\n */\nvar CategoryFilter = /*#__PURE__*/function (_wrapNativeSuper) {\n  function CategoryFilter() {\n    var _this;\n    (0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(this, CategoryFilter);\n    _this = _callSuper(this, CategoryFilter);\n    (0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_this, CategoryFilter);\n    _this.categories = [];\n    _this.products = {};\n    _this.activeCategory = null;\n\n    // تأخير التهيئة حتى تحميل سلة\n    if (window.salla) {\n      _this.initComponent();\n    } else {\n      document.addEventListener('salla::ready', function () {\n        return _this.initComponent();\n      });\n    }\n    return _this;\n  }\n\n  /**\n   * تهيئة المكون\n   */\n  (0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(CategoryFilter, _wrapNativeSuper);\n  return (0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(CategoryFilter, [{\n    key: \"initComponent\",\n    value: function initComponent() {\n      // إضافة الأنماط مباشرة للتأكد من عملها\n      var styleEl = document.createElement('style');\n      styleEl.textContent = \"\\n      .category-filter-container {\\n        display: flex;\\n        flex-wrap: nowrap;\\n        gap: 0.75rem;\\n        align-items: center;\\n        margin: 0.5rem 0;\\n        overflow-x: auto;\\n        scrollbar-width: none;\\n        -ms-overflow-style: none;\\n        padding: 0.5rem 0;\\n        scroll-behavior: smooth;\\n      }\\n      .category-filter-container::-webkit-scrollbar {\\n        display: none;\\n      }\\n      .category-icon {\\n        display: flex;\\n        flex-direction: column;\\n        align-items: center;\\n        padding: 0.5rem;\\n        border-radius: 0.75rem;\\n        background-color: #f9fafb;\\n        border: 1px solid #e5e7eb;\\n        cursor: pointer;\\n        transition: all 0.3s;\\n        min-width: 80px;\\n        text-align: center;\\n        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\\n        flex: 0 0 auto;\\n      }\\n      .category-icon:hover {\\n        background-color: #f3f4f6;\\n        border-color: rgba(var(--primary-color), 0.3);\\n        transform: translateY(-2px);\\n        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\\n      }\\n      .category-icon.active {\\n        background-color: rgba(var(--primary-color), 0.1);\\n        border-color: rgba(var(--primary-color), 0.4);\\n        transform: translateY(-2px);\\n        box-shadow: 0 4px 10px -2px rgba(var(--primary-color), 0.2);\\n      }\\n      .category-icon-image {\\n        width: 50px;\\n        height: 50px;\\n        display: flex;\\n        align-items: center;\\n        justify-content: center;\\n        margin-bottom: 0.5rem;\\n        background-color: #fff;\\n        border-radius: 50%;\\n        padding: 0.5rem;\\n        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\\n      }\\n      .category-icon-image img {\\n        max-width: 100%;\\n        max-height: 100%;\\n        object-fit: contain;\\n      }\\n      .category-icon-name {\\n        font-size: 0.8rem;\\n        color: #4b5563;\\n        max-width: 80px;\\n        overflow: hidden;\\n        text-overflow: ellipsis;\\n        white-space: nowrap;\\n        transition: color 0.3s;\\n      }\\n      .category-icon:hover .category-icon-name,\\n      .category-icon.active .category-icon-name {\\n        color: rgba(var(--primary-color), 1);\\n        font-weight: 500;\\n      }\\n      .category-products {\\n        display: flex;\\n        flex-direction: column;\\n        margin-top: 1rem;\\n        overflow: hidden;\\n        max-height: 0;\\n        transition: max-height 0.4s ease-out, opacity 0.3s ease-out;\\n        opacity: 0;\\n        border-radius: 0.75rem;\\n        background-color: #f9fafb;\\n        border: 1px solid #e5e7eb;\\n      }\\n      .category-products.active {\\n        max-height: 2000px;\\n        opacity: 1;\\n        transition: max-height 0.6s ease-in, opacity 0.4s ease-in 0.1s;\\n      }\\n      \\n      /* \\u062A\\u062D\\u0633\\u064A\\u0646 \\u062A\\u0635\\u0645\\u064A\\u0645 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A */\\n      .category-products-header {\\n        padding: 1rem 1rem 0.5rem;\\n        border-bottom: 1px solid rgba(var(--primary-color), 0.1);\\n        text-align: center;\\n        background: linear-gradient(to right, rgba(var(--primary-color), 0.05), rgba(var(--primary-color), 0.1));\\n      }\\n      \\n      .category-products-title {\\n        font-size: 1.25rem;\\n        font-weight: 600;\\n        color: rgba(var(--primary-color), 1);\\n        margin: 0;\\n      }\\n      \\n      .category-products-subtitle {\\n        font-size: 0.875rem;\\n        color: #4b5563;\\n        margin-top: 0.25rem;\\n      }\\n      \\n      .products-grid {\\n        display: grid;\\n        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));\\n        gap: 1rem;\\n        padding: 1rem;\\n      }\\n      \\n      .product-card {\\n        border-radius: 0.75rem;\\n        overflow: hidden;\\n        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);\\n        transition: all 0.3s;\\n        background-color: #fff;\\n        border: 1px solid #f3f4f6;\\n        position: relative;\\n      }\\n      .product-card:hover {\\n        transform: translateY(-3px);\\n        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);\\n        border-color: rgba(var(--primary-color), 0.2);\\n      }\\n      .product-image {\\n        height: 140px;\\n        background-color: #f9fafb;\\n        display: flex;\\n        align-items: center;\\n        justify-content: center;\\n        overflow: hidden;\\n        position: relative;\\n      }\\n      .product-image img {\\n        max-width: 100%;\\n        max-height: 100%;\\n        object-fit: contain;\\n        transition: transform 0.5s;\\n      }\\n      .product-card:hover .product-image img {\\n        transform: scale(1.05);\\n      }\\n      .product-details {\\n        padding: 0.75rem;\\n      }\\n      .product-name {\\n        font-size: 0.875rem;\\n        font-weight: 500;\\n        margin-bottom: 0.5rem;\\n        color: #1f2937;\\n        overflow: hidden;\\n        text-overflow: ellipsis;\\n        white-space: nowrap;\\n        transition: color 0.3s;\\n      }\\n      .product-card:hover .product-name {\\n        color: rgba(var(--primary-color), 1);\\n      }\\n      .product-price {\\n        font-size: 0.875rem;\\n        color: rgba(var(--primary-color), 1);\\n        font-weight: 600;\\n      }\\n      \\n      /* \\u0645\\u0624\\u062B\\u0631\\u0627\\u062A \\u062C\\u062F\\u064A\\u062F\\u0629 */\\n      .product-actions {\\n        position: absolute;\\n        bottom: 0;\\n        left: 0;\\n        right: 0;\\n        display: flex;\\n        justify-content: center;\\n        padding: 0.5rem;\\n        background: linear-gradient(to top, rgba(0,0,0,0.5), transparent);\\n        opacity: 0;\\n        transform: translateY(100%);\\n        transition: all 0.3s ease;\\n      }\\n      \\n      .product-card:hover .product-actions {\\n        opacity: 1;\\n        transform: translateY(0);\\n      }\\n      \\n      .add-to-cart-btn {\\n        width: 36px;\\n        height: 36px;\\n        border-radius: 50%;\\n        background-color: rgba(var(--primary-color), 1);\\n        color: white;\\n        border: none;\\n        display: flex;\\n        align-items: center;\\n        justify-content: center;\\n        cursor: pointer;\\n        transition: all 0.2s;\\n        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\\n      }\\n      \\n      .add-to-cart-btn:hover {\\n        transform: scale(1.1);\\n        background-color: rgba(var(--primary-color), 0.9);\\n      }\\n      \\n      .view-more-btn {\\n        padding: 0.5rem 1.25rem;\\n        background-color: rgba(var(--primary-color), 1);\\n        color: white;\\n        border-radius: 0.5rem;\\n        text-align: center;\\n        transition: all 0.3s;\\n        font-weight: 500;\\n        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\\n        display: inline-block;\\n      }\\n      \\n      .view-more-btn:hover {\\n        background-color: rgba(var(--primary-color), 0.9);\\n        transform: translateY(-2px);\\n        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);\\n      }\\n      \\n      /* \\u0625\\u0634\\u0639\\u0627\\u0631\\u0627\\u062A \\u0627\\u0644\\u0633\\u0644\\u0629 */\\n      .cart-notification {\\n        position: fixed;\\n        top: 20px;\\n        right: 20px;\\n        z-index: 9999;\\n        background-color: #fff;\\n        border-radius: 8px;\\n        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);\\n        padding: 1rem;\\n        transform: translateX(120%);\\n        transition: transform 0.3s ease;\\n        max-width: 300px;\\n        border-left: 4px solid rgba(var(--primary-color), 1);\\n      }\\n      \\n      .cart-notification.show {\\n        transform: translateX(0);\\n      }\\n      \\n      .cart-notification-content {\\n        display: flex;\\n        align-items: center;\\n      }\\n      \\n      .cart-notification-icon {\\n        font-size: 1.5rem;\\n        color: #10b981;\\n        margin-right: 0.75rem;\\n      }\\n      \\n      .cart-notification-text p {\\n        margin: 0 0 0.25rem 0;\\n        color: #4b5563;\\n      }\\n      \\n      .cart-notification-text strong {\\n        color: #1f2937;\\n        font-size: 0.9rem;\\n        word-break: break-word;\\n      }\\n      \\n      .loading-spinner {\\n        display: inline-block;\\n        width: 2rem;\\n        height: 2rem;\\n        border-radius: 50%;\\n        border: 2px solid rgba(var(--primary-color), 0.3);\\n        border-top-color: rgba(var(--primary-color), 1);\\n        animation: spin 1s linear infinite;\\n      }\\n      @keyframes spin {\\n        to { transform: rotate(360deg); }\\n      }\\n      .loading-container {\\n        display: flex;\\n        justify-content: center;\\n        padding: 1.5rem;\\n      }\\n      .category-filter-wrapper {\\n        position: relative;\\n      }\\n      .category-scroll-buttons {\\n        display: none;\\n      }\\n      @media (min-width: 640px) {\\n        .category-scroll-buttons {\\n          display: block;\\n          position: absolute;\\n          top: 0;\\n          bottom: 0;\\n          width: 40px;\\n          display: flex;\\n          align-items: center;\\n          justify-content: center;\\n          background: linear-gradient(90deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.9) 70%, rgba(255,255,255,0) 100%);\\n          z-index: 1;\\n          opacity: 0;\\n          transition: opacity 0.2s;\\n          cursor: pointer;\\n        }\\n        .category-scroll-buttons.show {\\n          opacity: 1;\\n        }\\n        .category-scroll-buttons.left {\\n          left: 0;\\n        }\\n        .category-scroll-buttons.right {\\n          right: 0;\\n          transform: rotate(180deg);\\n          background: linear-gradient(270deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.9) 70%, rgba(255,255,255,0) 100%);\\n        }\\n        .category-scroll-buttons i {\\n          font-size: 1.5rem;\\n          color: rgba(var(--primary-color), 0.8);\\n        }\\n      }\\n      \\n      /* \\u0623\\u0646\\u0645\\u0627\\u0637 \\u062C\\u062F\\u064A\\u062F\\u0629 \\u0644\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \\u0627\\u0644\\u0639\\u0627\\u0645\\u0629 */\\n      .generic-product {\\n        opacity: 0.8;\\n        border: 1px dashed #e5e7eb;\\n        position: relative;\\n      }\\n      .generic-product::before {\\n        content: '';\\n        position: absolute;\\n        top: 0;\\n        left: 0;\\n        right: 0;\\n        height: 4px;\\n        background-color: #f3f4f6;\\n        z-index: 1;\\n      }\\n      .generic-product:hover {\\n        opacity: 1;\\n      }\\n      .generic-product .product-name {\\n        font-style: italic;\\n      }\\n      \\n      /* \\u062A\\u062D\\u0633\\u064A\\u0646 \\u0631\\u0633\\u0627\\u0644\\u0629 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \\u0627\\u0644\\u0639\\u0627\\u0645\\u0629 */\\n      .col-span-full {\\n        grid-column: 1 / -1;\\n        width: 100%;\\n      }\\n      \\n      /* \\u062A\\u062D\\u0633\\u064A\\u0646\\u0627\\u062A \\u0644\\u0644\\u0645\\u0648\\u0628\\u0627\\u064A\\u0644 */\\n      @media (max-width: 640px) {\\n        .products-grid {\\n          grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));\\n          gap: 0.75rem;\\n          padding: 0.75rem;\\n        }\\n        \\n        .product-image {\\n          height: 110px;\\n        }\\n        \\n        .category-products-title {\\n          font-size: 1.1rem;\\n        }\\n        \\n        .category-products-subtitle {\\n          font-size: 0.8rem;\\n        }\\n      }\\n    \";\n      this.appendChild(styleEl);\n\n      // إنشاء هيكل المكون\n      var containerDiv = document.createElement('div');\n      containerDiv.className = 'category-filter-wrapper';\n      containerDiv.innerHTML = \"\\n      <div class=\\\"category-scroll-buttons left\\\">\\n        <i class=\\\"sicon-keyboard-arrow-left\\\"></i>\\n      </div>\\n      <div class=\\\"category-filter-container\\\" dir=\\\"\".concat(document.dir || 'rtl', \"\\\"></div>\\n      <div class=\\\"category-scroll-buttons right\\\">\\n        <i class=\\\"sicon-keyboard-arrow-right\\\"></i>\\n      </div>\\n      <div class=\\\"category-products-container\\\"></div>\\n    \");\n      this.appendChild(containerDiv);\n\n      // الحصول على العناصر\n      this.filterContainer = this.querySelector('.category-filter-container');\n      this.productsContainer = this.querySelector('.category-products-container');\n      this.scrollLeftBtn = this.querySelector('.category-scroll-buttons.left');\n      this.scrollRightBtn = this.querySelector('.category-scroll-buttons.right');\n\n      // إضافة مستمعات أزرار التمرير\n      this.scrollLeftBtn.addEventListener('click', this.scrollCategories.bind(this, 'left'));\n      this.scrollRightBtn.addEventListener('click', this.scrollCategories.bind(this, 'right'));\n\n      // مراقبة وضع التمرير لإظهار/إخفاء أزرار التمرير\n      this.filterContainer.addEventListener('scroll', this.updateScrollButtons.bind(this));\n      window.addEventListener('resize', this.updateScrollButtons.bind(this));\n\n      // جلب الفئات\n      this.loadCategories();\n      console.log('تم تهيئة مكون فلترة الفئات');\n    }\n  }, {\n    key: \"scrollCategories\",\n    value: function scrollCategories(direction) {\n      var container = this.filterContainer;\n      var scrollAmount = container.clientWidth * 0.75;\n      if (direction === 'left') {\n        container.scrollBy({\n          left: -scrollAmount,\n          behavior: 'smooth'\n        });\n      } else {\n        container.scrollBy({\n          left: scrollAmount,\n          behavior: 'smooth'\n        });\n      }\n    }\n  }, {\n    key: \"updateScrollButtons\",\n    value: function updateScrollButtons() {\n      var container = this.filterContainer;\n\n      // عرض/إخفاء زر التمرير يسار\n      if (container.scrollLeft > 20) {\n        this.scrollLeftBtn.classList.add('show');\n      } else {\n        this.scrollLeftBtn.classList.remove('show');\n      }\n\n      // عرض/إخفاء زر التمرير يمين\n      var isScrollable = container.scrollWidth > container.clientWidth;\n      var isAtEnd = container.scrollLeft + container.clientWidth >= container.scrollWidth - 20;\n      if (isScrollable && !isAtEnd) {\n        this.scrollRightBtn.classList.add('show');\n      } else {\n        this.scrollRightBtn.classList.remove('show');\n      }\n    }\n\n    /**\n     * البحث في عناصر المنتجات المتوفرة في الصفحة\n     * @param {NodeList} productElements - عناصر المنتجات\n     * @param {Object} category - الفئة المستهدفة\n     * @param {Array} results - مصفوفة النتائج\n     * @private\n     */\n  }, {\n    key: \"_searchInAvailableProducts\",\n    value: function _searchInAvailableProducts(productElements, category, results) {\n      var _this2 = this;\n      var categoryId = String(category.id);\n      var categoryName = category.name.toLowerCase();\n      productElements.forEach(function (element) {\n        try {\n          var _element$querySelecto, _element$textContent, _element$querySelecto2;\n          // تحسين اختيار اسم المنتج\n          var productName = ((_element$querySelecto = element.querySelector('.product-title, .product-name, h3, a, .name, .title, [itemprop=\"name\"]')) === null || _element$querySelecto === void 0 || (_element$querySelecto = _element$querySelecto.textContent) === null || _element$querySelecto === void 0 ? void 0 : _element$querySelecto.trim()) || ((_element$textContent = element.textContent) === null || _element$textContent === void 0 ? void 0 : _element$textContent.trim()) || '';\n\n          // الحصول على الرابط بطرق مختلفة\n          var productLink = null;\n          var linkElement = element.querySelector('a[href]');\n          if (linkElement) {\n            productLink = linkElement.href;\n          } else if (element.tagName === 'A') {\n            productLink = element.href;\n          } else if (element.dataset.url) {\n            productLink = element.dataset.url;\n          } else if (element.dataset.href) {\n            productLink = element.dataset.href;\n          } else if (element.onclick) {\n            // محاولة استخراج الرابط من وظيفة النقر\n            var onClickStr = element.onclick.toString();\n            var urlMatch = onClickStr.match(/(window\\.location|location\\.href)\\s*=\\s*['\"]([^'\"]+)['\"]/);\n            if (urlMatch && urlMatch[2]) {\n              productLink = urlMatch[2];\n            }\n          }\n\n          // إذا لم نجد رابطًا، تخطي هذا العنصر\n          if (!productLink) return;\n\n          // تحسين اختيار صورة المنتج\n          var productImage = '';\n          var imgElement = element.querySelector('img');\n          if (imgElement) {\n            productImage = imgElement.src || imgElement.dataset.src;\n          } else {\n            // محاولة البحث عن الصورة في الأنماط الخلفية\n            var computedStyle = window.getComputedStyle(element);\n            var backgroundImage = computedStyle.backgroundImage;\n            if (backgroundImage && backgroundImage !== 'none') {\n              var _urlMatch = backgroundImage.match(/url\\(['\"]?([^'\"]+)['\"]?\\)/);\n              if (_urlMatch && _urlMatch[1]) {\n                productImage = _urlMatch[1];\n              }\n            }\n          }\n\n          // تحسين اختيار سعر المنتج\n          var productPrice = ((_element$querySelecto2 = element.querySelector('.product-price, .price, .s-price, [itemprop=\"price\"], .price-wrapper')) === null || _element$querySelecto2 === void 0 ? void 0 : _element$querySelecto2.textContent) || '';\n\n          // التحقق من تطابق المنتج مع الفئة\n          var isRelated = _this2._isProductRelatedToCategory(element, {\n            name: productName,\n            url: productLink\n          }, categoryId, categoryName);\n          if (isRelated) {\n            results.push({\n              name: productName,\n              url: productLink,\n              image: productImage,\n              thumbnail: productImage,\n              price: productPrice,\n              related: true\n            });\n          }\n        } catch (error) {\n          console.error('خطأ في استخراج بيانات المنتج:', error);\n        }\n      });\n    }\n\n    /**\n     * التحقق من تطابق المنتج مع الفئة بطريقة محسنة\n     * @param {Element} element - عنصر المنتج\n     * @param {Object} product - بيانات المنتج\n     * @param {string} categoryId - معرف الفئة\n     * @param {string} categoryName - اسم الفئة\n     * @returns {boolean} - يعيد true إذا كان المنتج مرتبطًا بالفئة\n     * @private\n     */\n  }, {\n    key: \"_isProductRelatedToCategory\",\n    value: function _isProductRelatedToCategory(element, product, categoryId, categoryName) {\n      // فحص السمات المباشرة\n      if (element.dataset.categoryId === categoryId || element.getAttribute('data-category') === categoryId || element.dataset.category === categoryId) {\n        return true;\n      }\n\n      // فحص عناصر الأب\n      var parentWithCategory = element.closest(\"[data-category-id=\\\"\".concat(categoryId, \"\\\"], [data-category=\\\"\").concat(categoryId, \"\\\"], [data-category-slug=\\\"\").concat(categoryName, \"\\\"]\"));\n      if (parentWithCategory) {\n        return true;\n      }\n\n      // فحص URL المنتج\n      if (product.url.includes(\"/category/\".concat(categoryId)) || product.url.includes(\"?category=\".concat(categoryId)) || product.url.includes(\"category_id=\".concat(categoryId)) || product.url.includes(\"/categories/\".concat(categoryId))) {\n        return true;\n      }\n\n      // فحص اسم المنتج مع اسم الفئة\n      var productNameLower = product.name.toLowerCase();\n      if (productNameLower.includes(categoryName)) {\n        return true;\n      }\n\n      // فحص كلمات الفئة في اسم المنتج\n      var categoryWords = categoryName.split(/\\s+/).filter(function (word) {\n        return word.length > 2;\n      });\n      var matchCount = 0;\n      categoryWords.forEach(function (word) {\n        if (productNameLower.includes(word)) {\n          matchCount++;\n        }\n      });\n      if (matchCount > 0 && categoryWords.length > 0) {\n        var matchRatio = matchCount / categoryWords.length;\n        if (matchRatio >= 0.5) {\n          return true;\n        }\n      }\n      return false;\n    }\n\n    /**\n     * معالجة حدث النقر على الفئة\n     * @param {Object} category - الفئة المنقور عليها\n     */\n  }, {\n    key: \"handleCategoryClick\",\n    value: function handleCategoryClick(category) {\n      console.log('تم النقر على الفئة:', category.name);\n\n      // تحديث الفئة النشطة\n      var isActive = this.activeCategory === category.id;\n\n      // إزالة الحالة النشطة من جميع الأيقونات\n      var allIcons = this.querySelectorAll('.category-icon');\n      allIcons.forEach(function (icon) {\n        return icon.classList.remove('active');\n      });\n\n      // إزالة جميع المنتجات النشطة\n      var allProducts = this.querySelectorAll('.category-products');\n      allProducts.forEach(function (products) {\n        products.classList.remove('active');\n        // إزالة العنصر بعد انتهاء التأثير\n        setTimeout(function () {\n          return products.remove();\n        }, 300);\n      });\n\n      // إذا تم النقر على نفس الفئة مرة أخرى، نخرج\n      if (isActive) {\n        this.activeCategory = null;\n        return;\n      }\n\n      // تحديث الفئة النشطة\n      this.activeCategory = category.id;\n\n      // تحديث الأيقونة النشطة\n      var activeIcon = this.querySelector(\".category-icon[data-category-id=\\\"\".concat(category.id, \"\\\"]\"));\n      if (activeIcon) {\n        activeIcon.classList.add('active');\n      }\n\n      // إنشاء حاوية المنتجات\n      var productsDiv = document.createElement('div');\n      productsDiv.className = 'category-products';\n      productsDiv.dataset.categoryId = category.id;\n      this.productsContainer.appendChild(productsDiv);\n\n      // إظهار رسالة التحميل\n      this.showLoading(productsDiv);\n\n      // تأخير قصير ثم عرض المنتجات\n      setTimeout(function () {\n        productsDiv.classList.add('active');\n      }, 10);\n\n      // التحقق مما إذا كانت لدينا منتجات مخزنة لهذه الفئة\n      if (this.products[category.id]) {\n        this.renderProducts(category, this.products[category.id]);\n      } else {\n        // جلب منتجات الفئة\n        this.loadCategoryProducts(category);\n      }\n    }\n\n    /**\n     * استخراج المنتجات من الصفحة الرئيسية للفئة\n     * @param {Object} category - الفئة المستهدفة\n     * @param {Element} productsDiv - حاوية المنتجات\n     */\n  }, {\n    key: \"extractProductsFromHomepage\",\n    value: function extractProductsFromHomepage(category, productsDiv) {\n      console.log('محاولة استخراج المنتجات من الصفحة الرئيسية للفئة:', category.name);\n\n      // منتجات افتراضية للعرض عندما لا يتم العثور على منتجات حقيقية\n      var dummyProducts = [{\n        name: \"\\u0628\\u0637\\u0627\\u0642\\u0629 \\u062C\\u0648\\u062C\\u0644 \\u0628\\u0644\\u0627\\u064A \".concat(category.name),\n        price: \"100 ر.س\",\n        image: \"https://cdn.salla.sa/dummy/vG2YBKzDNBggg46tQO0D6H1QhO8FyGXtMJyFCosC.png\",\n        url: \"#\",\n        isGeneric: true\n      }, {\n        name: \"\\u0628\\u0637\\u0627\\u0642\\u0629 \\u0627\\u064A\\u062A\\u0648\\u0646\\u0632 \".concat(category.name),\n        price: \"150 ر.س\",\n        image: \"https://cdn.salla.sa/dummy/goFsQl9CYL4pJKAoJCQg5YAQZ7oX2QPSHLhHIIwJ.png\",\n        url: \"#\",\n        isGeneric: true\n      }, {\n        name: \"\\u0628\\u0637\\u0627\\u0642\\u0629 \\u0633\\u062A\\u0648\\u0631 \".concat(category.name),\n        price: \"200 ر.س\",\n        image: \"https://cdn.salla.sa/dummy/tXoHFJsROvMJlQptQAqm3Z5nH3nKjmTulpZGaRfK.png\",\n        url: \"#\",\n        isGeneric: true\n      }, {\n        name: \"\\u0628\\u0637\\u0627\\u0642\\u0629 \\u0628\\u0644\\u0627\\u064A\\u0633\\u062A\\u064A\\u0634\\u0646 \".concat(category.name),\n        price: \"250 ر.س\",\n        image: \"https://cdn.salla.sa/dummy/8FsKUxJVsLRdURdMBIEUNbfR2PjeWbkQnRcmxJYr.png\",\n        url: \"#\",\n        isGeneric: true\n      }];\n\n      // البحث عن المنتجات في الصفحة\n      var allProductElements = document.querySelectorAll('.product-card, .product, [data-product-id], .s-product-card, .s-grid-item, .swiper-slide, .card, .item, [id*=\"product\"], [class*=\"product\"]');\n      var realProducts = [];\n\n      // محاولة استخراج المنتجات الفعلية\n      this._searchInAvailableProducts(allProductElements, category, realProducts);\n      this.extractProductsFromSections(category, realProducts);\n\n      // استخدام المنتجات الحقيقية إذا وجدت، وإلا استخدام المنتجات الافتراضية\n      if (realProducts.length > 0) {\n        var uniqueProducts = this.removeDuplicateProducts(realProducts);\n        console.log(\"\\u062A\\u0645 \\u0627\\u0644\\u0639\\u062B\\u0648\\u0631 \\u0639\\u0644\\u0649 \".concat(uniqueProducts.length, \" \\u0645\\u0646\\u062A\\u062C \\u0641\\u0639\\u0644\\u064A \\u0644\\u0644\\u0641\\u0626\\u0629 \\\"\").concat(category.name, \"\\\"\"));\n        this.products[category.id] = uniqueProducts;\n        this.renderProducts(category, uniqueProducts);\n      } else {\n        console.log(\"\\u0627\\u0633\\u062A\\u062E\\u062F\\u0627\\u0645 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \\u0627\\u0644\\u0627\\u0641\\u062A\\u0631\\u0627\\u0636\\u064A\\u0629 \\u0644\\u0644\\u0641\\u0626\\u0629 \\\"\".concat(category.name, \"\\\"\"));\n        var productsWithMessage = [{\n          isMessage: true,\n          name: \"\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \".concat(category.name),\n          message: 'منتجات عرض توضيحي'\n        }].concat(dummyProducts);\n        this.products[category.id] = productsWithMessage;\n        this.renderProducts(category, productsWithMessage);\n      }\n    }\n\n    /**\n     * استخراج المنتجات من الأقسام التي تحتوي على اسم الفئة\n     * @param {Object} category - الفئة المستهدفة\n     * @param {Array} results - مصفوفة النتائج\n     * @private\n     */\n  }, {\n    key: \"extractProductsFromSections\",\n    value: function extractProductsFromSections(category, results) {\n      var _this3 = this;\n      // البحث عن الأقسام التي قد تحتوي على اسم الفئة\n      var allSections = document.querySelectorAll('section, .section, .block, .row, .products-section, .products-block, .container, .content');\n      var categoryNameLower = category.name.toLowerCase();\n      var processedUrls = new Set(results.map(function (r) {\n        return r.url;\n      }));\n      allSections.forEach(function (section) {\n        try {\n          // البحث عن عنوان القسم\n          var sectionTitles = section.querySelectorAll('h1, h2, h3, h4, .title, .heading, .section-title');\n          var matchingSection = false;\n          sectionTitles.forEach(function (title) {\n            var titleText = title.textContent.toLowerCase();\n            if (titleText.includes(categoryNameLower)) {\n              matchingSection = true;\n            }\n          });\n          if (matchingSection) {\n            // وجدنا قسمًا يحتوي على اسم الفئة، استخراج المنتجات منه\n            var sectionProducts = section.querySelectorAll('.product-card, .product, [data-product-id], .s-product-card, .card, .item');\n            console.log(\"\\u0648\\u062C\\u062F\\u0646\\u0627 \\u0642\\u0633\\u0645\\u064B\\u0627 \\u064A\\u062D\\u062A\\u0648\\u064A \\u0639\\u0644\\u0649 \\u0627\\u0633\\u0645 \\u0627\\u0644\\u0641\\u0626\\u0629 \\\"\".concat(category.name, \"\\\" \\u0645\\u0639 \").concat(sectionProducts.length, \" \\u0645\\u0646\\u062A\\u062C\"));\n            sectionProducts.forEach(function (productElement) {\n              var productData = _this3.extractProductData(productElement);\n              if (productData && !processedUrls.has(productData.url)) {\n                processedUrls.add(productData.url);\n                productData.confidence = 90; // ثقة عالية\n                results.push(productData);\n              }\n            });\n          }\n        } catch (error) {\n          console.error('خطأ في تحليل القسم:', error);\n        }\n      });\n    }\n\n    /**\n     * يبحث عن منتجات البديلة من الروابط في الصفحة\n     * @param {Object} category - الفئة المستهدفة\n     * @param {Array} results - مصفوفة النتائج\n     * @private\n     */\n  }, {\n    key: \"_searchInAllLinks\",\n    value: function _searchInAllLinks(category, results) {\n      // البحث في جميع الروابط التي قد تكون منتجات\n      var allLinks = document.querySelectorAll('a[href]');\n      var categoryNameLower = category.name.toLowerCase();\n      var processedUrls = new Set(results.map(function (r) {\n        return r.url;\n      }));\n      allLinks.forEach(function (link) {\n        try {\n          // تخطي الروابط المعالجة بالفعل\n          if (processedUrls.has(link.href)) return;\n          var linkUrl = link.href.toLowerCase();\n          var linkText = link.textContent.trim();\n\n          // التحقق مما إذا كان الرابط يشير إلى منتج\n          var isProductLink = linkUrl.includes('/product/') || linkUrl.includes('/products/') || linkUrl.includes('product_id=');\n\n          // التحقق من مطابقة كلمات الفئة\n          var isRelatedToCategory = false;\n          if (linkText.toLowerCase().includes(categoryNameLower)) {\n            isRelatedToCategory = true;\n          } else {\n            // فحص كلمات الفئة في النص\n            var categoryWords = categoryNameLower.split(/\\s+/).filter(function (word) {\n              return word.length > 2;\n            });\n            var matchCount = 0;\n            categoryWords.forEach(function (word) {\n              if (linkText.toLowerCase().includes(word)) {\n                matchCount++;\n              }\n            });\n            if (matchCount > 0 && categoryWords.length > 0) {\n              var matchRatio = matchCount / categoryWords.length;\n              if (matchRatio >= 0.5) {\n                isRelatedToCategory = true;\n              }\n            }\n          }\n\n          // إذا كان الرابط منتجًا ومرتبطًا بالفئة\n          if (isProductLink && isRelatedToCategory) {\n            // الحصول على الصورة من النص أو الصورة داخل الرابط\n            var productImage = '';\n            var imgElement = link.querySelector('img');\n            if (imgElement) {\n              productImage = imgElement.src;\n            }\n\n            // إضافة المنتج إلى النتائج\n            processedUrls.add(link.href);\n            results.push({\n              name: linkText,\n              url: link.href,\n              image: productImage,\n              thumbnail: productImage,\n              confidence: 60,\n              related: true\n            });\n          }\n        } catch (error) {\n          console.error('خطأ في معالجة الرابط:', error);\n        }\n      });\n    }\n\n    /**\n     * استخدام أحدث المنتجات كبديل\n     * طريقة محسنة\n     * @param {Object} category - الفئة المستهدفة\n     * @param {Element} productsDiv - حاوية المنتجات\n     * @private\n     */\n  }, {\n    key: \"useLatestProductsAsFallback\",\n    value: function useLatestProductsAsFallback(category, productsDiv) {\n      var _this4 = this;\n      console.log('استخدام أحدث المنتجات كبديل لفئة:', category.name);\n\n      // قائمة البحث المحسنة\n      var selectors = ['.latest-products', '.newest-products', '.featured-products', '[data-section=\"latest-products\"]', '.products-slider', '.product-slider', '.swiper-container', '.products-grid', '.products-container', '[class*=\"products\"]'];\n\n      // محاولة أخيرة: البحث في روابط الصفحة\n      var results = [];\n      this._searchInAllLinks(category, results);\n      if (results.length > 0) {\n        console.log(\"\\u062A\\u0645 \\u0627\\u0644\\u0639\\u062B\\u0648\\u0631 \\u0639\\u0644\\u0649 \".concat(results.length, \" \\u0645\\u0646\\u062A\\u062C \\u0639\\u0646 \\u0637\\u0631\\u064A\\u0642 \\u0627\\u0644\\u0628\\u062D\\u062B \\u0641\\u064A \\u0627\\u0644\\u0631\\u0648\\u0627\\u0628\\u0637\"));\n        var productsWithMessage = [{\n          isMessage: true,\n          name: \"\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \\u0645\\u062A\\u0639\\u0644\\u0642\\u0629 \\u0628\\u0640 \".concat(category.name),\n          message: 'منتجات متعلقة بالفئة'\n        }].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(results.slice(0, 8)));\n        this.products[category.id] = productsWithMessage;\n        this.renderProducts(category, productsWithMessage);\n        return;\n      }\n      var latestProducts = [];\n\n      // جرب كل محدد\n      for (var _i = 0, _selectors = selectors; _i < _selectors.length; _i++) {\n        var selector = _selectors[_i];\n        var section = document.querySelector(selector);\n        if (section) {\n          var products = section.querySelectorAll('.product-card, .product, [data-product-id], .card, .item');\n          if (products.length > 0) {\n            console.log(\"\\u0648\\u062C\\u062F\\u0646\\u0627 \".concat(products.length, \" \\u0645\\u0646\\u062A\\u062C \\u0641\\u064A \\u0642\\u0633\\u0645 \\\"\").concat(selector, \"\\\"\"));\n            products.forEach(function (element) {\n              var productData = _this4.extractProductData(element);\n              if (productData) {\n                productData.isGeneric = true;\n                latestProducts.push(productData);\n              }\n            });\n            if (latestProducts.length > 0) {\n              break;\n            }\n          }\n        }\n      }\n\n      // إذا وجدنا منتجات\n      if (latestProducts.length > 0) {\n        var _productsWithMessage = [{\n          isMessage: true,\n          name: 'منتجات متنوعة',\n          message: 'لم يتم العثور على منتجات مخصصة لهذه الفئة، نعرض منتجات متنوعة بدلاً من ذلك'\n        }].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(latestProducts.slice(0, 8)));\n        this.products[category.id] = _productsWithMessage;\n        this.renderProducts(category, _productsWithMessage);\n      } else {\n        // إذا لم نجد أي منتجات\n        productsDiv.innerHTML = '<div class=\"p-4 text-center text-gray-500\">لا توجد منتجات في هذه الفئة</div>';\n      }\n    }\n\n    /**\n     * عرض منتجات الفئة بشكل محسن\n     * @param {Object} category - الفئة المستهدفة\n     * @param {Array} products - منتجات الفئة\n     */\n  }, {\n    key: \"renderProducts\",\n    value: function renderProducts(category, products) {\n      var _this5 = this;\n      console.log('عرض منتجات الفئة:', category.name, products.length);\n\n      // العثور على حاوية المنتجات\n      var productsDiv = this.querySelector(\".category-products[data-category-id=\\\"\".concat(category.id, \"\\\"]\"));\n      if (!productsDiv) return;\n\n      // مسح محتوى حاوية المنتجات\n      productsDiv.innerHTML = '';\n      if (!products || products.length === 0) {\n        productsDiv.innerHTML = '<div class=\"p-4 text-center text-gray-500\">لا توجد منتجات في هذه الفئة</div>';\n        return;\n      }\n\n      // تحديد عدد المنتجات التي سيتم عرضها (بحد أقصى 8 منتجات)\n      var maxDisplayProducts = 8;\n      var displayProducts = products.slice(0, maxDisplayProducts);\n      var hasMoreProducts = products.length > maxDisplayProducts;\n\n      // إضافة عنوان الفئة\n      var categoryHeader = document.createElement('div');\n      categoryHeader.className = 'category-products-header';\n      categoryHeader.innerHTML = \"\\n      <h3 class=\\\"category-products-title\\\">\".concat(category.name, \"</h3>\\n      <div class=\\\"category-products-subtitle\\\">\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \").concat(category.name, \"</div>\\n    \");\n      productsDiv.appendChild(categoryHeader);\n\n      // إنشاء شبكة المنتجات\n      var productsGrid = document.createElement('div');\n      productsGrid.className = 'products-grid';\n      productsDiv.appendChild(productsGrid);\n\n      // إنشاء بطاقات المنتجات\n      displayProducts.forEach(function (product) {\n        var _productCard$querySel;\n        // التحقق إذا كان هذا رسالة خاصة\n        if (product.isMessage) {\n          var messageDiv = document.createElement('div');\n          messageDiv.className = 'col-span-full p-2 mb-4 bg-gradient-to-r from-primary-50 to-primary-100 rounded-md text-center text-sm text-gray-700';\n          messageDiv.innerHTML = \"<strong>\".concat(product.name, \":</strong> \").concat(product.message);\n          productsGrid.appendChild(messageDiv);\n          return;\n        }\n        var productCard = document.createElement('a');\n        productCard.href = product.url || '#';\n        productCard.className = 'product-card hover:shadow-lg transition-all duration-300';\n        productCard.setAttribute('data-product-id', product.id || '');\n\n        // إضافة فئة إضافية للمنتجات غير المرتبطة\n        if (product.isGeneric) {\n          productCard.classList.add('generic-product');\n        }\n\n        // تحضير الصورة\n        var imageHtml = '';\n        var imageSrc = product.thumbnail || product.image || '';\n        if (imageSrc) {\n          imageHtml = \"<img src=\\\"\".concat(imageSrc, \"\\\" alt=\\\"\").concat(product.name, \"\\\" loading=\\\"lazy\\\" class=\\\"transition-transform duration-500\\\" />\");\n        } else {\n          imageHtml = \"<i class=\\\"sicon-box text-gray-400 text-2xl\\\"></i>\";\n        }\n\n        // تحضير السعر\n        var priceHtml = '';\n        if (product.sale_price && product.regular_price) {\n          // سعر مخفض\n          priceHtml = \"\\n          <div class=\\\"product-price\\\">\\n            <span class=\\\"text-primary-500 font-bold\\\">\".concat(product.sale_price, \"</span>\\n            <span class=\\\"line-through text-gray-400 mr-1 rtl:mr-0 rtl:ml-1 text-sm\\\">\").concat(product.regular_price, \"</span>\\n          </div>\\n        \");\n        } else if (product.price) {\n          // سعر عادي\n          priceHtml = \"<div class=\\\"product-price\\\">\".concat(product.price, \"</div>\");\n        }\n\n        // إضافة علامة العرض الترويجي إذا كان متاحًا\n        var promoHtml = '';\n        if (product.promotion) {\n          promoHtml = \"<div class=\\\"absolute top-2 right-2 rtl:right-auto rtl:left-2 bg-red-500 text-white text-xs py-1 px-2 rounded-full\\\">\\u0639\\u0631\\u0636</div>\";\n        }\n\n        // إضافة مؤشر الكمية المتوفرة\n        var stockHtml = '';\n        if (product.quantity !== undefined) {\n          if (product.quantity <= 0) {\n            stockHtml = \"<div class=\\\"absolute bottom-2 right-2 rtl:right-auto rtl:left-2 bg-gray-500 text-white text-xs py-1 px-2 rounded-full\\\">\\u0646\\u0641\\u0630\\u062A \\u0627\\u0644\\u0643\\u0645\\u064A\\u0629</div>\";\n          } else if (product.quantity < 5) {\n            stockHtml = \"<div class=\\\"absolute bottom-2 right-2 rtl:right-auto rtl:left-2 bg-yellow-500 text-white text-xs py-1 px-2 rounded-full\\\">\\u0643\\u0645\\u064A\\u0629 \\u0645\\u062D\\u062F\\u0648\\u062F\\u0629</div>\";\n          }\n        }\n\n        // زر إضافة للسلة (للتفاعل)\n        var addToCartButton = \"\\n        <button class=\\\"add-to-cart-btn\\\" title=\\\"\\u0623\\u0636\\u0641 \\u0625\\u0644\\u0649 \\u0627\\u0644\\u0633\\u0644\\u0629\\\">\\n          <i class=\\\"sicon-cart-add\\\"></i>\\n        </button>\\n      \";\n\n        // إنشاء قالب بطاقة المنتج\n        productCard.innerHTML = \"\\n        <div class=\\\"product-image relative\\\">\\n          \".concat(imageHtml, \"\\n          \").concat(promoHtml, \"\\n          \").concat(stockHtml, \"\\n          <div class=\\\"product-actions\\\">\\n            \").concat(addToCartButton, \"\\n          </div>\\n        </div>\\n        <div class=\\\"product-details\\\">\\n          <div class=\\\"product-name\\\">\").concat(product.name, \"</div>\\n          \").concat(priceHtml, \"\\n        </div>\\n      \");\n\n        // إضافة مستمع حدث النقر إذا كان المنتج بلا رابط\n        if (product.url === '#' || !product.url) {\n          productCard.addEventListener('click', function (e) {\n            e.preventDefault();\n            _this5.showProductDetails(product);\n          });\n        }\n\n        // إضافة مستمع أحداث لزر إضافة للسلة\n        (_productCard$querySel = productCard.querySelector('.add-to-cart-btn')) === null || _productCard$querySel === void 0 || _productCard$querySel.addEventListener('click', function (e) {\n          e.preventDefault();\n          e.stopPropagation();\n          _this5.handleAddToCart(product);\n        });\n        productsGrid.appendChild(productCard);\n      });\n\n      // إضافة زر \"عرض المزيد\" في حالة وجود المزيد من المنتجات\n      if (hasMoreProducts || products.length > 0) {\n        var viewMoreContainer = document.createElement('div');\n        viewMoreContainer.className = 'col-span-full mt-4 mb-2 flex justify-center';\n        var viewMoreBtn = document.createElement('a');\n        viewMoreBtn.href = category.url || \"\".concat(window.location.origin, \"/products?category=\").concat(category.id);\n        viewMoreBtn.className = 'view-more-btn';\n        viewMoreBtn.textContent = 'عرض كل منتجات الفئة';\n        viewMoreBtn.style.backgroundColor = 'rgb(var(--primary-color))';\n        viewMoreContainer.appendChild(viewMoreBtn);\n        productsDiv.appendChild(viewMoreContainer);\n      }\n    }\n\n    /**\n     * معالجة إضافة منتج للسلة\n     * @param {Object} product - المنتج المراد إضافته للسلة\n     */\n  }, {\n    key: \"handleAddToCart\",\n    value: function handleAddToCart(product) {\n      console.log('إضافة المنتج للسلة:', product.name);\n\n      // عرض رسالة تأكيد إضافة المنتج\n      var notification = document.createElement('div');\n      notification.className = 'cart-notification';\n      notification.innerHTML = \"\\n      <div class=\\\"cart-notification-content\\\">\\n        <i class=\\\"sicon-check-circle cart-notification-icon\\\"></i>\\n        <div class=\\\"cart-notification-text\\\">\\n          <p>\\u062A\\u0645\\u062A \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C \\u0644\\u0644\\u0633\\u0644\\u0629 \\u0628\\u0646\\u062C\\u0627\\u062D</p>\\n          <strong>\".concat(product.name, \"</strong>\\n        </div>\\n      </div>\\n    \");\n      document.body.appendChild(notification);\n\n      // عرض الإشعار لفترة ثم إزالته\n      setTimeout(function () {\n        notification.classList.add('show');\n        setTimeout(function () {\n          notification.classList.remove('show');\n          setTimeout(function () {\n            return document.body.removeChild(notification);\n          }, 300);\n        }, 2000);\n      }, 10);\n\n      // إذا كان SDK سلة متاح، محاولة إضافة المنتج للسلة\n      try {\n        if (window.salla && window.salla.cart) {\n          // الحصول على معرف المنتج من الرابط إذا كان ممكنًا\n          var productId = product.id;\n          if (!productId && product.url) {\n            // محاولة استخراج المعرف من الرابط\n            var match = product.url.match(/\\/products\\/(\\d+)/);\n            if (match && match[1]) {\n              productId = match[1];\n            }\n          }\n          if (productId) {\n            window.salla.cart.addItem(productId, 1).then(function () {\n              return console.log('تمت إضافة المنتج للسلة');\n            })[\"catch\"](function (error) {\n              return console.error('خطأ في إضافة المنتج للسلة:', error);\n            });\n          }\n        }\n      } catch (error) {\n        console.error('خطأ في محاولة إضافة المنتج للسلة:', error);\n      }\n    }\n  }]);\n}((0,_babel_runtime_helpers_wrapNativeSuper__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(HTMLElement)); // إضافة طرق الصنف باستخدام createClass helper\n(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(CategoryFilter, [{\n  key: \"loadCategories\",\n  value: function loadCategories() {\n    var _this6 = this;\n    console.log('جاري تحميل الفئات');\n\n    // إظهار رسالة التحميل\n    this.showLoading(this.filterContainer);\n    try {\n      // محاولة جلب الفئات باستخدام Twilight SDK\n      if (window.salla && window.salla.config && window.salla.config.get) {\n        var url = window.salla.url.get('categories');\n        console.log('الحصول على الفئات من:', url);\n        fetch(url).then(function (response) {\n          if (!response.ok) {\n            throw new Error('فشل في الحصول على الفئات');\n          }\n          return response.json();\n        }).then(function (result) {\n          console.log('نتائج الفئات:', result);\n          if (result && result.data && Array.isArray(result.data)) {\n            _this6.categories = result.data.filter(function (cat) {\n              return cat.status === 'active';\n            });\n            _this6.renderCategories();\n          } else {\n            // إذا لم نتمكن من جلب الفئات، نحاول البحث محليًا\n            _this6.findLocalCategories();\n          }\n        })[\"catch\"](function (error) {\n          console.error('خطأ في الحصول على الفئات:', error);\n          // في حالة الخطأ، نحاول البحث محليًا\n          _this6.findLocalCategories();\n        });\n      } else if (window.twilight && window.twilight.category) {\n        // الطريقة البديلة باستخدام واجهة تويلايت للفئات\n        window.twilight.category.categories().then(function (result) {\n          console.log('نتائج الفئات (twilight):', result);\n          if (result && result.data) {\n            _this6.categories = result.data.filter(function (cat) {\n              return cat.status === 'active';\n            });\n            _this6.renderCategories();\n          } else {\n            _this6.findLocalCategories();\n          }\n        })[\"catch\"](function (error) {\n          console.error('خطأ في جلب الفئات (twilight):', error);\n          _this6.findLocalCategories();\n        });\n      } else {\n        // إذا لم نتمكن من استخدام Twilight، نحاول البحث محليًا\n        this.findLocalCategories();\n      }\n    } catch (error) {\n      console.error('خطأ في جلب الفئات:', error);\n      this.findLocalCategories();\n    }\n  }\n}, {\n  key: \"findLocalCategories\",\n  value: function findLocalCategories() {\n    console.log('البحث عن الفئات في الصفحة');\n\n    // البحث عن الفئات في صفحة الموقع\n    var categoryElements = document.querySelectorAll('.category-entry, .category-card, [data-category-id], .nav-link, .categories-menu a, .main-menu a');\n    var categories = [];\n    categoryElements.forEach(function (element) {\n      var _element$querySelecto3;\n      var categoryName = element.textContent.trim();\n      var categoryLink = element.href || '#';\n      var categoryImage = ((_element$querySelecto3 = element.querySelector('img')) === null || _element$querySelecto3 === void 0 ? void 0 : _element$querySelecto3.src) || '';\n      var categoryId = element.dataset.categoryId || categories.length + 1;\n\n      // تجنب تكرار الفئات\n      if (!categories.some(function (cat) {\n        return cat.name === categoryName;\n      }) && categoryName.length > 0) {\n        categories.push({\n          id: categoryId,\n          name: categoryName,\n          url: categoryLink,\n          image: categoryImage\n        });\n      }\n    });\n    console.log('تم العثور على الفئات المحلية:', categories);\n    this.categories = categories;\n    this.renderCategories();\n  }\n}, {\n  key: \"renderCategories\",\n  value: function renderCategories() {\n    var _this7 = this;\n    // إزالة رسالة التحميل\n    this.filterContainer.innerHTML = '';\n    if (this.categories.length === 0) {\n      this.filterContainer.innerHTML = '<div class=\"text-gray-500\">لا توجد فئات متاحة</div>';\n      return;\n    }\n    console.log('عرض الفئات:', this.categories.length);\n\n    // إنشاء أيقونات الفئات\n    this.categories.forEach(function (category) {\n      var categoryIcon = document.createElement('div');\n      categoryIcon.className = 'category-icon';\n      categoryIcon.dataset.categoryId = category.id;\n      var imageHtml = '';\n      if (category.image) {\n        imageHtml = \"<img src=\\\"\".concat(category.image, \"\\\" alt=\\\"\").concat(category.name, \"\\\" />\");\n      } else {\n        imageHtml = \"<i class=\\\"sicon-folder text-gray-400 text-xl\\\"></i>\";\n      }\n      categoryIcon.innerHTML = \"\\n        <div class=\\\"category-icon-image\\\">\\n          \".concat(imageHtml, \"\\n        </div>\\n        <div class=\\\"category-icon-name\\\">\").concat(category.name, \"</div>\\n      \");\n\n      // إضافة مستمع حدث النقر\n      categoryIcon.addEventListener('click', function () {\n        return _this7.handleCategoryClick(category);\n      });\n      _this7.filterContainer.appendChild(categoryIcon);\n    });\n  }\n}, {\n  key: \"handleCategoryClick\",\n  value: function handleCategoryClick(category) {\n    console.log('تم النقر على الفئة:', category.name);\n\n    // تحديث الفئة النشطة\n    var isActive = this.activeCategory === category.id;\n\n    // إزالة الحالة النشطة من جميع الأيقونات\n    var allIcons = this.querySelectorAll('.category-icon');\n    allIcons.forEach(function (icon) {\n      return icon.classList.remove('active');\n    });\n\n    // إزالة جميع المنتجات النشطة\n    var allProducts = this.querySelectorAll('.category-products');\n    allProducts.forEach(function (products) {\n      products.classList.remove('active');\n      // إزالة العنصر بعد انتهاء التأثير\n      setTimeout(function () {\n        return products.remove();\n      }, 300);\n    });\n\n    // إذا تم النقر على نفس الفئة مرة أخرى، نخرج\n    if (isActive) {\n      this.activeCategory = null;\n      return;\n    }\n\n    // تحديث الفئة النشطة\n    this.activeCategory = category.id;\n\n    // تحديث الأيقونة النشطة\n    var activeIcon = this.querySelector(\".category-icon[data-category-id=\\\"\".concat(category.id, \"\\\"]\"));\n    if (activeIcon) {\n      activeIcon.classList.add('active');\n    }\n\n    // إنشاء حاوية المنتجات\n    var productsDiv = document.createElement('div');\n    productsDiv.className = 'category-products';\n    productsDiv.dataset.categoryId = category.id;\n    this.productsContainer.appendChild(productsDiv);\n\n    // إظهار رسالة التحميل\n    this.showLoading(productsDiv);\n\n    // تأخير قصير ثم عرض المنتجات\n    setTimeout(function () {\n      productsDiv.classList.add('active');\n    }, 10);\n\n    // التحقق مما إذا كانت لدينا منتجات مخزنة لهذه الفئة\n    if (this.products[category.id]) {\n      this.renderProducts(category, this.products[category.id]);\n    } else {\n      // جلب منتجات الفئة\n      this.loadCategoryProducts(category);\n    }\n  }\n}, {\n  key: \"loadCategoryProducts\",\n  value: function loadCategoryProducts(category) {\n    console.log('جاري تحميل منتجات الفئة:', category.name, 'ID:', category.id);\n\n    // إنشاء حاوية المنتجات\n    var productsDiv = document.createElement('div');\n    productsDiv.className = 'category-products';\n    productsDiv.dataset.categoryId = category.id;\n    this.productsContainer.appendChild(productsDiv);\n\n    // إظهار رسالة التحميل\n    this.showLoading(productsDiv);\n\n    // تأخير قصير ثم عرض المنتجات\n    setTimeout(function () {\n      productsDiv.classList.add('active');\n    }, 10);\n\n    // حل جديد: محاولة استخراج المنتجات من الصفحة الرئيسية مباشرة\n    this.extractProductsFromHomepage(category, productsDiv);\n  }\n}, {\n  key: \"extractProductData\",\n  value: function extractProductData(element) {\n    var name = this.extractProductName(element);\n    if (!name) return null;\n    var url = this.extractProductLink(element);\n    var image = this.extractProductImage(element);\n    var price = this.extractProductPrice(element);\n    return {\n      name: name,\n      url: url,\n      image: image,\n      thumbnail: image,\n      price: price,\n      related: true\n    };\n  }\n}, {\n  key: \"extractProductsFromSections\",\n  value: function extractProductsFromSections(category, results) {\n    var _this8 = this;\n    // البحث عن الأقسام التي قد تحتوي على اسم الفئة\n    var allSections = document.querySelectorAll('section, .section, .block, .row, .products-section, .products-block, .container, .content');\n    var categoryNameLower = category.name.toLowerCase();\n    var processedUrls = new Set(results.map(function (r) {\n      return r.url;\n    }));\n    allSections.forEach(function (section) {\n      try {\n        // البحث عن عنوان القسم\n        var sectionTitles = section.querySelectorAll('h1, h2, h3, h4, .title, .heading, .section-title');\n        var matchingSection = false;\n        sectionTitles.forEach(function (title) {\n          var titleText = title.textContent.toLowerCase();\n          if (titleText.includes(categoryNameLower)) {\n            matchingSection = true;\n          }\n        });\n        if (matchingSection) {\n          // وجدنا قسمًا يحتوي على اسم الفئة، استخراج المنتجات منه\n          var sectionProducts = section.querySelectorAll('.product-card, .product, [data-product-id], .s-product-card, .card, .item');\n          console.log(\"\\u0648\\u062C\\u062F\\u0646\\u0627 \\u0642\\u0633\\u0645\\u064B\\u0627 \\u064A\\u062D\\u062A\\u0648\\u064A \\u0639\\u0644\\u0649 \\u0627\\u0633\\u0645 \\u0627\\u0644\\u0641\\u0626\\u0629 \\\"\".concat(category.name, \"\\\" \\u0645\\u0639 \").concat(sectionProducts.length, \" \\u0645\\u0646\\u062A\\u062C\"));\n          sectionProducts.forEach(function (productElement) {\n            var productData = _this8.extractProductData(productElement);\n            if (productData && !processedUrls.has(productData.url)) {\n              processedUrls.add(productData.url);\n              productData.confidence = 90; // ثقة عالية\n              results.push(productData);\n            }\n          });\n        }\n      } catch (error) {\n        console.error('خطأ في تحليل القسم:', error);\n      }\n    });\n  }\n}, {\n  key: \"isProductRelatedToCategory\",\n  value: function isProductRelatedToCategory(element, product, categoryId, categoryName) {\n    // فحص السمات المباشرة\n    if (element.dataset.categoryId === categoryId || element.getAttribute('data-category') === categoryId || element.dataset.category === categoryId) {\n      return true;\n    }\n\n    // فحص عناصر الأب\n    var parentWithCategory = element.closest(\"[data-category-id=\\\"\".concat(categoryId, \"\\\"], [data-category=\\\"\").concat(categoryId, \"\\\"], [data-category-slug=\\\"\").concat(categoryName, \"\\\"]\"));\n    if (parentWithCategory) {\n      return true;\n    }\n\n    // فحص URL المنتج\n    if (product.url.includes(\"/category/\".concat(categoryId)) || product.url.includes(\"?category=\".concat(categoryId)) || product.url.includes(\"category_id=\".concat(categoryId)) || product.url.includes(\"/categories/\".concat(categoryId))) {\n      return true;\n    }\n\n    // فحص اسم المنتج مع اسم الفئة\n    var productNameLower = product.name.toLowerCase();\n    if (productNameLower.includes(categoryName)) {\n      return true;\n    }\n\n    // فحص كلمات الفئة في اسم المنتج\n    var categoryWords = categoryName.split(/\\s+/).filter(function (word) {\n      return word.length > 2;\n    });\n    var matchCount = 0;\n    categoryWords.forEach(function (word) {\n      if (productNameLower.includes(word)) {\n        matchCount++;\n      }\n    });\n    if (matchCount > 0 && categoryWords.length > 0) {\n      var matchRatio = matchCount / categoryWords.length;\n      if (matchRatio >= 0.5) {\n        return true;\n      }\n    }\n    return false;\n  }\n}, {\n  key: \"useLatestProductsAsFallback\",\n  value: function useLatestProductsAsFallback(category, productsDiv) {\n    var _this9 = this;\n    console.log('استخدام أحدث المنتجات كبديل لفئة:', category.name);\n\n    // قائمة البحث المحسنة\n    var selectors = ['.latest-products', '.newest-products', '.featured-products', '[data-section=\"latest-products\"]', '.products-slider', '.product-slider', '.swiper-container', '.products-grid', '.products-container', '[class*=\"products\"]'];\n\n    // محاولة أخيرة: البحث في روابط الصفحة\n    var results = [];\n    this._searchInAllLinks(category, results);\n    if (results.length > 0) {\n      console.log(\"\\u062A\\u0645 \\u0627\\u0644\\u0639\\u062B\\u0648\\u0631 \\u0639\\u0644\\u0649 \".concat(results.length, \" \\u0645\\u0646\\u062A\\u062C \\u0639\\u0646 \\u0637\\u0631\\u064A\\u0642 \\u0627\\u0644\\u0628\\u062D\\u062B \\u0641\\u064A \\u0627\\u0644\\u0631\\u0648\\u0627\\u0628\\u0637\"));\n      var productsWithMessage = [{\n        isMessage: true,\n        name: \"\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \\u0645\\u062A\\u0639\\u0644\\u0642\\u0629 \\u0628\\u0640 \".concat(category.name),\n        message: 'منتجات متعلقة بالفئة'\n      }].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(results.slice(0, 8)));\n      this.products[category.id] = productsWithMessage;\n      this.renderProducts(category, productsWithMessage);\n      return;\n    }\n    var latestProducts = [];\n\n    // جرب كل محدد\n    for (var _i2 = 0, _selectors2 = selectors; _i2 < _selectors2.length; _i2++) {\n      var selector = _selectors2[_i2];\n      var section = document.querySelector(selector);\n      if (section) {\n        var products = section.querySelectorAll('.product-card, .product, [data-product-id], .card, .item');\n        if (products.length > 0) {\n          console.log(\"\\u0648\\u062C\\u062F\\u0646\\u0627 \".concat(products.length, \" \\u0645\\u0646\\u062A\\u062C \\u0641\\u064A \\u0642\\u0633\\u0645 \\\"\").concat(selector, \"\\\"\"));\n          products.forEach(function (element) {\n            var productData = _this9.extractProductData(element);\n            if (productData) {\n              productData.isGeneric = true;\n              latestProducts.push(productData);\n            }\n          });\n          if (latestProducts.length > 0) {\n            break;\n          }\n        }\n      }\n    }\n\n    // إذا وجدنا منتجات\n    if (latestProducts.length > 0) {\n      var _productsWithMessage2 = [{\n        isMessage: true,\n        name: 'منتجات متنوعة',\n        message: 'لم يتم العثور على منتجات مخصصة لهذه الفئة، نعرض منتجات متنوعة بدلاً من ذلك'\n      }].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(latestProducts.slice(0, 8)));\n      this.products[category.id] = _productsWithMessage2;\n      this.renderProducts(category, _productsWithMessage2);\n    } else {\n      // إذا لم نجد أي منتجات\n      productsDiv.innerHTML = '<div class=\"p-4 text-center text-gray-500\">لا توجد منتجات في هذه الفئة</div>';\n    }\n  }\n}, {\n  key: \"removeDuplicateProducts\",\n  value: function removeDuplicateProducts(products) {\n    // إزالة المنتجات المكررة بناءً على URL أو الاسم\n    var uniqueUrls = new Set();\n    var uniqueNames = new Set();\n    var uniqueProducts = [];\n    products.forEach(function (product) {\n      // استخدام مزيج من URL واسم المنتج للتحقق من التكرار\n      var productUrl = product.url;\n      var productName = product.name;\n      if (!uniqueUrls.has(productUrl) && !uniqueNames.has(productName)) {\n        uniqueUrls.add(productUrl);\n        uniqueNames.add(productName);\n        uniqueProducts.push(product);\n      }\n    });\n    return uniqueProducts;\n  }\n}, {\n  key: \"extractProductName\",\n  value: function extractProductName(element) {\n    // تحسين استخراج اسم المنتج\n    var nameSelectors = ['.product-title', '.product-name', 'h3', 'h2', '.title', '.name', 'a[title]', '.s-product-card-title', '.card-title', '.product-info h4', '.product-info .title'];\n\n    // محاولة كل انتقاء\n    for (var _i3 = 0, _nameSelectors = nameSelectors; _i3 < _nameSelectors.length; _i3++) {\n      var selector = _nameSelectors[_i3];\n      var nameEl = element.querySelector(selector);\n      if (nameEl && nameEl.textContent.trim()) {\n        return nameEl.textContent.trim();\n      }\n    }\n\n    // محاولات إضافية\n    if (element.dataset.productTitle) {\n      return element.dataset.productTitle;\n    }\n    if (element.getAttribute('aria-label')) {\n      return element.getAttribute('aria-label');\n    }\n    if (element.querySelector('img') && element.querySelector('img').alt) {\n      return element.querySelector('img').alt;\n    }\n\n    // نحتفظ بهذا كملاذ أخير\n    var text = element.textContent.trim();\n    if (text && text.length < 100) {\n      // تجنب النصوص الطويلة جدًا\n      return text;\n    }\n    return '';\n  }\n}, {\n  key: \"extractProductLink\",\n  value: function extractProductLink(element) {\n    // تحسين استخراج رابط المنتج\n\n    // إذا كان العنصر نفسه رابطًا\n    if (element.tagName === 'A' && element.href) {\n      return element.href;\n    }\n\n    // البحث عن رابط داخل العنصر\n    var linkEl = element.querySelector('a[href]');\n    if (linkEl && linkEl.href) {\n      return linkEl.href;\n    }\n\n    // البحث عن الرابط في السمات\n    if (element.getAttribute('href')) {\n      return element.getAttribute('href');\n    }\n    if (element.dataset.href) {\n      return element.dataset.href;\n    }\n    if (element.dataset.link) {\n      return element.dataset.link;\n    }\n    if (element.dataset.url) {\n      return element.dataset.url;\n    }\n\n    // البحث عن عنصر يحتوي على سمة data-product-url\n    var productUrlEl = element.querySelector('[data-product-url]');\n    if (productUrlEl && productUrlEl.dataset.productUrl) {\n      return productUrlEl.dataset.productUrl;\n    }\n\n    // إذا كان لدينا معرف للمنتج، يمكننا تخمين الرابط\n    if (element.dataset.productId) {\n      return \"\".concat(window.location.origin, \"/products/\").concat(element.dataset.productId);\n    }\n\n    // إذا لم نجد رابطًا\n    return '#';\n  }\n}, {\n  key: \"extractProductImage\",\n  value: function extractProductImage(element) {\n    // تحسين استخراج صورة المنتج\n\n    // البحث عن صورة\n    var imgEl = element.querySelector('img');\n    if (imgEl) {\n      // تحقق من عدة سمات للصورة\n      return imgEl.src || imgEl.dataset.src || imgEl.getAttribute('data-src') || imgEl.getAttribute('data-lazy-src') || '';\n    }\n\n    // البحث عن عنصر بخلفية CSS\n    var elements = [element].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(Array.from(element.querySelectorAll('*'))));\n    var _iterator = _createForOfIteratorHelper(elements),\n      _step;\n    try {\n      for (_iterator.s(); !(_step = _iterator.n()).done;) {\n        var el = _step.value;\n        var style = window.getComputedStyle(el);\n        if (style.backgroundImage && style.backgroundImage !== 'none') {\n          var match = style.backgroundImage.match(/url\\(['\"]?(.+?)['\"]?\\)/);\n          if (match && match[1]) {\n            return match[1];\n          }\n        }\n      }\n\n      // البحث عن سمات أخرى\n    } catch (err) {\n      _iterator.e(err);\n    } finally {\n      _iterator.f();\n    }\n    if (element.dataset.image) {\n      return element.dataset.image;\n    }\n    var imageEl = element.querySelector('[data-image]');\n    if (imageEl && imageEl.dataset.image) {\n      return imageEl.dataset.image;\n    }\n    return '';\n  }\n}, {\n  key: \"extractProductPrice\",\n  value: function extractProductPrice(element) {\n    // تحسين استخراج سعر المنتج\n    var priceSelectors = ['.product-price', '.price', '.s-product-card-price', '.card-price', '.product-info .price', '[data-price]', '.amount', '.product-amount'];\n\n    // محاولة كل انتقاء\n    for (var _i4 = 0, _priceSelectors = priceSelectors; _i4 < _priceSelectors.length; _i4++) {\n      var selector = _priceSelectors[_i4];\n      var priceEl = element.querySelector(selector);\n      if (priceEl && priceEl.textContent.trim()) {\n        return priceEl.textContent.trim();\n      }\n    }\n\n    // محاولات إضافية\n    if (element.dataset.price) {\n      return element.dataset.price;\n    }\n    var priceDataEl = element.querySelector('[data-price]');\n    if (priceDataEl && priceDataEl.dataset.price) {\n      return priceDataEl.dataset.price;\n    }\n    return '';\n  }\n}, {\n  key: \"showLoading\",\n  value: function showLoading(container) {\n    container.innerHTML = \"\\n      <div class=\\\"loading-container\\\">\\n        <div class=\\\"loading-spinner\\\"></div>\\n      </div>\\n    \";\n  }\n}, {\n  key: \"showProductDetails\",\n  value: function showProductDetails(product) {\n    console.log('عرض تفاصيل المنتج:', product);\n\n    // إنشاء طبقة مظلمة\n    var overlay = document.createElement('div');\n    overlay.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center';\n\n    // إنشاء نافذة تفاصيل المنتج\n    var modal = document.createElement('div');\n    modal.className = 'bg-white rounded-lg p-4 max-w-md w-full max-h-90vh overflow-auto relative';\n\n    // زر الإغلاق\n    var closeButton = document.createElement('button');\n    closeButton.className = 'absolute top-2 right-2 rtl:right-auto rtl:left-2 text-gray-500 hover:text-gray-800';\n    closeButton.innerHTML = '<i class=\"sicon-cancel text-xl\"></i>';\n    closeButton.addEventListener('click', function () {\n      document.body.removeChild(overlay);\n    });\n\n    // صورة المنتج\n    var imageHtml = '';\n    if (product.image) {\n      imageHtml = \"<img src=\\\"\".concat(product.image, \"\\\" alt=\\\"\").concat(product.name, \"\\\" class=\\\"w-full h-auto rounded-md max-h-60 object-contain mx-auto\\\">\");\n    } else {\n      imageHtml = \"<div class=\\\"w-full h-48 bg-gray-200 rounded-md flex items-center justify-center\\\"><i class=\\\"sicon-box text-gray-400 text-4xl\\\"></i></div>\";\n    }\n\n    // سعر المنتج\n    var priceHtml = '';\n    if (product.sale_price && product.regular_price) {\n      priceHtml = \"\\n        <div class=\\\"mt-4 flex items-center\\\">\\n          <span class=\\\"text-primary-500 font-bold text-xl\\\">\".concat(product.sale_price, \"</span>\\n          <span class=\\\"line-through text-gray-400 mr-2 rtl:mr-0 rtl:ml-2\\\">\").concat(product.regular_price, \"</span>\\n        </div>\\n      \");\n    } else if (product.price) {\n      priceHtml = \"<div class=\\\"mt-4 text-primary-500 font-bold text-xl\\\">\".concat(product.price, \"</div>\");\n    }\n\n    // حالة المخزون\n    var stockHtml = '';\n    if (product.quantity !== undefined) {\n      if (product.quantity <= 0) {\n        stockHtml = \"<div class=\\\"mt-2 text-red-500\\\">\\u063A\\u064A\\u0631 \\u0645\\u062A\\u0648\\u0641\\u0631 \\u0641\\u064A \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646</div>\";\n      } else if (product.quantity < 5) {\n        stockHtml = \"<div class=\\\"mt-2 text-yellow-500\\\">\\u0645\\u062A\\u0628\\u0642\\u064A \".concat(product.quantity, \" \\u0642\\u0637\\u0639 \\u0641\\u0642\\u0637</div>\");\n      } else {\n        stockHtml = \"<div class=\\\"mt-2 text-green-500\\\">\\u0645\\u062A\\u0648\\u0641\\u0631 \\u0641\\u064A \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646</div>\";\n      }\n    }\n\n    // زر الانتقال للمنتج\n    var buttonHtml = '';\n    if (product.url && product.url !== '#') {\n      buttonHtml = \"\\n        <a href=\\\"\".concat(product.url, \"\\\" class=\\\"block w-full mt-4 py-2 px-4 bg-primary-500 hover:bg-primary-600 text-white text-center rounded-md transition-colors duration-300\\\">\\n          \\u0639\\u0631\\u0636 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C \\u0641\\u064A \\u0627\\u0644\\u0645\\u062A\\u062C\\u0631\\n        </a>\\n      \");\n    }\n\n    // HTML النافذة المنبثقة\n    modal.innerHTML = \"\\n      <div class=\\\"product-modal-content\\\">\\n        \".concat(imageHtml, \"\\n        <h3 class=\\\"text-xl font-bold mt-4\\\">\").concat(product.name, \"</h3>\\n        \").concat(priceHtml, \"\\n        \").concat(stockHtml, \"\\n        \").concat(buttonHtml, \"\\n      </div>\\n    \");\n    modal.appendChild(closeButton);\n    overlay.appendChild(modal);\n    document.body.appendChild(overlay);\n\n    // إضافة مستمع حدث النقر على الطبقة المظلمة لإغلاق النافذة\n    overlay.addEventListener('click', function (e) {\n      if (e.target === overlay) {\n        document.body.removeChild(overlay);\n      }\n    });\n\n    // إضافة مستمع حدث ضغط الزر Escape لإغلاق النافذة\n    var _escHandler = function escHandler(e) {\n      if (e.key === 'Escape') {\n        document.body.removeChild(overlay);\n        document.removeEventListener('keydown', _escHandler);\n      }\n    };\n    document.addEventListener('keydown', _escHandler);\n  }\n}]);\n\n// تسجيل المكون المخصص\ncustomElements.define('category-filter', CategoryFilter);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CategoryFilter);\n\n//# sourceURL=webpack://theme-raed/./src/assets/js/partials/category-filter.js?");

/***/ }),

/***/ "./src/assets/js/partials/category-search.js":
/*!***************************************************!*\
  !*** ./src/assets/js/partials/category-search.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\");\n/* harmony import */ var _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/classCallCheck */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/classCallCheck.js\");\n/* harmony import */ var _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/createClass */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/createClass.js\");\n/* harmony import */ var _babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/possibleConstructorReturn */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/possibleConstructorReturn.js\");\n/* harmony import */ var _babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/getPrototypeOf */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/getPrototypeOf.js\");\n/* harmony import */ var _babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/inherits */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/inherits.js\");\n/* harmony import */ var _babel_runtime_helpers_wrapNativeSuper__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @babel/runtime/helpers/wrapNativeSuper */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/wrapNativeSuper.js\");\n\n\n\n\n\n\nfunction _callSuper(t, o, e) { return o = (0,_babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(o), (0,_babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0,_babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\n\n\n\n\n\n\n\n/**\n * مكون بحث مخصص للمنتجات والفئات\n * يقوم بالبحث عن المنتجات والفئات باستخدام Salla Twilight SDK ويعرضها في قائمة منسدلة\n * تم تحسين الأداء وهيكلة الكود للتوافق مع نمط الألعاب (Gaming Theme)\n */\nvar CategorySearch = /*#__PURE__*/function (_wrapNativeSuper) {\n  function CategorySearch() {\n    var _this;\n    (0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(this, CategorySearch);\n    _this = _callSuper(this, CategorySearch);\n    (0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_this, CategorySearch);\n\n    // تعريف المتغيرات الأساسية\n    _this.searchTimeout = null;\n    _this.debounceDelay = 300; // تأخير البحث لتحسين الأداء\n    _this.isOpen = false;\n    _this.products = [];\n    _this.categories = [];\n    _this.showCategories = true;\n    _this.showProducts = true; // إضافة خاصية للتحكم في ظهور المنتجات\n    _this.isLoading = false;\n    _this.processedUrls = new Set(); // تتبع الروابط التي تمت معالجتها لمنع التكرار\n\n    // تأخير التهيئة حتى تحميل سلة\n    if (window.salla) {\n      _this.initComponent();\n    } else {\n      document.addEventListener('salla::ready', function () {\n        return _this.initComponent();\n      });\n    }\n    return _this;\n  }\n\n  /**\n   * تهيئة المكون\n   */\n  (0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(CategorySearch, _wrapNativeSuper);\n  return (0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(CategorySearch, [{\n    key: \"initComponent\",\n    value: function initComponent() {\n      // إنشاء هيكل العناصر\n      this._createElements();\n\n      // إضافة مستمعي الأحداث\n      this._addEventListeners();\n    }\n\n    /**\n     * إنشاء عناصر المكون\n     * @private\n     */\n  }, {\n    key: \"_createElements\",\n    value: function _createElements() {\n      // إضافة العناصر HTML\n      this.innerHTML = \"\\n      <div class=\\\"gaming-search-input-container\\\">\\n        <input \\n          type=\\\"text\\\" \\n          class=\\\"gaming-search-input\\\" \\n          placeholder=\\\"\\u0627\\u0628\\u062D\\u062B \\u0639\\u0646 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \\u0648\\u0627\\u0644\\u0623\\u0642\\u0633\\u0627\\u0645...\\\" \\n          aria-label=\\\"\\u0628\\u062D\\u062B\\\"\\n        />\\n        <span class=\\\"gaming-search-icon\\\">\\n          <i class=\\\"sicon-search\\\"></i>\\n        </span>\\n      </div>\\n      <div class=\\\"gaming-search-results\\\">\\n        <div class=\\\"gaming-search-results-content\\\"></div>\\n        <div class=\\\"gaming-search-loading\\\">\\n          <span class=\\\"gaming-search-loading-spinner\\\"></span>\\n          <span>\\u062C\\u0627\\u0631\\u064A \\u0627\\u0644\\u0628\\u062D\\u062B...</span>\\n        </div>\\n        <div class=\\\"gaming-search-no-results\\\">\\n          \\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0646\\u062A\\u0627\\u0626\\u062C\\n        </div>\\n      </div>\\n    \";\n\n      // الحصول على العناصر\n      this.searchInput = this.querySelector('.gaming-search-input');\n      this.searchResults = this.querySelector('.gaming-search-results');\n      this.searchResultsContent = this.querySelector('.gaming-search-results-content');\n      this.searchLoading = this.querySelector('.gaming-search-loading');\n      this.searchNoResults = this.querySelector('.gaming-search-no-results');\n\n      // إخفاء النتائج في البداية\n      this.closeResults();\n    }\n\n    /**\n     * إضافة مستمعي الأحداث\n     * @private\n     */\n  }, {\n    key: \"_addEventListeners\",\n    value: function _addEventListeners() {\n      // البحث عند الكتابة\n      this.searchInput.addEventListener('input', this._debounce(this.handleSearch.bind(this), this.debounceDelay));\n\n      // عند التركيز على حقل البحث\n      this.searchInput.addEventListener('focus', this.handleFocus.bind(this));\n\n      // إغلاق النتائج عند النقر خارج المكون\n      document.addEventListener('click', this.handleClickOutside.bind(this));\n\n      // دعم التنقل بالكيبورد للوصولية\n      this.searchInput.addEventListener('keydown', this.handleKeyboardNavigation.bind(this));\n    }\n\n    /**\n     * دالة للحد من عدد مرات تنفيذ الدالة خلال فترة زمنية\n     * @param {Function} func - الدالة المراد تنفيذها\n     * @param {number} delay - التأخير بالمللي ثانية\n     * @returns {Function} - دالة مع تأخير\n     * @private\n     */\n  }, {\n    key: \"_debounce\",\n    value: function _debounce(func, delay) {\n      return function () {\n        var context = this;\n        var args = arguments;\n        clearTimeout(this.searchTimeout);\n        this.searchTimeout = setTimeout(function () {\n          return func.apply(context, args);\n        }, delay);\n      };\n    }\n\n    /**\n     * معالجة حدث البحث\n     */\n  }, {\n    key: \"handleSearch\",\n    value: function handleSearch() {\n      var _this2 = this;\n      var query = this.searchInput.value.trim();\n\n      // مسح أي توقيت بحث سابق\n      clearTimeout(this.searchTimeout);\n\n      // إعادة تعيين الروابط المعالجة\n      this.processedUrls.clear();\n\n      // إعادة تعيين نتائج البحث السابقة\n      this.products = [];\n      this.categories = [];\n\n      // إذا كان البحث فارغًا، أغلق النتائج\n      if (!query) {\n        this.closeResults();\n        return;\n      }\n\n      // عرض النتائج\n      this.openResults();\n      this.showLoading();\n\n      // تأخير البحث لتجنب الطلبات المتكررة\n      this.searchTimeout = setTimeout(function () {\n        if (_this2.showProducts) {\n          _this2.searchProducts(query);\n        }\n        if (_this2.showCategories) {\n          _this2.searchCategories(query);\n        }\n      }, this.debounceDelay);\n    }\n\n    /**\n     * معالجة التنقل بالكيبورد\n     * @param {KeyboardEvent} event - حدث الكيبورد\n     */\n  }, {\n    key: \"handleKeyboardNavigation\",\n    value: function handleKeyboardNavigation(event) {\n      if (!this.isOpen) return;\n      var resultItems = this.searchResultsContent.querySelectorAll('.gaming-search-result-item');\n      if (!resultItems.length) return;\n      var focusedItem = this.searchResultsContent.querySelector('.gaming-search-result-item:focus');\n      var index = -1;\n      if (focusedItem) {\n        index = Array.from(resultItems).indexOf(focusedItem);\n      }\n\n      // التنقل لأسفل (السهم لأسفل أو Tab)\n      if (event.key === 'ArrowDown' || event.key === 'Tab' && !event.shiftKey) {\n        event.preventDefault();\n        index = (index + 1) % resultItems.length;\n        resultItems[index].focus();\n      }\n\n      // التنقل لأعلى (السهم لأعلى أو Shift+Tab)\n      else if (event.key === 'ArrowUp' || event.key === 'Tab' && event.shiftKey) {\n        event.preventDefault();\n        index = (index - 1 + resultItems.length) % resultItems.length;\n        resultItems[index].focus();\n      }\n\n      // إغلاق النتائج (Escape)\n      else if (event.key === 'Escape') {\n        this.closeResults();\n        this.searchInput.focus();\n      }\n    }\n\n    /**\n     * البحث عن المنتجات\n     * @param {string} query - نص البحث\n     */\n  }, {\n    key: \"searchProducts\",\n    value: function searchProducts(query) {\n      var _this3 = this;\n      this.isLoading = true;\n      this.showLoading();\n\n      // البحث المحلي أولاً\n      this.localSearchProducts(query);\n\n      // ثم محاولة البحث باستخدام SDK\n      try {\n        if (window.salla && window.salla.product) {\n          window.salla.product.fetch({\n            search: query,\n            limit: 10\n          }).then(function (response) {\n            if (response && response.data && Array.isArray(response.data) && response.data.length > 0) {\n              // إضافة درجة الصلة للمنتجات\n              var productsWithRelevance = response.data.map(function (product) {\n                product.relevance = _this3._calculateRelevance(product.name, query);\n                return product;\n              });\n\n              // تصفية المنتجات المكررة\n              var filteredProducts = productsWithRelevance.filter(function (product) {\n                return !_this3.processedUrls.has(product.url);\n              });\n\n              // إضافة الروابط المعالجة\n              filteredProducts.forEach(function (product) {\n                _this3.processedUrls.add(product.url);\n              });\n\n              // دمج النتائج\n              _this3.products = _this3._mergeResults(_this3.products, filteredProducts);\n\n              // تحديث العرض\n              _this3.renderResults();\n            }\n          })[\"catch\"](function () {\n            // عند حدوث خطأ، نعتمد على النتائج المحلية فقط\n            _this3.renderResults();\n          });\n        }\n      } catch (error) {\n        this.renderResults();\n      }\n    }\n\n    /**\n     * البحث عن الفئات\n     * @param {string} query - نص البحث\n     */\n  }, {\n    key: \"searchCategories\",\n    value: function searchCategories(query) {\n      var _this4 = this;\n      // البحث المحلي أولاً\n      this.localSearchCategories(query);\n      try {\n        if (window.salla && window.salla.category) {\n          window.salla.category.fetch({\n            search: query,\n            limit: 10\n          }).then(function (response) {\n            if (response && response.data && Array.isArray(response.data) && response.data.length > 0) {\n              // إضافة درجة الصلة للفئات\n              var categoriesWithRelevance = response.data.filter(function (cat) {\n                return cat.status === 'active';\n              }).map(function (category) {\n                category.relevance = _this4._calculateRelevance(category.name, query);\n                return category;\n              });\n\n              // تصفية الفئات المكررة\n              var filteredCategories = categoriesWithRelevance.filter(function (category) {\n                return !_this4.processedUrls.has(category.url);\n              });\n\n              // إضافة الروابط المعالجة\n              filteredCategories.forEach(function (category) {\n                _this4.processedUrls.add(category.url);\n              });\n\n              // دمج النتائج\n              _this4.categories = _this4._mergeResults(_this4.categories, filteredCategories);\n\n              // تحديث العرض\n              _this4.renderResults();\n            }\n          })[\"catch\"](function () {\n            // عند حدوث خطأ، نعتمد على النتائج المحلية فقط\n            _this4.renderResults();\n          });\n        }\n      } catch (error) {\n        this.renderResults();\n      }\n    }\n\n    /**\n     * حساب درجة صلة النص بالبحث\n     * @param {string} text - النص المراد فحصه\n     * @param {string} query - نص البحث\n     * @returns {number} - درجة الصلة من 0 إلى 100\n     * @private\n     */\n  }, {\n    key: \"_calculateRelevance\",\n    value: function _calculateRelevance(text, query) {\n      if (!text || !query) return 0;\n      var textLower = text.toLowerCase();\n      var queryLower = query.toLowerCase();\n      var relevanceScore = 0;\n\n      // تطابق النص الكامل\n      if (textLower.includes(queryLower)) {\n        relevanceScore += 60;\n\n        // وزن إضافي للكلمات التي تبدأ بالاستعلام\n        if (textLower.startsWith(queryLower)) {\n          relevanceScore += 30;\n        }\n      }\n\n      // تطابق الكلمات الفردية\n      var queryWords = queryLower.split(/\\s+/).filter(function (word) {\n        return word.length > 2;\n      });\n      var matchedWords = 0;\n      queryWords.forEach(function (word) {\n        if (textLower.includes(word)) {\n          matchedWords++;\n        }\n      });\n      if (matchedWords > 0 && queryWords.length > 0) {\n        relevanceScore += matchedWords / queryWords.length * 40;\n      }\n      return relevanceScore;\n    }\n\n    /**\n     * دمج نتائج البحث مع إزالة العناصر المتكررة\n     * @param {Array} existingResults - النتائج الحالية\n     * @param {Array} newResults - النتائج الجديدة\n     * @returns {Array} - النتائج المدمجة بدون تكرار\n     * @private\n     */\n  }, {\n    key: \"_mergeResults\",\n    value: function _mergeResults(existingResults, newResults) {\n      // إنشاء نسخة من النتائج الحالية\n      var mergedResults = (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(existingResults);\n\n      // إنشاء مصفوفة من الروابط الموجودة حاليا\n      var existingUrls = existingResults.map(function (item) {\n        return item.url;\n      });\n\n      // إضافة النتائج الجديدة التي لا توجد في النتائج الحالية\n      newResults.forEach(function (newItem) {\n        // إذا كان العنصر الجديد غير موجود في النتائج الحالية، أضفه\n        if (!existingUrls.includes(newItem.url) && newItem.name) {\n          mergedResults.push(newItem);\n        }\n      });\n\n      // ترتيب النتائج حسب درجة الصلة\n      return mergedResults.sort(function (a, b) {\n        return b.relevance - a.relevance;\n      });\n    }\n\n    /**\n     * البحث المحلي عن المنتجات\n     * @param {string} query - نص البحث\n     */\n  }, {\n    key: \"localSearchProducts\",\n    value: function localSearchProducts(query) {\n      // تحسين اختيار عناصر المنتجات لتغطية جميع أنواع المنتجات المحتملة\n      var productElements = document.querySelectorAll('.product-entry, .product-card, [data-product-id], .product, .product-block, [data-type=\"product\"], .card--product, .s-product-card, .product-item, [id*=\"product\"], [class*=\"product\"]');\n      var results = [];\n      query = query.toLowerCase();\n      var queryWords = query.split(/\\s+/).filter(function (word) {\n        return word.length > 2;\n      });\n\n      // ابحث أولاً في العناصر المتوفرة\n      this._searchInAvailableProducts(productElements, query, queryWords, results);\n\n      // إذا لم نجد منتجات كافية، ابحث عن أي روابط تحتوي على كلمة \"product\" أو \"منتج\"\n      if (results.length < 2) {\n        this._searchInAllLinks(query, queryWords, results);\n      }\n\n      // ترتيب النتائج حسب الصلة\n      results.sort(function (a, b) {\n        return b.relevance - a.relevance;\n      });\n      if (results.length > 0) {\n        this.products = results;\n        this.renderResults();\n      }\n    }\n\n    /**\n     * البحث في عناصر المنتجات المتوفرة في الصفحة\n     * @param {NodeList} productElements - عناصر المنتجات\n     * @param {string} query - نص البحث\n     * @param {Array} queryWords - كلمات البحث\n     * @param {Array} results - مصفوفة النتائج\n     * @private\n     */\n  }, {\n    key: \"_searchInAvailableProducts\",\n    value: function _searchInAvailableProducts(productElements, query, queryWords, results) {\n      var _this5 = this;\n      productElements.forEach(function (element) {\n        try {\n          var _element$querySelecto, _element$textContent, _element$querySelecto2;\n          // تحسين اختيار اسم المنتج\n          var productName = ((_element$querySelecto = element.querySelector('.product-title, .product-name, h3, a, .name, .title, [itemprop=\"name\"]')) === null || _element$querySelecto === void 0 || (_element$querySelecto = _element$querySelecto.textContent) === null || _element$querySelecto === void 0 ? void 0 : _element$querySelecto.trim()) || ((_element$textContent = element.textContent) === null || _element$textContent === void 0 ? void 0 : _element$textContent.trim()) || '';\n\n          // الحصول على الرابط بطرق مختلفة\n          var productLink = null;\n          var linkElement = element.querySelector('a[href]');\n          if (linkElement) {\n            productLink = linkElement.href;\n          } else if (element.tagName === 'A') {\n            productLink = element.href;\n          } else if (element.dataset.url) {\n            productLink = element.dataset.url;\n          } else if (element.dataset.href) {\n            productLink = element.dataset.href;\n          } else if (element.onclick) {\n            // محاولة استخراج الرابط من وظيفة النقر\n            var onClickStr = element.onclick.toString();\n            var urlMatch = onClickStr.match(/(window\\.location|location\\.href)\\s*=\\s*['\"]([^'\"]+)['\"]/);\n            if (urlMatch && urlMatch[2]) {\n              productLink = urlMatch[2];\n            }\n          }\n\n          // إذا لم نجد رابطًا، تخطي هذا العنصر\n          if (!productLink) return;\n\n          // تخطي المنتجات المكررة\n          if (_this5.processedUrls.has(productLink)) return;\n\n          // تحسين اختيار صورة المنتج\n          var productImage = '';\n          var imgElement = element.querySelector('img');\n          if (imgElement) {\n            productImage = imgElement.src;\n          } else {\n            // محاولة البحث عن الصورة في الأنماط الخلفية\n            var computedStyle = window.getComputedStyle(element);\n            var backgroundImage = computedStyle.backgroundImage;\n            if (backgroundImage && backgroundImage !== 'none') {\n              var _urlMatch = backgroundImage.match(/url\\(['\"]?([^'\"]+)['\"]?\\)/);\n              if (_urlMatch && _urlMatch[1]) {\n                productImage = _urlMatch[1];\n              }\n            }\n          }\n\n          // تحسين اختيار سعر المنتج\n          var productPrice = ((_element$querySelecto2 = element.querySelector('.product-price, .price, .s-price, [itemprop=\"price\"], .price-wrapper')) === null || _element$querySelecto2 === void 0 ? void 0 : _element$querySelecto2.textContent) || '';\n\n          // حساب درجة الصلة\n          var relevanceScore = _this5._calculateRelevance(productName, query);\n\n          // التحقق من تطابق المنتج مع البحث\n          if (relevanceScore === 0 && !productName.toLowerCase().includes(query.toLowerCase()) && !_this5._anyWordMatch(productName, queryWords)) {\n            // قبل التخطي، نتحقق من وجود مطابقة في الرابط نفسه\n            var linkMatch = productLink.toLowerCase().includes(query.toLowerCase()) || queryWords.some(function (word) {\n              return productLink.toLowerCase().includes(word);\n            });\n            if (!linkMatch) return;\n            // إذا كانت هناك مطابقة في الرابط، نعطي درجة صلة منخفضة\n            relevanceScore = 5;\n          }\n\n          // إضافة الرابط إلى المجموعة\n          _this5.processedUrls.add(productLink);\n\n          // تخزين النتيجة مع البيانات\n          results.push({\n            name: productName,\n            url: productLink,\n            image: productImage,\n            thumbnail: productImage,\n            price: productPrice,\n            relevance: relevanceScore > 0 ? relevanceScore : 10,\n            // ضمان وجود قيمة صلة موجبة\n            type: 'product' // إضافة النوع للتمييز\n          });\n        } catch (error) {\n          // تجاهل الأخطاء في عناصر فردية\n          console.error('Error processing product element:', error);\n        }\n      });\n    }\n\n    /**\n     * البحث في جميع الروابط في الصفحة\n     * @param {string} query - نص البحث\n     * @param {Array} queryWords - كلمات البحث\n     * @param {Array} results - مصفوفة النتائج\n     * @private\n     */\n  }, {\n    key: \"_searchInAllLinks\",\n    value: function _searchInAllLinks(query, queryWords, results) {\n      var _this6 = this;\n      // البحث في جميع الروابط التي قد تكون منتجات\n      var allLinks = document.querySelectorAll('a[href]');\n      allLinks.forEach(function (link) {\n        try {\n          // تخطي الروابط المعالجة بالفعل\n          if (_this6.processedUrls.has(link.href)) return;\n          var linkUrl = link.href.toLowerCase();\n          var linkText = link.textContent.trim();\n\n          // التحقق مما إذا كان الرابط يشير إلى منتج\n          var isProductLink = linkUrl.includes('/product/') || linkUrl.includes('/products/') || linkUrl.includes('product_id=') || linkUrl.includes('منتج') || linkUrl.includes('بطاقة');\n\n          // إذا كان الرابط ليس منتجًا، تخطيه إلا إذا كان النص يتطابق مع البحث\n          if (!isProductLink && !linkText.toLowerCase().includes(query) && !_this6._anyWordMatch(linkText, queryWords)) {\n            return;\n          }\n\n          // حساب درجة الصلة\n          var relevanceScore = _this6._calculateRelevance(linkText, query);\n\n          // الحصول على الصورة من النص أو الصورة داخل الرابط\n          var productImage = '';\n          var imgElement = link.querySelector('img');\n          if (imgElement) {\n            productImage = imgElement.src;\n          }\n\n          // إضافة الرابط إلى المجموعة\n          _this6.processedUrls.add(link.href);\n\n          // تخزين النتيجة\n          results.push({\n            name: linkText,\n            url: link.href,\n            image: productImage,\n            thumbnail: productImage,\n            relevance: relevanceScore > 0 ? relevanceScore : isProductLink ? 15 : 5,\n            type: 'product'\n          });\n        } catch (error) {\n          // تجاهل الأخطاء\n          console.error('Error processing link:', error);\n        }\n      });\n    }\n\n    /**\n     * التحقق من تطابق أي كلمة في النص مع الكلمات في البحث\n     * @param {string} text - النص المراد فحصه\n     * @param {Array} queryWords - كلمات البحث\n     * @returns {boolean} - يعيد true إذا كان هناك أي تطابق\n     * @private\n     */\n  }, {\n    key: \"_anyWordMatch\",\n    value: function _anyWordMatch(text, queryWords) {\n      if (!text || !queryWords || queryWords.length === 0) return false;\n      text = text.toLowerCase();\n      return queryWords.some(function (word) {\n        return text.includes(word);\n      });\n    }\n\n    /**\n     * البحث المحلي عن الفئات\n     * @param {string} query - نص البحث\n     */\n  }, {\n    key: \"localSearchCategories\",\n    value: function localSearchCategories(query) {\n      var _this7 = this;\n      var categoryElements = document.querySelectorAll('.category-entry, .category-card, [data-category-id], .nav-link, .categories-menu a, .main-menu a');\n      var results = [];\n      query = query.toLowerCase();\n      var queryWords = query.split(/\\s+/).filter(function (word) {\n        return word.length > 2;\n      });\n      categoryElements.forEach(function (element) {\n        try {\n          var _element$querySelecto3;\n          var categoryName = element.textContent.trim();\n          var categoryLink = element.href || '#';\n\n          // تخطي الفئات المكررة\n          if (_this7.processedUrls.has(categoryLink)) return;\n          var categoryImage = ((_element$querySelecto3 = element.querySelector('img')) === null || _element$querySelecto3 === void 0 ? void 0 : _element$querySelecto3.src) || '';\n\n          // حساب درجة الصلة\n          var relevanceScore = _this7._calculateRelevance(categoryName, query);\n\n          // إذا كانت النتيجة تساوي صفر، نتخطى هذه الفئة\n          if (relevanceScore === 0) {\n            return;\n          }\n\n          // إضافة الرابط إلى المجموعة\n          _this7.processedUrls.add(categoryLink);\n\n          // تخزين النتيجة مع البيانات\n          results.push({\n            name: categoryName,\n            url: categoryLink,\n            image: categoryImage,\n            thumbnail: categoryImage,\n            relevance: relevanceScore,\n            type: 'category' // إضافة النوع للتمييز\n          });\n        } catch (error) {\n          // تجاهل الأخطاء في عناصر فردية\n        }\n      });\n\n      // ترتيب النتائج حسب الصلة\n      results.sort(function (a, b) {\n        return b.relevance - a.relevance;\n      });\n      if (results.length > 0) {\n        this.categories = results;\n        this.renderResults();\n      }\n    }\n\n    /**\n     * عرض نتائج البحث\n     */\n  }, {\n    key: \"renderResults\",\n    value: function renderResults() {\n      this.hideLoading();\n      var hasProducts = this.products.length > 0;\n      var hasCategories = this.categories.length > 0 && this.showCategories;\n\n      // إذا لم تكن هناك نتائج، أظهر رسالة عدم وجود نتائج\n      if (!hasProducts && !hasCategories) {\n        this.showNoResults();\n        return;\n      }\n\n      // إخفاء رسالة عدم وجود نتائج\n      this.hideNoResults();\n\n      // إنشاء عناصر النتائج\n      this.searchResultsContent.innerHTML = '';\n\n      // عرض الفئات أولاً\n      if (hasCategories) {\n        this._renderCategoriesSection();\n      }\n\n      // عرض المنتجات\n      if (hasProducts) {\n        this._renderProductsSection();\n      }\n    }\n\n    /**\n     * عرض قسم الفئات\n     * @private\n     */\n  }, {\n    key: \"_renderCategoriesSection\",\n    value: function _renderCategoriesSection() {\n      var categoriesHeader = document.createElement('h3');\n      categoriesHeader.className = 'gaming-search-section-title';\n      categoriesHeader.textContent = 'الأقسام';\n      this.searchResultsContent.appendChild(categoriesHeader);\n\n      // تحديد الحد الأقصى للفئات المعروضة\n      var maxCategories = Math.min(this.categories.length, 5);\n      for (var i = 0; i < maxCategories; i++) {\n        var category = this.categories[i];\n        var resultItem = this._createResultItem(category, 'category');\n        this.searchResultsContent.appendChild(resultItem);\n      }\n    }\n\n    /**\n     * عرض قسم المنتجات\n     * @private\n     */\n  }, {\n    key: \"_renderProductsSection\",\n    value: function _renderProductsSection() {\n      var productsHeader = document.createElement('h3');\n      productsHeader.className = 'gaming-search-section-title';\n      productsHeader.textContent = 'المنتجات';\n      this.searchResultsContent.appendChild(productsHeader);\n\n      // تحديد الحد الأقصى للمنتجات المعروضة\n      var maxProducts = Math.min(this.products.length, 5);\n      for (var i = 0; i < maxProducts; i++) {\n        var product = this.products[i];\n        var resultItem = this._createResultItem(product, 'product');\n        this.searchResultsContent.appendChild(resultItem);\n      }\n    }\n\n    /**\n     * إنشاء عنصر نتيجة\n     * @param {Object} item - عنصر النتيجة (منتج أو فئة)\n     * @param {string} type - نوع العنصر ('product' أو 'category')\n     * @returns {HTMLElement} - عنصر HTML\n     * @private\n     */\n  }, {\n    key: \"_createResultItem\",\n    value: function _createResultItem(item, type) {\n      var resultItem = document.createElement('a');\n\n      // التأكد من أن الرابط صالح\n      var url = item.url || '#';\n\n      // إضافة اسم المجال إذا كان الرابط نسبيًا ولا يبدأ بـ \"/\"\n      if (url.startsWith('/')) {\n        // إذا كان الرابط نسبيًا يبدأ بـ \"/\" أضف المجال\n        url = window.location.origin + url;\n      } else if (!url.startsWith('http') && !url.startsWith('#')) {\n        // إذا كان الرابط لا يبدأ بـ \"http\" أو \"#\"، أضف \"/\" والمجال\n        url = window.location.origin + '/' + url;\n      }\n      resultItem.href = url;\n      resultItem.className = 'gaming-search-result-item';\n      resultItem.setAttribute('tabindex', '0');\n      resultItem.setAttribute('role', 'option');\n\n      // إضافة onclick لجعل النقر يعمل بشكل مؤكد\n      resultItem.onclick = function (e) {\n        if (url !== '#') {\n          // حفظ الرابط في التاريخ\n          window.location.href = url;\n        }\n      };\n\n      // إضافة مؤشر الصلة بالبحث\n      if (item.relevance && item.relevance > 90) {\n        resultItem.classList.add('high-relevance');\n      }\n\n      // صورة العنصر\n      var imageHtml = '';\n      if (item.thumbnail || item.image) {\n        var imageSrc = item.thumbnail || item.image;\n        imageHtml = \"<img src=\\\"\".concat(imageSrc, \"\\\" alt=\\\"\").concat(item.name, \"\\\" class=\\\"gaming-search-result-image\\\" loading=\\\"lazy\\\" onerror=\\\"this.src='data:image/svg+xml;utf8,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2240%22 height=%2240%22 viewBox=%220 0 24 24%22><path fill=%22%231DE9B6%22 d=%22M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm4-8c0 2.21-1.79 4-4 4s-4-1.79-4-4 1.79-4 4-4 4 1.79 4 4z%22/></svg>'\\\"/>\");\n      } else {\n        var iconClass = type === 'product' ? 'sicon-box' : 'sicon-folder';\n        imageHtml = \"<div class=\\\"gaming-search-result-icon\\\">\\n        <i class=\\\"\".concat(iconClass, \"\\\"></i>\\n      </div>\");\n      }\n\n      // سعر المنتج (للمنتجات فقط)\n      var priceHtml = '';\n      if (type === 'product' && (item.sale_price || item.price)) {\n        var priceText = item.price || '';\n        var saleText = '';\n        if (item.sale_price && item.regular_price) {\n          priceText = item.sale_price;\n          saleText = item.regular_price;\n        }\n        priceHtml = \"\\n        <div class=\\\"gaming-search-result-price\\\">\\n          <span class=\\\"gaming-search-result-current-price\\\">\".concat(priceText, \"</span>\\n          \").concat(saleText ? \"<span class=\\\"gaming-search-result-old-price\\\">\".concat(saleText, \"</span>\") : '', \"\\n        </div>\\n      \");\n      }\n      resultItem.innerHTML = \"\\n      <div class=\\\"gaming-search-result-wrapper\\\">\\n        \".concat(imageHtml, \"\\n        <div class=\\\"gaming-search-result-content\\\">\\n          <div class=\\\"gaming-search-result-name\\\">\").concat(item.name, \"</div>\\n          \").concat(priceHtml, \"\\n        </div>\\n      </div>\\n    \");\n      return resultItem;\n    }\n\n    /**\n     * فتح نتائج البحث\n     */\n  }, {\n    key: \"openResults\",\n    value: function openResults() {\n      if (!this.isOpen) {\n        this.searchResults.classList.add('gaming-search-results-open');\n        this.isOpen = true;\n      }\n    }\n\n    /**\n     * إغلاق نتائج البحث\n     */\n  }, {\n    key: \"closeResults\",\n    value: function closeResults() {\n      if (this.isOpen) {\n        this.searchResults.classList.remove('gaming-search-results-open');\n        this.isOpen = false;\n      }\n    }\n\n    /**\n     * معالجة النقر خارج المكون\n     * @param {Event} event - حدث النقر\n     */\n  }, {\n    key: \"handleClickOutside\",\n    value: function handleClickOutside(event) {\n      if (!this.contains(event.target)) {\n        this.closeResults();\n      }\n    }\n\n    /**\n     * معالجة التركيز على حقل البحث\n     */\n  }, {\n    key: \"handleFocus\",\n    value: function handleFocus() {\n      var query = this.searchInput.value.trim();\n      if (query) {\n        if (this.products.length || this.categories.length) {\n          this.openResults();\n        } else {\n          this.searchProducts(query);\n          if (this.showCategories) {\n            this.searchCategories(query);\n          }\n        }\n      }\n    }\n\n    /**\n     * إظهار مؤشر التحميل\n     */\n  }, {\n    key: \"showLoading\",\n    value: function showLoading() {\n      this.searchLoading.classList.add('gaming-search-loading-visible');\n      this.searchNoResults.classList.remove('gaming-search-no-results-visible');\n    }\n\n    /**\n     * إخفاء مؤشر التحميل\n     */\n  }, {\n    key: \"hideLoading\",\n    value: function hideLoading() {\n      this.searchLoading.classList.remove('gaming-search-loading-visible');\n      this.isLoading = false;\n    }\n\n    /**\n     * إظهار رسالة عدم وجود نتائج\n     */\n  }, {\n    key: \"showNoResults\",\n    value: function showNoResults() {\n      this.searchNoResults.classList.add('gaming-search-no-results-visible');\n    }\n\n    /**\n     * إخفاء رسالة عدم وجود نتائج\n     */\n  }, {\n    key: \"hideNoResults\",\n    value: function hideNoResults() {\n      this.searchNoResults.classList.remove('gaming-search-no-results-visible');\n    }\n  }]);\n}((0,_babel_runtime_helpers_wrapNativeSuper__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(HTMLElement)); // تسجيل المكون المخصص\ncustomElements.define('category-search', CategorySearch);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CategorySearch);\n\n//# sourceURL=webpack://theme-raed/./src/assets/js/partials/category-search.js?");

/***/ }),

/***/ "./src/assets/js/partials/tooltip.js":
/*!*******************************************!*\
  !*** ./src/assets/js/partials/tooltip.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ toolTip)\n/* harmony export */ });\nfunction toolTip() {\n  var tooltipToggleClick = document.querySelectorAll('.tooltip-toggle--clickable'),\n    tooltipToggleHover = document.querySelectorAll('.tooltip-toggle--hover'),\n    closeTooltip = document.querySelectorAll('.close-tooltip');\n  var isTouchDevice = window.matchMedia(\"(pointer: coarse)\").matches;\n  var showTooltip = function showTooltip(element) {\n    element.classList.add('visible');\n  };\n  var hideTooltip = function hideTooltip(element) {\n    element.classList.remove('visible');\n  };\n\n  // Show the tooltip if the type is clickable\n  if (tooltipToggleClick.length) {\n    tooltipToggleClick.forEach(function (element) {\n      element.addEventListener('click', function (e) {\n        e.stopPropagation();\n        showTooltip(element);\n      });\n    });\n  }\n\n  // Show the tooltip if the type is hover or click on touch devices\n  if (tooltipToggleHover.length) {\n    tooltipToggleHover.forEach(function (element) {\n      if (isTouchDevice) {\n        element.addEventListener('click', function (e) {\n          e.stopPropagation();\n          showTooltip(element);\n        });\n      } else {\n        element.addEventListener('mouseenter', function () {\n          showTooltip(element);\n        });\n        element.addEventListener('mouseleave', function () {\n          hideTooltip(element);\n        });\n      }\n    });\n  }\n\n  // Hide the tooltip when the close button is clicked\n  if (closeTooltip.length) {\n    closeTooltip.forEach(function (element) {\n      element.addEventListener('click', function (e) {\n        e.stopPropagation();\n        hideTooltip(element.parentElement.parentElement);\n      });\n    });\n  }\n\n  // Hide the tooltip on window click\n  window.addEventListener('click', function () {\n    tooltipToggleClick.forEach(function (element) {\n      hideTooltip(element);\n    });\n    tooltipToggleHover.forEach(function (element) {\n      hideTooltip(element);\n    });\n  });\n}\n\n//# sourceURL=webpack://theme-raed/./src/assets/js/partials/tooltip.js?");

/***/ }),

/***/ "./src/assets/js/wishlist.js":
/*!***********************************!*\
  !*** ./src/assets/js/wishlist.js ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/classCallCheck */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/classCallCheck.js\");\n/* harmony import */ var _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/createClass */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/createClass.js\");\n/* harmony import */ var _babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/possibleConstructorReturn */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/possibleConstructorReturn.js\");\n/* harmony import */ var _babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/getPrototypeOf */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/getPrototypeOf.js\");\n/* harmony import */ var _babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/inherits */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/inherits.js\");\n/* harmony import */ var _base_page__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./base-page */ \"./src/assets/js/base-page.js\");\n\n\n\n\n\nfunction _callSuper(t, o, e) { return o = (0,_babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(o), (0,_babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0,_babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\n\nvar Wishlist = /*#__PURE__*/function (_BasePage) {\n  function Wishlist() {\n    (0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this, Wishlist);\n    return _callSuper(this, Wishlist, arguments);\n  }\n  (0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(Wishlist, _BasePage);\n  return (0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(Wishlist, [{\n    key: \"onReady\",\n    value: function onReady() {\n      var _this = this;\n      // init wishlist icons in product cards\n      salla.storage.get('salla::wishlist', []).forEach(function (id) {\n        return _this.toggleFavoriteIcon(id);\n      });\n    }\n  }, {\n    key: \"registerEvents\",\n    value: function registerEvents() {\n      var _this2 = this;\n      salla.wishlist.event.onAdded(function (event, id) {\n        return _this2.toggleFavoriteIcon(id);\n      });\n      salla.wishlist.event.onRemoved(function (response, id) {\n        _this2.toggleFavoriteIcon(id, false);\n\n        // just an animation when the item removed from wishlist page\n        var item = document.querySelector('#wishlist-product-' + id);\n        if (!item) {\n          return;\n        }\n        app.anime(item, false).height(0) // -> from 'height' to '0',\n        .opacity(0).easing('easeInOutQuad').duration(300).complete(function () {\n          return item.remove() || document.querySelector('#wishlist>*') || window.location.reload();\n        }).play();\n      });\n    }\n  }, {\n    key: \"toggleFavoriteIcon\",\n    value: function toggleFavoriteIcon(id) {\n      var isAdded = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n      document.querySelectorAll('.btn--wishlist[data-id=\"' + id + '\"]').forEach(function (btn) {\n        app.toggleElementClassIf(btn, 'is-added', 'not-added', function () {\n          return isAdded;\n        });\n        // app.toggleElementClassIf(btn, 'pulse', 'un-favorited', () => isAdded);\n      });\n    }\n  }]);\n}(_base_page__WEBPACK_IMPORTED_MODULE_5__[\"default\"]);\nWishlist.initiateWhenReady();\n\n//# sourceURL=webpack://theme-raed/./src/assets/js/wishlist.js?");

/***/ }),

/***/ "./src/assets/styles/app.scss":
/*!************************************!*\
  !*** ./src/assets/styles/app.scss ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n// extracted by mini-css-extract-plugin\n\n\n//# sourceURL=webpack://theme-raed/./src/assets/styles/app.scss?");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval devtool is used.
/******/ 	__webpack_require__("./src/assets/styles/app.scss");
/******/ 	__webpack_require__("./src/assets/js/wishlist.js");
/******/ 	__webpack_require__("./src/assets/js/app.js");
/******/ 	var __webpack_exports__ = __webpack_require__("./src/assets/js/blog.js");
/******/ 	
/******/ })()
;